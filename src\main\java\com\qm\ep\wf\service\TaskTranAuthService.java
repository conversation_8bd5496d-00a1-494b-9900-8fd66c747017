package com.qm.ep.wf.service;

import com.qm.ep.wf.domain.bean.TaskTranAuthDO;
import com.qm.ep.wf.domain.bean.TransCodeDO;
import com.qm.ep.wf.domain.dto.TaskTranAuthDTO;
import com.qm.ep.wf.domain.vo.RoleVO;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.service.IQmBaseService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-19
 */
public interface TaskTranAuthService extends IQmBaseService<TaskTranAuthDO> {

    /**
     * 该方法弃用，存在严重性能问题，建议使用下 createRedisData(String processDefKey,Map<String, List<UserRoleCodeVO>> roleMap)
     *
     * @param processDefKey
     * @deprecated（存在严重性能问题，建议使用下 createRedisData(String processDefKey, Map < String, List < UserRoleCodeVO > > roleMap)）
     */
    @SuppressWarnings("squid:S1133")
    @Deprecated
    void createRedisData(String processDefKey);

    /**
     * 创建Redis缓存数据
     *
     * @param processDefKey
     * @param roleMap       sysFeignRemote.getPersonCodeByRoleCode(BootAppUtil.getLoginKey().getTenantId(), "").getData();
     */
    void createRedisData(String processDefKey, Map<String, List<RoleVO>> roleMap);

    JsonResultVo<QmPage<TransCodeDO>> searchTable(TaskTranAuthDTO tempDTO);

    boolean judgeTaskAuth(String taskName, String transCode, String processDefKey, String buttonCode);


    List<TaskTranAuthDO> selectTaskAuthByParams(String taskName, String transCode, String processDefKey, String buttonCode);

    /**
     * @description 查询流程定义列表
     * <AUTHOR>
     * @date 2020/9/2 13:12
     */
    List procDefList(List list);

    /**
     * @description 查询事务授权数据
     * <AUTHOR>
     * @date 2020/9/4 10:35
     */
    public JsonResultVo<QmPage<TaskTranAuthDO>> searchTransAuth(TaskTranAuthDTO tempDTO);

    /**
     * @description 批量修改保存数据，并同步redis
     * <AUTHOR>
     * @date 2020/9/22 15:03
     */
    boolean saveOrUpdateBatchAndRedis(List<TaskTranAuthDO> list);

    /**
     * @description 删除事务授权并附带redis操作
     * <AUTHOR>
     * @date 2020/9/23 14:33
     */
    boolean removeByMapAndRedis(Map map);
}
