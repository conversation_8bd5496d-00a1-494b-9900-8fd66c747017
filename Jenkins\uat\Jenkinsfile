#!/usr/bin/env groovy
properties(
        [parameters(
                [string(defaultValue: "deploy_qm02_uat", description: "测试云docker用户名", name: "DOCKER_USER"),
                 string(defaultValue: "Lxl12345", description: "测试云docker密码", name: "DOCKER_PASSWORD"),
                 string(defaultValue: "************:31104/tsf_100000013/uat-common-service-wf", description: "测试云镜像地址", name: "REGISTRY_URL"),
                 string(defaultValue: "common-service-wf", description: "服务名称", name: "APP_NAME"),
                 gitParameter(branch: "",
                         branchFilter: "origin/(.*)",
                         defaultValue: "sit",
                         description: "",
                         name: "<PERSON><PERSON><PERSON>",
                         quickFilterEnabled: false,
                         selectedValue: "NONE",
                         sortMode: "NONE",
                         tagFilter: "*",
                         type: "PT_BRANCH")
                ]
          )
        ])

node("master") {

        stage("checkout") {
            echo "======检出代码分支名称======:" + "${params.BRANCH}"
            git branch: "${params.BRANCH}", credentialsId: 'c76172c2-95f4-4911-be62-ad176ce08a1c', url: 'http://*************/spring/common-service-wf.git'
    		//判断代码是否有变更
            def changeString = getChangeString()          
          }
        stage("build") {
                    echo "======进行代码构建======:"
                    withMaven(jdk: "JAVA_HOME", maven: "MAVEN_HOME") {
                    sh 'mvn -U clean install -Dmaven.test.skip=true'
                     }
                }
        stage("merge") {
        echo "======合并sit到uat分支======:"
        sh '''
         git push http://*************/spring/common-service-wf.git sit:uat
         
         ''' 
         //git push https://yldevsvm.faw.com/ep_hq/common-wf.git sit:uat
    }
       // stage("docker-login") {
           // echo "======进行镜像仓库登录======:"
          //  sh "docker login -u ${params.DOCKER_USER} -p ${params.DOCKER_PASSWORD} ${params.REGISTRY_URL}"
       // }

      //  dateVersion = "${params.BRANCH}" + "-" + sh(script: "date \"+%Y%m%d%H%M%S\"", returnStdout: true).trim()
     //   echo "dateVersion = ${dateVersion}"

      //  stage("docker-build") {
        //    echo "======进行镜像仓库构建======:"
            //sh "cp target/*.jar ../${APP_NAME}"

         //   echo "======进行镜像======:"
        //    def image = docker.build("${params.REGISTRY_URL}:${APP_NAME}-${dateVersion}-v${BUILD_ID}")

          //  echo "======进行推送======:"
           // echo "${image}"
           // image.push()
            
           // echo "======删除本地镜像======:"
         //   sh "docker rmi -f ${params.REGISTRY_URL}:${APP_NAME}-${dateVersion}-v${BUILD_ID}"
        
       // }


}

@NonCPS
def getChangeString() {
    MAX_MSG_LEN = 100
    def changeString = ""
    echo "Gathering SCM changes"
    def changeLogSets = currentBuild.changeSets
    for (int i = 0; i < changeLogSets.size(); i++) {
        def entries = changeLogSets[i].items
        for (int j = 0; j < entries.length; j++) {
            def entry = entries[j]
            truncated_msg = entry.msg.take(MAX_MSG_LEN)
            changeString += " - [${entry.commitId}] [${entry.author}] ${truncated_msg} \n"
        }
    }
    if (!changeString) {
        //changeString = "No new changes"
        currentBuild.result = 'ABORTED'
        error '无代码更新,流水线暂停'
    }
    return changeString
}
