package com.qm.ep.wf.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.qm.ep.wf.domain.bean.WorkFlowLogDO;
import com.qm.ep.wf.domain.dto.WorkFlowLogDTO;
import com.qm.ep.wf.domain.vo.WorkFlowLogVO;
import com.qm.tds.api.mp.mapper.QmBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 导出Excel日志 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-25
 */
public interface WorkFlowLogMapper extends QmBaseMapper<WorkFlowLogDO> {
    IPage<WorkFlowLogVO> selectListWithVO(IPage<WorkFlowLogVO> queryPage, @Param(Constants.WRAPPER) QueryWrapper<WorkFlowLogDO> queryWrapper, @Param("params") WorkFlowLogDTO tempDTO);

    List<WorkFlowLogDO> queryData(String sqlfy);
}
