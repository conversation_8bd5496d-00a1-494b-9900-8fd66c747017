<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qm.ep.wf.mapper.ProcessDefinitionMapper">

    <resultMap id="processDefinitionResultMap" type="org.flowable.engine.impl.persistence.entity.ProcessDefinitionEntityImpl">
        <id property="id" column="ID_" jdbcType="VARCHAR" />
        <result property="revision" column="REV_" />
        <result property="category" column="CATEGORY_" />
        <result property="name" column="NAME_" />
        <result property="key" column="KEY_" jdbcType="VARCHAR" />
        <result property="version" column="VERSION_" jdbcType="INTEGER"/>
        <result property="deploymentId" column="DEPLOYMENT_ID_" jdbcType="VARCHAR"/>
        <result property="resourceName" column="RESOURCE_NAME_" jdbcType="VARCHAR"/>
        <result property="tenantId" column="TENANT_ID_" jdbcType="VARCHAR" />
        <result property="diagramResourceName" column="DGRM_RESOURCE_NAME_" jdbcType="VARCHAR"/>
        <result property="description" column="DESCRIPTION_" jdbcType="VARCHAR" />
        <result property="hasStartFormKey" column="HAS_START_FORM_KEY_" jdbcType="BOOLEAN"/>
        <result property="isGraphicalNotationDefined" column="HAS_GRAPHICAL_NOTATION_" jdbcType="BOOLEAN" />
        <result property="suspensionState" column="SUSPENSION_STATE_" jdbcType="INTEGER"/>
        <result property="derivedFrom" column="DERIVED_FROM_" jdbcType="VARCHAR" />
        <result property="derivedFromRoot" column="DERIVED_FROM_ROOT_" jdbcType="VARCHAR" />
        <result property="derivedVersion" column="DERIVED_VERSION_" jdbcType="INTEGER" />
        <result property="engineVersion" column="ENGINE_VERSION_" jdbcType="VARCHAR" />
    </resultMap>


    <select id="selectLatestProcessByParams" resultMap="processDefinitionResultMap">
        SELECT
            t1.*
        FROM
        ACT_RE_PROCDEF AS t1
        LEFT JOIN wf_conf AS t2 ON t1.KEY_ = t2.VMODEL
        WHERE
        1 = 1

        <if  test="dto.name !=null and dto.name !=''">
            <bind name="nameKeyword" value="'%' + dto.name + '%'" />
            AND t1.NAME_  like #{nameKeyword}
        </if>
        <if  test="dto.key !=null and dto.key !=''">
            <bind name="keyword" value="'%' + dto.key + '%'" />
            AND t1.KEY_ like  #{keyword}
        </if>
        <if  test="dto.vmodule !=null and dto.vmodule !=''">
            <bind name="moduleKeyword" value="'%' + dto.vmodule + '%'" />
            AND t2.vmodule like  #{moduleKeyword}
        </if>
        <if test="dto.authorization !=null">
            AND t2.AUTHORIZATION  = #{dto.authorization}
        </if>
        AND t1.VERSION_ = (
            SELECT
            max( VERSION_ )
            FROM
            ACT_RE_PROCDEF
            WHERE
            KEY_ = t1.KEY_
            AND ( ( TENANT_ID_ IS NOT NULL AND TENANT_ID_ = t1.TENANT_ID_ ) OR ( TENANT_ID_ IS NULL AND t1.TENANT_ID_ IS NULL ) )
        )
    </select>
</mapper>