#!/usr/bin/env groovy
properties(
        [parameters(
                [string(defaultValue: "deploy_qm02", description: "测试云docker用户名", name: "DOCKER_USER"),
                 string(defaultValue: "Dms_5906928", description: "测试云docker密码", name: "DOCKER_PASSWORD"),
                 string(defaultValue: "************:31104/tsf_100000149/prod-common-service-wf", description: "测试云镜像地址", name: "REGISTRY_URL"),
                 string(defaultValue: "common-service-wf", description: "服务名称", name: "APP_NAME"),
                 gitParameter(branch: "",
                         branchFilter: "origin/(.*)",
                         defaultValue: "master",
                         description: "",
                         name: "<PERSON><PERSON><PERSON>",
                         quickFilterEnabled: false,
                         selectedValue: "NONE",
                         sortMode: "NONE",
                         tagFilter: "*",
                         type: "PT_BRANCH")
                ]
          )
        ])

node("master") {
        withEnv(["PATH+EXTRA=/usr/sbin:/usr/bin:/sbin:/bin:/opt/apache-maven-3.5.4/bin"]) {
        stage("checkout") {
            echo "======检出代码分支名称======:" + "${params.BRANCH}"
            git branch: "${params.BRANCH}", credentialsId: 'a61c427d-80d8-4f23-8a31-fb5969ba09f9', url: 'http://*************/spring/common-service-wf.git'
          }
        stage("build") {
            echo "======进行代码构建======:"
            withMaven(jdk: "JDK  8u181", maven: "Maven 3.5.4") {
            sh 'mvn -U clean install -Dmaven.test.skip=true'
            }
        }
        stage("merge") {
        echo "======合并uat到prod分支======:"
        sh '''
         git push  https://zhangchun_qm:<EMAIL>/bt-ep/common-wf.git master:dev
         ''' 
        }
       
    }

}
