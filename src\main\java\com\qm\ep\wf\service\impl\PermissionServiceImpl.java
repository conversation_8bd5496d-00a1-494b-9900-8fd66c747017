package com.qm.ep.wf.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.qm.ep.wf.common.cmd.GetProcessDefinitionInfoCmd;
import com.qm.ep.wf.common.exception.FlowableNoPermissionException;
import com.qm.ep.wf.constant.FlowableConstant;
import com.qm.ep.wf.service.PermissionService;
import org.apache.commons.lang3.StringUtils;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.bpmn.model.ExtensionElement;
import org.flowable.bpmn.model.FlowElement;
import org.flowable.bpmn.model.UserTask;
import org.flowable.common.engine.api.FlowableException;
import org.flowable.common.engine.api.FlowableObjectNotFoundException;
import org.flowable.engine.*;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.history.HistoricProcessInstanceQuery;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.identitylink.api.IdentityLink;
import org.flowable.idm.api.Group;
import org.flowable.task.api.DelegationState;
import org.flowable.task.api.Task;
import org.flowable.task.api.TaskInfo;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.flowable.task.api.history.HistoricTaskInstanceQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> @date
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class PermissionServiceImpl implements PermissionService {
    @Autowired
    private IdentityService identityService;
    @Autowired
    protected TaskService taskService;
    @Autowired
    protected RuntimeService runtimeService;
    @Autowired
    protected RepositoryService repositoryService;
    @Autowired
    protected HistoryService historyService;
    @Autowired
    protected ManagementService managementService;

    /**
     * Check if the given user is allowed to read the task.
     */
    @Override
    public HistoricTaskInstance validateReadPermissionOnTask(String taskId, String userId,
                                                             boolean validateReadProcessInstance, boolean validateReadParentTask) {
        if (isAdmin(userId)) {
            return historyService.createHistoricTaskInstanceQuery().taskId(taskId).singleResult();
        }
        HistoricTaskInstanceQuery taskQuery = historyService.createHistoricTaskInstanceQuery().taskId(taskId);
        List<String> groupIds = getGroupIdsForUser(userId);
        if (!groupIds.isEmpty()) {
            taskQuery.or().taskInvolvedUser(userId).taskCandidateGroupIn(groupIds).endOr();
        } else {
            taskQuery.taskInvolvedUser(userId);
        }
        HistoricTaskInstance task = taskQuery.singleResult();
        if (null != task) {
            return task;
        }
        // Last resort: user has access to process instance or parent task -> can see task
        task = historyService.createHistoricTaskInstanceQuery().taskId(taskId).singleResult();
        if (null != task) {
            return task;
        }
        throw new FlowableNoPermissionException(FlowableConstant.NO_PERMISSION);
    }

    @Override
    public Task validateReadPermissionOnTask2(String taskId, String userId, boolean validateReadProcessInstance,
                                              boolean validateReadParentTask) {
        if (isAdmin(userId)) {
            return taskService.createTaskQuery().taskId(taskId).singleResult();
        }

        Task task = taskService.createTaskQuery().taskId(taskId).or().taskCandidateOrAssigned(userId).taskOwner(userId)
                .endOr().singleResult();
        if (null != task) {
            return task;
        }

        // Last resort: user has access to process instance or parent task -> can see task
        task = taskService.createTaskQuery().taskId(taskId).singleResult();
        return task;
    }

    private List<String> getGroupIdsForUser(String userId) {
        List<String> groupIds = new ArrayList<>();

        List<Group> userGroups = identityService.createGroupQuery().groupMember(userId).list();
        for (Group group : userGroups) {
            groupIds.add(String.valueOf(group.getId()));
        }
        return groupIds;
    }

    @Override
    public boolean isTaskOwnerOrAssignee(String currentUser, Task task) {
        return currentUser.equals(task.getOwner()) || currentUser.equals(task.getAssignee());
    }

    @Override
    public boolean isTaskOwnerOrAssignee(String currentUser, String taskId) {
        return isTaskOwnerOrAssignee(currentUser, taskService.createTaskQuery().taskId(taskId).singleResult());
    }

    @Override
    public boolean validateIfUserIsInitiatorAndCanCompleteTask(String userId, TaskInfo task) {
        boolean canCompleteTask = false;
        if (null != task.getProcessInstanceId()) {
            HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                    .processInstanceId(task.getProcessInstanceId()).singleResult();
            if (null != historicProcessInstance && StringUtils.isNotEmpty(historicProcessInstance.getStartUserId())) {
                String processInstanceStartUserId = historicProcessInstance.getStartUserId();
                if (userId.equals(processInstanceStartUserId) && validateIfInitiatorCanCompleteTask(task)) {
                    return true;
                }
            }

        }
        return canCompleteTask;
    }

    @Override
    public boolean validateIfInitiatorCanCompleteTask(TaskInfo task) {
        boolean canCompleteTask = false;
        BpmnModel bpmnModel = repositoryService.getBpmnModel(task.getProcessDefinitionId());
        FlowElement flowElement = bpmnModel.getFlowElement(task.getTaskDefinitionKey());
        if (flowElement instanceof UserTask) {
            UserTask userTask = (UserTask) flowElement;
            List<ExtensionElement> extensionElements = userTask.getExtensionElements().get("initiator-can-complete");
            if (CollectionUtils.isNotEmpty(extensionElements)) {
                String value = extensionElements.get(0).getElementText();
                if (StringUtils.isNotEmpty(value) && Boolean.parseBoolean(value)) {
                    canCompleteTask = true;
                }
            }
        }
        return canCompleteTask;
    }

    @Override
    public boolean isInvolved(String userId, String taskId) {
        return historyService.createHistoricTaskInstanceQuery().taskId(taskId).taskInvolvedUser(userId).count() == 1;
    }

    @Override
    public HistoricProcessInstance validateReadPermissionOnProcessInstance(String userId, String processInstanceId) {
        HistoricProcessInstance historicProcessInstance;
        historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                .processInstanceId(processInstanceId).singleResult();
        return historicProcessInstance;
    }

    /**
     * Check if the given user is allowed to read the process instance.
     */
    @Override
    public boolean hasReadPermissionOnProcessInstance(String userId, String processInstanceId) {
        HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                .processInstanceId(processInstanceId).singleResult();
        return hasReadPermissionOnProcessInstance(userId, historicProcessInstance, processInstanceId);
    }

    /**
     * Check if the given user is allowed to read the process instance.
     */
    @SuppressWarnings({"squid:S1192"})
    @Override
    public boolean hasReadPermissionOnProcessInstance(String userId, HistoricProcessInstance historicProcessInstance,
                                                      String processInstanceId) {
        if (historicProcessInstance == null) {
            throw new FlowableObjectNotFoundException(
                    "ProcessInstance with id: " + processInstanceId + " does not exist");
        }

        if (isAdmin(userId)) {
            return true;
        }

        // Start user check
        if (null != historicProcessInstance.getStartUserId()
                && historicProcessInstance.getStartUserId().equals(userId)) {
            return true;
        }

        // check if the user is involved in the task
        HistoricProcessInstanceQuery historicProcessInstanceQuery = historyService.createHistoricProcessInstanceQuery();
        historicProcessInstanceQuery.processInstanceId(processInstanceId);
        historicProcessInstanceQuery.involvedUser(userId);
        if (historicProcessInstanceQuery.count() > 0) {
            return true;
        }

        // Visibility: check if there are any tasks for the current user
        HistoricTaskInstanceQuery historicTaskInstanceQuery = historyService.createHistoricTaskInstanceQuery();
        historicTaskInstanceQuery.processInstanceId(processInstanceId);
        historicTaskInstanceQuery.taskInvolvedUser(userId);
        if (historicTaskInstanceQuery.count() > 0) {
            return true;
        }

        List<String> groupIds = getGroupIdsForUser(userId);
        if (!groupIds.isEmpty()) {
            historicTaskInstanceQuery = historyService.createHistoricTaskInstanceQuery();
            historicTaskInstanceQuery.processInstanceId(processInstanceId).taskCandidateGroupIn(groupIds);
            return historicTaskInstanceQuery.count() > 0;
        }

        return false;
    }

    @Override
    public ProcessDefinition validateReadPermissionOnProcessDefinition(String userId, String processDefinitionId,
                                                                       String processDefinitionKey, String tenantId) {
        ProcessDefinition definition = managementService
                .executeCommand(new GetProcessDefinitionInfoCmd(processDefinitionId, processDefinitionKey, tenantId));
        validateReadPermissionOnProcessDefinition(userId, definition.getId());
        return definition;
    }

    @Override
    public void validateReadPermissionOnProcessDefinition(String userId, String processDefinitionId) {
        if (isAdmin(userId)) {
            return;
        }
        List<IdentityLink> identityLinks = repositoryService.getIdentityLinksForProcessDefinition(processDefinitionId);
        List<String> startUserIds = getPotentialStarterUserIds(identityLinks);
        List<String> startGroupIds = getPotentialStarterGroupIds(identityLinks);
        if (startUserIds.isEmpty() && startGroupIds.isEmpty()) {
            return;
        }
        if (startUserIds.contains(userId)) {
            return;
        }
        List<String> groupsIds = getGroupIdsForUser(userId);
        for (String startGroupId : startGroupIds) {
            if (groupsIds.contains(startGroupId)) {
                return;
            }
        }
    }

    @Override
    public List<String> getPotentialStarterGroupIds(List<IdentityLink> identityLinks) {
        List<String> groupIds = new ArrayList<>();
        for (IdentityLink identityLink : identityLinks) {
            if (null != identityLink.getGroupId() && identityLink.getGroupId().length() > 0 && !groupIds.contains(identityLink.getGroupId())) {
                groupIds.add(identityLink.getGroupId());
            }
        }
        return groupIds;
    }

    @Override
    public List<String> getPotentialStarterUserIds(List<IdentityLink> identityLinks) {
        List<String> userIds = new ArrayList<>();
        for (IdentityLink identityLink : identityLinks) {
            if (null != identityLink.getUserId() && identityLink.getUserId().length() > 0 && !userIds.contains(identityLink.getUserId())) {
                userIds.add(identityLink.getUserId());
            }
        }
        return userIds;
    }

    @Override
    public boolean isTaskPending(Task task) {
        return DelegationState.PENDING.equals(task.getDelegationState());
    }

    /**
     * 是否可以转办任务
     * <p>
     * 1.任务所有人可以转办
     * <p>
     * 2.任务执行人可以转办，但要求任务非委派状态
     * <p>
     * 3.被转办人不能是当前任务执行人
     *
     * @param taskId
     * @param userId
     * @param assignee
     * @return
     */
    @SuppressWarnings({"squid:S1192"})
    @Override
    public Task validateAssignPermissionOnTask(String taskId, String userId, String assignee) {
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
        if (task == null) {
            throw new FlowableObjectNotFoundException("Task with id: " + taskId + " does not exist");
        }
        String owner = task.getOwner();
        String oldAssignee = task.getAssignee();
        boolean canAssignFlag = isAdmin(userId) || userId.equals(owner)
                || (userId.equals(oldAssignee) && !isTaskPending(task));
        if (canAssignFlag) {
            if (assignee == null || assignee.length() == 0) {
                throw new FlowableException("Assignee cannot be empty");
            } else if (assignee.equals(oldAssignee)) {
                throw new FlowableException("The assignee is already " + assignee);
            }
            return task;
        }
        throw new FlowableNoPermissionException("User does not have permission");
    }

    /**
     * 是否可以委派任务
     * <p>
     * 1.任务所有人可以委派
     * <p>
     * 2.任务执行人可以委派
     * <p>
     * 3.被委派人不能是任务所有人和当前任务执行人
     *
     * @param taskId
     * @param userId
     * @param delegater
     * @return
     */
    @SuppressWarnings({"squid:S1192"})
    @Override
    public Task validateDelegatePermissionOnTask(String taskId, String userId, String delegater) {
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
        if (task == null) {
            throw new FlowableObjectNotFoundException("Task with id: " + taskId + " does not exist");
        }
        if (isAdmin(userId) || isTaskOwnerOrAssignee(userId, task)) {
            String owner = task.getOwner();
            String oldAssignee = task.getAssignee();
            if (delegater == null || delegater.length() == 0) {
                throw new FlowableException("Assignee cannot be empty");
            } else if (delegater.equals(owner)) {
                throw new FlowableException("Cannot delegate to owner");
            } else if (delegater.equals(oldAssignee)) {
                throw new FlowableException("The executor is already " + delegater);
            }
            return task;
        }
        throw new FlowableNoPermissionException("User does not have permission");
    }


    @SuppressWarnings({"squid:S1192"})
    @Override
    public Task validateExcutePermissionOnTask(String taskId, String userId) {
        //暂时去掉认领权限验证 20200622赵微
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
        if (task == null) {
            throw new FlowableObjectNotFoundException("Task with id: " + taskId + " does not exist");
        }
        return task;
    }

    @SuppressWarnings({"squid:S1192"})
    @Override
    public ProcessInstance validateStopProcessInstancePermissionOnTask(String taskId, String userId) {
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
        if (task == null) {
            throw new FlowableObjectNotFoundException("Task with id: " + taskId + " does not exist");
        }
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                .processInstanceId(task.getProcessInstanceId()).singleResult();
        if (processInstance == null) {
            throw new FlowableObjectNotFoundException(
                    "ProcessInstance with id: " + task.getProcessInstanceId() + " does not exist");
        }
        return processInstance;
    }

    @Override
    public boolean isAdmin(String userId) {
        return "admin".equals(userId);
    }
}
