package com.qm.ep.wf.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@SuppressWarnings("squid:S00116")
@ApiModel(value = "UserRoleCodeVO对象", description = "")
@Data
public class UserRoleCodeVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "角色ID")
    private String ID;

    @ApiModelProperty(value = "用户code")
    private String VPERSONCODE;

    @ApiModelProperty(value = "角色code")
    private String VROLECODE;
}
