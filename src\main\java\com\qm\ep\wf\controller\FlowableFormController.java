package com.qm.ep.wf.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qm.ep.wf.domain.entity.FlowableForm;
import com.qm.ep.wf.service.FlowableFormService;
import com.qm.ep.wf.util.EscapeUnescape;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.util.I18nUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;

/**
 * 流程表单Controller
 *
 * <AUTHOR>
@RestController
@RequestMapping("/flowable/form")
@Api(value = "表单管理", tags = {"form"})
public class FlowableFormController {
    @Autowired
    private FlowableFormService flowableFormService;
    @Autowired
    private I18nUtil i18nUtil;

    private static final String FORM_KEY = "form_key";

    /**
     * 自定义查询列表
     *
     * @param flowableForm
     * @param currentPage
     * @param pageSize
     * @return
     */
    @ApiOperation(value = "获取表单列表", httpMethod = "GET", notes = "[author:10027705]")
    @GetMapping(value = "/list")
    public JsonResultVo list(FlowableForm flowableForm, @RequestParam Integer currentPage, @RequestParam Integer pageSize) {
        JsonResultVo resultObj = new JsonResultVo();
        IPage<FlowableForm> pageList = flowableFormService.list(new Page<>(currentPage, pageSize), flowableForm);
        resultObj.setData(pageList);
        return resultObj;
    }

    @ApiOperation(value = "获取指定表单", httpMethod = "GET", notes = "[author:10027705]")
    @GetMapping(value = "/queryById")
    public JsonResultVo queryById(@RequestParam String id) {
        JsonResultVo resultObj = new JsonResultVo();
        QueryWrapper<FlowableForm> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(FORM_KEY, id);
        FlowableForm flowableForm = flowableFormService.getOne(queryWrapper);
        if (null != flowableForm.getFormJson())
            flowableForm.setFormJson(EscapeUnescape.escape(flowableForm.getFormJson()));
        resultObj.setData(flowableForm);
        return resultObj;
    }

    /**
     * @param flowableForm
     * @return
     * @功能：新增
     */
    @ApiOperation(value = "新增流程表单", httpMethod = "POST", notes = "[author:10027705]")
    @PostMapping(value = "/save")
    public JsonResultVo save(@Valid @RequestBody FlowableForm flowableForm) {
        JsonResultVo resultObj = new JsonResultVo();
        String formKey = flowableForm.getFormKey();
        QueryWrapper<FlowableForm> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(FORM_KEY, formKey);
        boolean flag = true;
        if (flowableFormService.getOne(queryWrapper) != null) {
            if (null == flowableForm.getId()) {
                String message = i18nUtil.getMessage("ERR.wf.flowableForm.save", formKey);
                resultObj.setMsgErr(message);
                return resultObj;
            } else {
                flowableForm.setFormJson(null);
                flag = flowableFormService.updateById(flowableForm);
            }
        } else {
            flag = flowableFormService.saveOrUpdate(flowableForm);
        }
        if (flag) {
            String message = i18nUtil.getMessage("MSG.wf.common.saveSuccess");
            resultObj.setMsg(message);
        } else {
            String message = i18nUtil.getMessage("ERR.wf.common.saveFail");
            resultObj.setMsgErr(message);
        }
        return resultObj;
    }

    /**
     * @param flowableForm
     * @retur
     * @功能：修改
     */
    @ApiOperation(value = "修改流程表单",  httpMethod = "POST", notes = "[author:10027705]")
    @PostMapping(value = "/update")
    public JsonResultVo update(@Valid @RequestBody FlowableForm flowableForm) {
        JsonResultVo resultObj = new JsonResultVo();
        flowableForm.setFormJson(EscapeUnescape.unescape(flowableForm.getFormJson()));
        QueryWrapper<FlowableForm> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(FORM_KEY, flowableForm.getFormKey());
        boolean flag = flowableFormService.update(flowableForm, queryWrapper);
        if (flag) {
            String message = i18nUtil.getMessage("MSG.wf.common.saveSuccess");
            resultObj.setMsg(message);
        } else {
            String message = i18nUtil.getMessage("ERR.wf.common.saveFail");
            resultObj.setMsgErr(message);
        }
        return resultObj;
    }

    /**
     * @param ids
     * @return
     * @功能：批量删除
     */
    @ApiOperation(value = "删除流程表单",  httpMethod = "DELETE", notes = "[author:10027705]")
    @DeleteMapping(value = "/delete")
    public JsonResultVo delete(@RequestParam String ids) {

        JsonResultVo resultObj = new JsonResultVo();
        if (ids == null || ids.trim().length() == 0) {
            resultObj.setMsgErr("ids can't be empty");
            return resultObj;
        }
        String[] idsArr = ids.split(",");
        boolean flag = true;
        if (idsArr.length > 1) {
            flag = flowableFormService.removeByIds(Arrays.asList(idsArr));
        } else {
            flag = flowableFormService.removeById(idsArr[0]);
        }
        if (flag) {
            String message = i18nUtil.getMessage("MSG.wf.common.delSuccess");
            resultObj.setMsg(message);
        } else {
            String message = i18nUtil.getMessage("ERR.wf.common.delFail");
            resultObj.setMsgErr(message);
        }
        return resultObj;
    }

    @ApiOperation(value = "获取所有表格",  httpMethod = "POST", notes = "[author:10027705]")
    @PostMapping("/getAllForms")
    public JsonResultVo<FlowableForm> getAll() {
        JsonResultVo resultObj = new JsonResultVo();
        List<FlowableForm> result = flowableFormService.list();
        resultObj.setData(result);
        return resultObj;
    }
}
