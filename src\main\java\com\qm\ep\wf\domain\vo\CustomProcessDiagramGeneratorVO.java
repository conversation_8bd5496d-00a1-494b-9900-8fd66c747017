package com.qm.ep.wf.domain.vo;

import lombok.Data;
import org.flowable.bpmn.model.BpmnModel;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/7
 */
@Data
public class CustomProcessDiagramGeneratorVO implements Serializable {
    private BpmnModel bpmnModel;
    private String imageType;
    private List<String> highLightedActivities;
    private List<String> highLightedFlows;
    private List<String> runningActivitiIdList;
    private String activityFontName;
    private String labelFontName;
    private String annotationFontName;
    private ClassLoader customClassLoader;
    private Double scaleFactor;
    private Boolean drawSequenceFlowNameWithNoLabelDi;
}
