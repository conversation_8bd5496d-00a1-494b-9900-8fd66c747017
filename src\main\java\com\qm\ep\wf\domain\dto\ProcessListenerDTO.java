package com.qm.ep.wf.domain.dto;

import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-15
 */
@ApiModel(value = "对象ProcessListenerDTO对象", description = "对象ProcessListenerDTO对象")
@Data
public class ProcessListenerDTO extends JsonParamDto {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "主键ID")
    private String id;
    @ApiModelProperty(value = "名称")
    private String name;
    @ApiModelProperty(value = "监听器类型  字典项：（执行监听器executionListener；任务监听器taskListener）")
    private String listenerType;
    @ApiModelProperty(value = "事件类型 执行监听器对应（start；take；end） 任务监听器对应（assignment；create；complete；delete）")
    private String eventType;
    @ApiModelProperty(value = "值类型 字典项（类；表达式；委托表达式）")
    private String valueType;
    @ApiModelProperty(value = "类/表达式/委托表达式")
    private String expression;
    @ApiModelProperty(value = "流程定义Key 对应bpmn中process的ID")
    private String processDefKey;
}
