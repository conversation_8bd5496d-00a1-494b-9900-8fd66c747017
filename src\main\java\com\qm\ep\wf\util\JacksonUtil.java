package com.qm.ep.wf.util;

import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

/**
 * Jackson工具类
 *
 * <AUTHOR>
@Slf4j
public class JacksonUtil {


    /**
     * 这个类不能实例化
     */
    private JacksonUtil() {
    }

    /**
     * 对象转str
     *
     * @param obj
     * @param include
     * @return
     */
    public static String objToStr(Object obj, Include include) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            if (include != null) {
                objectMapper.setSerializationInclusion(include);
            }
            return objectMapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            log.info("---error--"+"objToStr:", e);
        }
        return null;
    }

    /**
     * 对象转str
     *
     * @param obj
     * @return
     */
    public static String objToStr(Object obj) {
        return objToStr(obj, null);
    }

}
