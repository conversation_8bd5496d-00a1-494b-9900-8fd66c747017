package com.qm.ep.wf.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.qm.ep.wf.domain.entity.FlowableForm;

/**
 * 流程表单Service
 *
 * <AUTHOR>
public interface FlowableFormService extends IService<FlowableForm> {
    /**
     * 分页查询流程表单
     *
     * @param page
     * @param flowableForm
     * @return
     */
    IPage<FlowableForm> list(IPage<FlowableForm> page, FlowableForm flowableForm);

    FlowableForm getById(String formKeyDefinition);
}
