package com.qm.ep.wf.domain.bean;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

/**
 * <p>
 * 事务码表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-02
 */
@ApiModel(description = "事务码表")
@Data
public class TransCodeDO implements Serializable {

    @ApiModelProperty("串行版 uid")
    private static final long serialVersionUID = 121212134343L;

    @ApiModelProperty("数据id")
    private String id;

    @ApiModelProperty(value = "事务码代码")
    private String vcode;

    @ApiModelProperty(value = "事务码名称")
    private String vtext;

    @ApiModelProperty(value = "用例路径")
    private String vpath;

    @ApiModelProperty(value = "模块代码（数据字典module）")
    private String vmodule;

    @ApiModelProperty(value = "停用标志")
    private String vstop;

    @ApiModelProperty(value = "停用日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date dstop;

    @ApiModelProperty(value = "时间戳")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Timestamp dtstamp;

    @ApiModelProperty("授权")
    private Integer authorization;


    @ApiModelProperty("是编辑")
    private Boolean isEdit = false;

}
