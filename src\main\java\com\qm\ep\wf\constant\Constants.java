package com.qm.ep.wf.constant;

/**
 * 常量文件类
 *
 * <AUTHOR>
public class Constants {
    /**
     * 这个类不能实例化
     */
    private Constants() {
    }

    public static final String ADMIN = "admin";
    public static final String UNDERSCORE = "_";
    public static final String CROSSBAR = "-";
    public static final String COMMA = ",";
    public static final String FORWARD_SLASH = "/";
    public static final String BACK_SLASH = "\\";
    public static final String CURRENT = "currentPage";
    public static final String SIZE = "pageSize";
    public static final String ORDER_RULE = "orderRule";
    public static final String INITNODENAME = "__initiator__";
    public static final String NO_MODEL_DEF = "No process definition found for key";
    public static final String MODULE = "common-wf";
    public static final String AUTO_TASK_BUTTON = "autoTaskButton";
    public static final String BUTTON_BELONG_TASK = "button_belong_to_task";
    public static final String RABBIT_WF_ROLECHANGE_QUEUE = "qm.wf.role.queue";
    public static final String RABBIT_DEADLETTER_EXCHANGE = "x-dead-letter-exchange";
    public static final String RABBIT_WF_DEADLETTER_EXCHANGE = "qm-wf-deadletter-exchange";
    public static final String RABBIT_DEADLETTER_ROUTINGKEY = "x-dead-letter-routing-key";
    public static final String RABBIT_ROLE_DEADLETTER_ROUTINGKEY = "qm-wf-deadletter-routingkey";
    public static final String RABBIT_ROLE_EXCHANGE = "qm.sys.role.exchange";

}
