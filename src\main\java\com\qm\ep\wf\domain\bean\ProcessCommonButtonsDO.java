package com.qm.ep.wf.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-15
 */
@Data
@Accessors(chain = true)
@TableName("act_ex_button")
@EqualsAndHashCode
@ApiModel(value = "对象ProcessCommonButtonsDO对象", description = "对象ProcessCommonButtonsDO对象")
public class ProcessCommonButtonsDO implements Serializable {

    @ApiModelProperty("串行版 uid")
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty(value = "名称")
    @TableField("NAME")
    private String name;

    @ApiModelProperty(value = "代码")
    @TableField("CODE")
    private String code;

    @ApiModelProperty(value = "状态码")
    @TableField("STATE_CODE")
    private String stateCode;

    @ApiModelProperty(value = "正向/方向，字典项")
    @TableField("DIRECTION")
    private String direction;

    @ApiModelProperty(value = "排序")
    @TableField("ORDER_NO")
    private Integer orderNo;

    @ApiModelProperty(value = "动作Url")
    @TableField("ACTION_URL")
    private String actionUrl;

    @ApiModelProperty(value = "流程定义Key")
    @TableField("PROCESS_DEF_KEY")
    private String processDefKey;

    @ApiModelProperty(value = "核心标识")
    @TableField("CORE_FLAG")
    private Integer coreFlag;

    @ApiModelProperty(value = "是否隐藏")
    @TableField("VISABLE")
    private Integer visable;

    @ApiModelProperty(value = "互斥显示")
    @TableField("MUTEX")
    private Integer mutex;

    @ApiModelProperty(value = "时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    @Version
    private Timestamp dtstamp;

    @ApiModelProperty(value = "备注")
    @TableField("REMARK")
    private String remark;

    @ApiModelProperty(value = "是否显示状态0否1是")
    @TableField("IS_STATE")
    private Integer isState;

    @ApiModelProperty(value = "按钮状态 reject 拒绝 agree 同意 giveup 弃权")
    @TableField("BUTTON_TYPE")
    private String buttonType;

    @ApiModelProperty(value = "任务ID")
    @TableField(exist = false)
    private String taskId;

    @ApiModelProperty(value = "样式分类")
    @TableField("STYLE_TYPE")
    private String styleType;

    @ApiModelProperty(value = "样式")
    @TableField("STYLE")
    private String style;

    @ApiModelProperty(value = "对齐方式")
    @TableField("HORIZONTAL_ALIGN")
    private String horizontalAlign;


    @ApiModelProperty(value = "语言代码")
    @TableField(value = "vlanguagecode", exist = false)
    private String vlanguagecode;

    @ApiModelProperty(value = "主操作标识")
    @TableField(value = "OPERATION_FLAG")
    private Integer operationFlag;

}
