package com.qm.ep.wf.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-19
 */
@ApiModel(value = "TaskTranAuthVO对象", description = "")
@Data
public class TaskTranAuthVO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private String id;

    @ApiModelProperty(value = "任务名称")
    private String taskName;

    @ApiModelProperty(value = "事务码")
    private String transCode;

    @ApiModelProperty(value = "工作流模版Key")
    private String processDefKey;

    @ApiModelProperty(value = "时间戳")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Timestamp dtstamp;

    @ApiModelProperty(value = "按钮CODE")
    private String buttonCode;
}
