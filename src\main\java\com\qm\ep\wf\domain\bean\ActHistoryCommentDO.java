package com.qm.ep.wf.domain.bean;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.flowable.engine.task.Comment;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("act_ex_comment")
@ApiModel(value = "工作流迁移明细表", description = "")
public class ActHistoryCommentDO implements Comment {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "ID_", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty(value = "审批类型")
    @TableField("TYPE_")
    private String type;

    @ApiModelProperty(value = "审批时间")
    @TableField("TIME_")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date time;

    @ApiModelProperty(value = "审批人")
    @TableField("USER_ID_")
    private String userId;

    @ApiModelProperty(value = "消息内容")
    @TableField("MESSAGE_")
    private String message;

    @ApiModelProperty(value = "消息内容")
    @TableField("FULL_MSG_")
    private String fullMessage;

    @ApiModelProperty(value = "动作")
    @TableField("ACTION_")
    private String action;

    @ApiModelProperty(value = "流程定义KEY")
    @TableField("PROCESS_DEF_KEY")
    private String processDefKey;

    @ApiModelProperty(value = "业务主键KEY")
    @TableField("BUSINESS_KEY")
    private String businessKey;

    @ApiModelProperty(value = "taskId")
    @TableField(value = "taskId", exist = false)
    private String taskId;

    @ApiModelProperty(value = "processInstanceId")
    @TableField(value = "processInstanceId", exist = false)
    private String processInstanceId;
}
