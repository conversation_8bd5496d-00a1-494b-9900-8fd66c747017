package com.qm.ep.wf.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qm.ep.wf.domain.bean.ProcessCommonButtonsDO;
import com.qm.ep.wf.domain.dto.ProcessCommonButtonsDTO;
import com.qm.ep.wf.domain.dto.TaskTranAuthDTO;
import com.qm.ep.wf.domain.vo.RoleVO;
import com.qm.ep.wf.service.ProcessCommonButtonsService;
import com.qm.ep.wf.service.TaskTranAuthService;
import com.qm.ep.wf.service.WfConfService;
import com.qm.ep.wf.service.impl.v2.ProcessInstanceServiceV2Impl;
import com.qm.ep.wf.util.LoginUtil;
import com.qm.ep.wf.util.ObjectUtils;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonParamDto;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.I18nUtil;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * Controller
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-15
 */
@RestController
@RequestMapping("/processCommonButtons")
public class ProcessCommonButtonsController extends BaseController {
    @Autowired
    private ProcessCommonButtonsService processCommonButtonsService;
    @Autowired
    private WfConfService wfConfService;
    @Autowired
    private TaskTranAuthService taskTranAuthService;
    @Autowired
    private I18nUtil i18nUtil;
    @Autowired
    private ProcessInstanceServiceV2Impl processInstanceService;


    private static final String PROCESS_DEF_KEY = "processDefKey";

    /**
     * 使用系统默认的保存/修改 方法
     */
    @ApiOperation(value = "使用系统默认的保存/修改 方法", notes = "[author:10027705]")
    @PostMapping("/save")
    public JsonResultVo<ProcessCommonButtonsDO> save(@RequestBody ProcessCommonButtonsDO tempDO) {
        JsonResultVo<ProcessCommonButtonsDO> resultObj = new JsonResultVo<>();
        //搜索
        QmQueryWrapper<ProcessCommonButtonsDO> qmQueryWrapper = new QmQueryWrapper<>();
        qmQueryWrapper.eq("code", tempDO.getCode());
        qmQueryWrapper.eq(PROCESS_DEF_KEY, tempDO.getProcessDefKey());

        if (ObjectUtils.isEmpty(tempDO)) {
            String message = i18nUtil.getMessage("ERR.wf.common.isEmpty", "ProcessCommonButtonsDO");
            resultObj.setMsgErr(message);
            return resultObj;
        } else {
            QmQueryWrapper<ProcessCommonButtonsDO> qmQueryWrapper1 = new QmQueryWrapper<>();
            qmQueryWrapper1.eq("id", tempDO.getId());
            ProcessCommonButtonsDO rptItemDO1 = processCommonButtonsService.getOne(qmQueryWrapper1);
            if (rptItemDO1 != null && !rptItemDO1.getCode().equals(tempDO.getCode())) {
                QmQueryWrapper<ProcessCommonButtonsDO> queryWrapper2 = new QmQueryWrapper<>();
                queryWrapper2.eq(PROCESS_DEF_KEY, tempDO.getProcessDefKey());
                queryWrapper2.eq("code", tempDO.getCode());
                ProcessCommonButtonsDO rptItemDO2 = processCommonButtonsService.getOne(queryWrapper2);
                if (rptItemDO2 != null) {
                    String message = i18nUtil.getMessage("ERR.wf.processCommonButtons.rptItemDO2");
                    resultObj.setMsgErr(message);
                    return resultObj;
                }
            }
        }
        boolean flag = processCommonButtonsService.saveOrUpdate(tempDO);
        if (flag) {
            resultObj.setData(tempDO);
            if (!BootAppUtil.isNullOrEmpty(tempDO.getId())) {
                Map<String, List<RoleVO>> roleMaps = processInstanceService.cacheCurrentAllUserRole();
                taskTranAuthService.createRedisData(tempDO.getProcessDefKey(), roleMaps);
            }
        } else {
            String message = i18nUtil.getMessage("ERR.wf.common.saveFail");
            resultObj.setMsgErr(message);
        }
        return resultObj;
    }

    /**
     * 根据传入的id删除数据
     */
    @ApiOperation(value = "根据传入的id删除数据", notes = "[author:10027705]")
    @PostMapping("/deleteById")
    public JsonResultVo deleteById(@RequestBody ProcessCommonButtonsDO tempDO) {
        JsonResultVo resultObj = new JsonResultVo();
        boolean flag = processCommonButtonsService.removeById(tempDO.getId());
        if (flag) {
            String message = i18nUtil.getMessage("MSG.wf.common.delSuccess");
            resultObj.setMsg(message);
            Map<String, List<RoleVO>> roleMaps = processInstanceService.cacheCurrentAllUserRole();
            taskTranAuthService.createRedisData(tempDO.getProcessDefKey(), roleMaps);
        } else {
            String message = i18nUtil.getMessage("ERR.wf.common.delFail");
            resultObj.setMsgErr(message);
        }
        return resultObj;
    }

    /**
     * 根据传入的map删除信息
     */
    @ApiOperation(value = "根据传入的map删除信息", notes = "[author:10027705]")
    @PostMapping("/deleteByMap")
    public JsonResultVo deleteByMap(@RequestBody Map map) {
        JsonResultVo resultObj = new JsonResultVo();
        boolean flag = processCommonButtonsService.removeByMap(map);
        if (flag) {
            String message = i18nUtil.getMessage("ERR.wf.ProcessCommonButtonsController.operateSuccess");
            resultObj.setMsg(message);
        } else {
            String message = i18nUtil.getMessage("MSG.wf.ProcessCommonButtonsController.operateFail");
            resultObj.setMsgErr(message);
        }
        return resultObj;
    }

    /**
     * 根据传入的实体信息进行查询
     */
    @ApiOperation(value = "根据传入的实体信息进行查询", notes = "[author:10027705]")
    @PostMapping("/table")
    public JsonResultVo<QmPage<ProcessCommonButtonsDO>> table(@RequestBody ProcessCommonButtonsDTO tempDTO) {
        //定义查询构造器
        QmQueryWrapper<ProcessCommonButtonsDO> queryWrapper = new QmQueryWrapper<>();
        //拼装实体属性查询条件
        //主键ID
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getId())) {
            queryWrapper.eq("id", tempDTO.getId());
        }
        //Name
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getName())) {
            queryWrapper.like("name", tempDTO.getName());
        }
        //	代码
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getCode())) {
            queryWrapper.eq("code", tempDTO.getCode());
        }
        //状态码
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getStateCode())) {
            queryWrapper.eq("stateCode", tempDTO.getStateCode());
        }
        //正向/方向，字典项
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getDirection())) {
            queryWrapper.eq("direction", tempDTO.getDirection());
        }
        //排序
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getOrderNo())) {
            queryWrapper.eq("orderNo", tempDTO.getOrderNo());
        }
        //动作Url
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getActionUrl())) {
            queryWrapper.eq("actionUrl", tempDTO.getActionUrl());
        }
        //流程定义Key
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getProcessDefKey())) {
            queryWrapper.eq(PROCESS_DEF_KEY, tempDTO.getProcessDefKey());
        }
        //核心标识	
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getCoreFlag())) {
            queryWrapper.eq("coreFlag", tempDTO.getCoreFlag());
        }
        //是否隐藏
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getVisable())) {
            queryWrapper.eq("visable", tempDTO.getVisable());
        }
        //模块
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getVmodule())) {
            Set<String> modules = wfConfService.getWfConfByModule(tempDTO.getVmodule(), 2);
            if (modules.isEmpty()) {
                modules.add("non");
            }
            queryWrapper.in(PROCESS_DEF_KEY, modules);
        }
        //主操作标识
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getOperationFlag())) {
            queryWrapper.eq("operationFlag", tempDTO.getOperationFlag());
        }
        //查询数据，使用table函数。
        QmPage<ProcessCommonButtonsDO> list = processCommonButtonsService.table(queryWrapper, tempDTO);
        JsonResultVo<QmPage<ProcessCommonButtonsDO>> ret = new JsonResultVo<>();
        ret.setData(list);
        return ret;
    }

    /**
     * 查询数据，使用Map进行查找数据集合。
     */
    @ApiOperation(value = "查询数据，使用Map进行查找数据集合", notes = "[author:10027705]")
    @PostMapping("/tableByMap")
    public JsonResultVo<QmPage<ProcessCommonButtonsDO>> tableByMap(@RequestBody Map map) {
        QmQueryWrapper<ProcessCommonButtonsDO> qmQueryWrapper = new QmQueryWrapper<>();
        //拼装实体属性查询条件
        LambdaQueryWrapper<ProcessCommonButtonsDO> lambdaWrapper = qmQueryWrapper.lambda();
        // 主键ID
        if (!BootAppUtil.isNullOrEmpty(map.get("ID"))) {
            lambdaWrapper.eq(ProcessCommonButtonsDO::getId, map.get("ID"));
        }
        // Name
        if (!BootAppUtil.isNullOrEmpty(map.get("NAME"))) {
            lambdaWrapper.eq(ProcessCommonButtonsDO::getName, map.get("NAME"));
        }
        // 	代码
        if (!BootAppUtil.isNullOrEmpty(map.get("CODE"))) {
            lambdaWrapper.eq(ProcessCommonButtonsDO::getCode, map.get("CODE"));
        }
        // 状态码
        if (!BootAppUtil.isNullOrEmpty(map.get("STATE_CODE"))) {
            lambdaWrapper.eq(ProcessCommonButtonsDO::getStateCode, map.get("STATE_CODE"));
        }
        // 正向/方向，字典项
        if (!BootAppUtil.isNullOrEmpty(map.get("DIRECTION"))) {
            lambdaWrapper.eq(ProcessCommonButtonsDO::getDirection, map.get("DIRECTION"));
        }
        // 排序
        if (!BootAppUtil.isNullOrEmpty(map.get("ORDER_NO"))) {
            lambdaWrapper.eq(ProcessCommonButtonsDO::getOrderNo, map.get("ORDER_NO"));
        }
        // 动作Url
        if (!BootAppUtil.isNullOrEmpty(map.get("ACTION_URL"))) {
            lambdaWrapper.eq(ProcessCommonButtonsDO::getActionUrl, map.get("ACTION_URL"));
        }
        // 流程定义Key
        if (!BootAppUtil.isNullOrEmpty(map.get("PROCESS_DEF_KEY"))) {
            lambdaWrapper.eq(ProcessCommonButtonsDO::getProcessDefKey, map.get("PROCESS_DEF_KEY"));
        }
        // 核心标识	
        if (!BootAppUtil.isNullOrEmpty(map.get("CORE_FLAG"))) {
            lambdaWrapper.eq(ProcessCommonButtonsDO::getCoreFlag, map.get("CORE_FLAG"));
        }
        // 是否隐藏
        if (!BootAppUtil.isNullOrEmpty(map.get("VISABLE"))) {
            lambdaWrapper.eq(ProcessCommonButtonsDO::getVisable, map.get("VISABLE"));
        }
        //获取分页信息
        JsonParamDto jsonParamDto = getParaFromMap(map);
        //查询数据，使用table函数。
        QmPage<ProcessCommonButtonsDO> list = processCommonButtonsService.table(qmQueryWrapper, jsonParamDto);
        JsonResultVo<QmPage<ProcessCommonButtonsDO>> ret = new JsonResultVo<>();
        ret.setData(list);
        return ret;
    }

    @ApiOperation(value = "复制按钮", notes = "[author:10027705]")
    @PostMapping("/copyButton")
    public JsonResultVo copyButton(@RequestBody Map<String, String> map) {
        return processCommonButtonsService.copyButton(map);
    }

    @ApiOperation(value = "根据参数进行查询", notes = "[author:10027705]")
    @PostMapping("/tableByParams")
    public JsonResultVo<ProcessCommonButtonsDTO> tableByParams(@RequestBody TaskTranAuthDTO tempDTO) {
        //查询数据，使用table函数。
        tempDTO.setIsState(1);
        if (StringUtils.isEmpty(tempDTO.getActionCode())) {
            tempDTO.setActionCode(LoginUtil.getOperatorId());
        }
        List<ProcessCommonButtonsDTO> list = processCommonButtonsService.getButtonsByParams(tempDTO);
        JsonResultVo<ProcessCommonButtonsDTO> ret = new JsonResultVo<>();
        ret.setDataList(list);
        return ret;
    }
}
