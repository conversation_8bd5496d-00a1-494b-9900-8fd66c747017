package com.qm.ep.wf.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Maps;
import com.qm.ep.wf.common.BaseFlowableController;
import com.qm.ep.wf.common.CommentTypeEnum;
import com.qm.ep.wf.common.FlowablePage;
import com.qm.ep.wf.constant.Constants;
import com.qm.ep.wf.constant.FlowableConstant;
import com.qm.ep.wf.domain.bean.ProcessCommonButtonsDO;
import com.qm.ep.wf.domain.bean.ProcessCounterSignDO;
import com.qm.ep.wf.domain.bean.TaskAuthDO;
import com.qm.ep.wf.domain.bean.TaskTranAuthDO;
import com.qm.ep.wf.domain.dto.*;
import com.qm.ep.wf.domain.entity.WfConf;
import com.qm.ep.wf.domain.vo.*;
import com.qm.ep.wf.exception.FlowableTaskException;
import com.qm.ep.wf.mapper.HisFlowableActinstMapper;
import com.qm.ep.wf.mapper.RunFlowableActinstMapper;
import com.qm.ep.wf.remote.SysFeignRemote;
import com.qm.ep.wf.service.*;
import com.qm.ep.wf.util.*;
import com.qm.ep.wf.wapper.IListWrapper;
import com.qm.ep.wf.wapper.ProcInsListWrapper;
import com.qm.ep.wf.wapper.TaskListWrapper;
import com.qm.ep.wf.wapper.TaskTodoListWrapper;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import com.qm.tds.base.domain.LoginKeyDO;
import com.qm.tds.lock.client.RedissonLockClient;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.I18nUtil;
import com.qm.tds.util.RedisUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.RandomStringUtils;
import org.flowable.bpmn.model.Process;
import org.flowable.bpmn.model.*;
import org.flowable.common.engine.api.query.QueryProperty;
import org.flowable.common.engine.impl.identity.Authentication;
import org.flowable.editor.language.json.converter.util.CollectionUtils;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.history.HistoricProcessInstanceQuery;
import org.flowable.engine.impl.persistence.entity.ActivityInstanceEntity;
import org.flowable.engine.impl.persistence.entity.ExecutionEntity;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.runtime.ActivityInstance;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.api.Task;
import org.flowable.task.api.TaskQuery;
import org.flowable.task.api.history.HistoricTaskInstanceQuery;
import org.flowable.task.service.impl.HistoricTaskInstanceQueryProperty;
import org.flowable.task.service.impl.TaskQueryProperty;
import org.flowable.variable.api.history.HistoricVariableInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> @date
 */
@SuppressWarnings({"squid:S3776", "squid:S3740", "rawtypes", "unchecked"})
@Slf4j
@RestController
@RequestMapping("/flowable/task")
@Api(value = "任务管理", tags = {"task"})
public class TaskController extends BaseFlowableController {
    @Autowired
    protected FlowableTaskService flowableTaskService;

    @Autowired
    private SysFeignRemote sysFeignRemote;

    @Autowired
    private ProcessInstanceService processInstanceService;

    @Autowired
    private ProcessDefinitionService processDefinitionService;

    @Autowired
    private WfConfService wfConfService;

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private ProcessCommonButtonsService commonButtonsService;

    @Autowired
    private ProcessCounterSignService processCounterSignService;

    @Autowired
    private WorkFlowLogService workFlowLogService;

    @Autowired
    RedissonLockClient redissonLock;
    @Autowired
    private I18nUtil i18nUtil;
    @Autowired
    private RunFlowableActinstMapper runFlowableActinstDao;
    @Autowired
    private HisFlowableActinstMapper hisFlowableActinstDao;

    @Autowired
    private TaskAuthService taskAuthService;

    @Autowired
    private TaskTranAuthService taskTranAuthService;

    private static Map<String, QueryProperty> allowedSortProperties = new HashMap<>();
    private static Map<String, QueryProperty> allowedSortPropertiesTodo = new HashMap<>();


    private static final String BUSINESS_KEYS = "businessKeys";

    private static final String BUSINESS_KEY = "BUSINESS_KEY_";

    private static final String MESSAGE = "message";

    private static final String STATE_CODE = "stateCode";

    private static final String PROC_INST_ID = "PROC_INST_ID_";

    static {
        allowedSortProperties.put("deleteReason", HistoricTaskInstanceQueryProperty.DELETE_REASON);
        allowedSortProperties.put("duration", HistoricTaskInstanceQueryProperty.DURATION);
        allowedSortProperties.put("endTime", HistoricTaskInstanceQueryProperty.END);
        allowedSortProperties.put(FlowableConstant.EXECUTION_ID, HistoricTaskInstanceQueryProperty.EXECUTION_ID);
        allowedSortProperties.put("taskInstanceId", HistoricTaskInstanceQueryProperty.HISTORIC_TASK_INSTANCE_ID);
        allowedSortProperties.put(FlowableConstant.PROCESS_DEFINITION_ID,
                HistoricTaskInstanceQueryProperty.PROCESS_DEFINITION_ID);
        allowedSortProperties.put(FlowableConstant.PROCESS_INSTANCE_ID,
                HistoricTaskInstanceQueryProperty.PROCESS_INSTANCE_ID);
        allowedSortProperties.put("assignee", HistoricTaskInstanceQueryProperty.TASK_ASSIGNEE);
        allowedSortProperties.put(FlowableConstant.TASK_DEFINITION_KEY,
                HistoricTaskInstanceQueryProperty.TASK_DEFINITION_KEY);
        allowedSortProperties.put("description", HistoricTaskInstanceQueryProperty.TASK_DESCRIPTION);
        allowedSortProperties.put("dueDate", HistoricTaskInstanceQueryProperty.TASK_DUE_DATE);
        allowedSortProperties.put(FlowableConstant.NAME, HistoricTaskInstanceQueryProperty.TASK_NAME);
        allowedSortProperties.put("owner", HistoricTaskInstanceQueryProperty.TASK_OWNER);
        allowedSortProperties.put("priority", HistoricTaskInstanceQueryProperty.TASK_PRIORITY);
        allowedSortProperties.put(FlowableConstant.TENANT_ID, HistoricTaskInstanceQueryProperty.TENANT_ID_);
        allowedSortProperties.put("startTime", HistoricTaskInstanceQueryProperty.START);

        allowedSortPropertiesTodo.put(FlowableConstant.PROCESS_DEFINITION_ID, TaskQueryProperty.PROCESS_DEFINITION_ID);
        allowedSortPropertiesTodo.put(FlowableConstant.PROCESS_INSTANCE_ID, TaskQueryProperty.PROCESS_INSTANCE_ID);
        allowedSortPropertiesTodo.put(FlowableConstant.TASK_DEFINITION_KEY, TaskQueryProperty.TASK_DEFINITION_KEY);
        allowedSortPropertiesTodo.put("dueDate", TaskQueryProperty.DUE_DATE);
        allowedSortPropertiesTodo.put(FlowableConstant.NAME, TaskQueryProperty.NAME);
        allowedSortPropertiesTodo.put("priority", TaskQueryProperty.PRIORITY);
        allowedSortPropertiesTodo.put(FlowableConstant.TENANT_ID, TaskQueryProperty.TENANT_ID);
        allowedSortPropertiesTodo.put("createTime", TaskQueryProperty.CREATE_TIME);
    }

    protected HistoricTaskInstanceQuery createHistoricTaskInstanceQuery(Map<String, String> requestParams) {
        HistoricTaskInstanceQuery query = historyService.createHistoricTaskInstanceQuery();
        if (ObjectUtils.isNotEmpty(requestParams.get(FlowableConstant.TASK_ID))) {
            query.taskId(requestParams.get(FlowableConstant.TASK_ID));
        }
        if (ObjectUtils.isNotEmpty(requestParams.get(FlowableConstant.PROCESS_INSTANCE_ID))) {
            query.processInstanceId(requestParams.get(FlowableConstant.PROCESS_INSTANCE_ID));
        }
        if (ObjectUtils.isNotEmpty(requestParams.get(FlowableConstant.PROCESS_INSTANCE_BUSINESS_KEY))) {
            query.processInstanceBusinessKeyLike(
                    ObjectUtils.convertToLike(requestParams.get(FlowableConstant.PROCESS_INSTANCE_BUSINESS_KEY)));
        }
        if (ObjectUtils.isNotEmpty(requestParams.get(FlowableConstant.PROCESS_DEFINITION_KEY))) {
            query.processDefinitionKey(requestParams.get(FlowableConstant.PROCESS_DEFINITION_KEY));
        }
        if (ObjectUtils.isNotEmpty(requestParams.get(FlowableConstant.PROCESS_DEFINITION_ID))) {
            query.processDefinitionId(requestParams.get(FlowableConstant.PROCESS_DEFINITION_ID));
        }
        if (ObjectUtils.isNotEmpty(requestParams.get(FlowableConstant.PROCESS_DEFINITION_NAME))) {
            query.processDefinitionNameLike(
                    ObjectUtils.convertToLike(requestParams.get(FlowableConstant.PROCESS_DEFINITION_NAME)));
        }
        if (ObjectUtils.isNotEmpty(requestParams.get(FlowableConstant.EXECUTION_ID))) {
            query.executionId(requestParams.get(FlowableConstant.EXECUTION_ID));
        }
        if (ObjectUtils.isNotEmpty(requestParams.get(FlowableConstant.TASK_NAME))) {
            query.taskNameLike(ObjectUtils.convertToLike(requestParams.get(FlowableConstant.TASK_NAME)));
        }
        if (ObjectUtils.isNotEmpty(requestParams.get(FlowableConstant.TASK_DESCRIPTION))) {
            query.taskDescriptionLike(ObjectUtils.convertToLike(requestParams.get(FlowableConstant.TASK_DESCRIPTION)));
        }
        if (ObjectUtils.isNotEmpty(requestParams.get(FlowableConstant.TASK_DEFINITION_KEY))) {
            query.taskDefinitionKeyLike(
                    ObjectUtils.convertToLike(requestParams.get(FlowableConstant.TASK_DEFINITION_KEY)));
        }
        if (ObjectUtils.isNotEmpty(requestParams.get(FlowableConstant.TASK_ASSIGNEE))) {
            query.taskAssignee(requestParams.get(FlowableConstant.TASK_ASSIGNEE));
        }
        if (ObjectUtils.isNotEmpty(requestParams.get(FlowableConstant.TASK_OWNER))) {
            query.taskOwner(requestParams.get(FlowableConstant.TASK_OWNER));
        }
        if (ObjectUtils.isNotEmpty(requestParams.get(FlowableConstant.TASK_INVOLVED_USER))) {
            query.taskInvolvedUser(requestParams.get(FlowableConstant.TASK_INVOLVED_USER));
        }
        if (ObjectUtils.isNotEmpty(requestParams.get(FlowableConstant.TASK_PRIORITY))) {
            query.taskPriority(ObjectUtils.convertToInteger(requestParams.get(FlowableConstant.TASK_PRIORITY)));
        }
        if (ObjectUtils.isNotEmpty(requestParams.get(FlowableConstant.FINISHED))) {
            boolean isFinished = ObjectUtils.convertToBoolean(requestParams.get(FlowableConstant.FINISHED));
            if (isFinished) {
                query.finished();
            } else {
                query.unfinished();
            }
        }
        if (ObjectUtils.isNotEmpty(requestParams.get(FlowableConstant.PROCESS_FINISHED))) {
            boolean isProcessFinished = ObjectUtils
                    .convertToBoolean(requestParams.get(FlowableConstant.PROCESS_FINISHED));
            if (isProcessFinished) {
                query.processFinished();
            } else {
                query.processUnfinished();
            }
        }
        if (ObjectUtils.isNotEmpty(requestParams.get(FlowableConstant.PARENT_TASK_ID))) {
            query.taskParentTaskId(requestParams.get(FlowableConstant.PARENT_TASK_ID));
        }
        if (ObjectUtils.isNotEmpty(requestParams.get(FlowableConstant.TENANT_ID))) {
            query.taskTenantId(requestParams.get(FlowableConstant.TENANT_ID));
        }
        if (ObjectUtils.isNotEmpty(requestParams.get(FlowableConstant.TASK_CANDIDATE_USER))) {
            query.taskCandidateUser(requestParams.get(FlowableConstant.TASK_CANDIDATE_USER));
        }
        if (ObjectUtils.isNotEmpty(requestParams.get(FlowableConstant.TASK_CANDIDATE_GROUP))) {
            query.taskCandidateGroup(requestParams.get(FlowableConstant.TASK_CANDIDATE_GROUP));
        }
        if (ObjectUtils.isNotEmpty(requestParams.get(FlowableConstant.TASK_CANDIDATE_GROUPS))) {
            query.taskCandidateGroupIn(
                    Arrays.asList(requestParams.get(FlowableConstant.TASK_CANDIDATE_GROUPS).split(",")));
        }
        if (ObjectUtils.isNotEmpty(requestParams.get(FlowableConstant.DUE_DATE_AFTER))) {
            query.taskDueAfter(ObjectUtils.convertToDate(requestParams.get(FlowableConstant.DUE_DATE_AFTER)));
        }
        if (ObjectUtils.isNotEmpty(requestParams.get(FlowableConstant.DUE_DATE_BEFORE))) {
            query.taskDueBefore(ObjectUtils.convertToDate(requestParams.get(FlowableConstant.DUE_DATE_BEFORE)));
        }
        if (ObjectUtils.isNotEmpty(requestParams.get(FlowableConstant.TASK_CREATED_BEFORE))) {
            query.taskCreatedBefore(
                    ObjectUtils.convertToDatetime(requestParams.get(FlowableConstant.TASK_CREATED_BEFORE)));
        }
        if (ObjectUtils.isNotEmpty(requestParams.get(FlowableConstant.TASK_CREATED_AFTER))) {
            query.taskCreatedAfter(
                    ObjectUtils.convertToDatetime(requestParams.get(FlowableConstant.TASK_CREATED_AFTER)));
        }
        if (ObjectUtils.isNotEmpty(requestParams.get(FlowableConstant.TASK_COMPLETED_BEFORE))) {
            query.taskCompletedBefore(
                    ObjectUtils.convertToDatetime(requestParams.get(FlowableConstant.TASK_COMPLETED_BEFORE)));
        }
        if (ObjectUtils.isNotEmpty(requestParams.get(FlowableConstant.TASK_COMPLETED_AFTER))) {
            query.taskCompletedAfter(
                    ObjectUtils.convertToDatetime(requestParams.get(FlowableConstant.TASK_COMPLETED_AFTER)));
        }
        return query;
    }

    protected TaskQuery createTaskQuery(Map<String, String> requestParams) {
        TaskQuery query = taskService.createTaskQuery();
        if (ObjectUtils.isNotEmpty(requestParams.get(FlowableConstant.PROCESS_INSTANCE_ID))) {
            query.processInstanceId(requestParams.get(FlowableConstant.PROCESS_INSTANCE_ID));
        }
        if (ObjectUtils.isNotEmpty(requestParams.get(FlowableConstant.TASK_NAME))) {
            query.taskNameLike(ObjectUtils.convertToLike(requestParams.get(FlowableConstant.TASK_NAME)));
        }
        if (ObjectUtils.isNotEmpty(requestParams.get(FlowableConstant.PROCESS_INSTANCE_BUSINESS_KEY))) {
            query.processInstanceBusinessKeyLike(
                    ObjectUtils.convertToLike(requestParams.get(FlowableConstant.PROCESS_INSTANCE_BUSINESS_KEY)));
        }
        if (ObjectUtils.isNotEmpty(requestParams.get(FlowableConstant.PROCESS_DEFINITION_KEY))) {
            query.processDefinitionKeyLike(
                    ObjectUtils.convertToLike(requestParams.get(FlowableConstant.PROCESS_DEFINITION_KEY)));
        }
        if (ObjectUtils.isNotEmpty(requestParams.get(FlowableConstant.PROCESS_DEFINITION_ID))) {
            query.processDefinitionId(requestParams.get(FlowableConstant.PROCESS_DEFINITION_ID));
        }
        if (ObjectUtils.isNotEmpty(requestParams.get(FlowableConstant.PROCESS_DEFINITION_NAME))) {
            query.processDefinitionNameLike(
                    ObjectUtils.convertToLike(requestParams.get(FlowableConstant.PROCESS_DEFINITION_NAME)));
        }
        if (ObjectUtils.isNotEmpty(requestParams.get(FlowableConstant.DUE_DATE_AFTER))) {
            query.taskDueAfter(ObjectUtils.convertToDate(requestParams.get(FlowableConstant.DUE_DATE_AFTER)));
        }
        if (ObjectUtils.isNotEmpty(requestParams.get(FlowableConstant.DUE_DATE_BEFORE))) {
            query.taskDueBefore(ObjectUtils.convertToDate(requestParams.get(FlowableConstant.DUE_DATE_BEFORE)));
        }
        if (ObjectUtils.isNotEmpty(requestParams.get(FlowableConstant.TASK_CREATED_BEFORE))) {
            query.taskCreatedBefore(
                    ObjectUtils.convertToDatetime(requestParams.get(FlowableConstant.TASK_CREATED_BEFORE)));
        }
        if (ObjectUtils.isNotEmpty(requestParams.get(FlowableConstant.TASK_CREATED_AFTER))) {
            query.taskCreatedAfter(
                    ObjectUtils.convertToDatetime(requestParams.get(FlowableConstant.TASK_CREATED_AFTER)));
        }
        if (ObjectUtils.isNotEmpty(requestParams.get(FlowableConstant.TENANT_ID))) {
            query.taskTenantId(requestParams.get(FlowableConstant.TENANT_ID));
        }
        if (ObjectUtils.isNotEmpty(requestParams.get(FlowableConstant.SUSPENDED))) {
            boolean isSuspended = ObjectUtils.convertToBoolean(requestParams.get(FlowableConstant.SUSPENDED));
            if (isSuspended) {
                query.suspended();
            } else {
                query.active();
            }
        }
        return query;
    }

    @GetMapping(value = "/list")
    @ApiOperation(value = "列表查询", notes = "[author:10027705]")
    public JsonResultVo list(@RequestParam Map<String, String> requestParams) {
        JsonResultVo resultObj = new JsonResultVo();
        HistoricTaskInstanceQuery query = createHistoricTaskInstanceQuery(requestParams);
        FlowablePage page = pageList(requestParams, query, TaskListWrapper.class, allowedSortProperties,
                HistoricTaskInstanceQueryProperty.START);
        resultObj.setData(page);
        return resultObj;
    }

    //用于文件导出
    @PostMapping(value = "/list")
    @ApiOperation(value = "列表查询", notes = "[author:10027705]")
    public JsonResultVo list1(@RequestBody Map<String, String> requestParams) {
        JsonResultVo resultObj = new JsonResultVo();
        HistoricTaskInstanceQuery query = createHistoricTaskInstanceQuery(requestParams);
        FlowablePage page = pageList(requestParams, query, TaskListWrapper.class, allowedSortProperties,
                HistoricTaskInstanceQueryProperty.START);
        resultObj.setData(page);
        return resultObj;
    }

    @GetMapping(value = "/listDoneCount")
    @ApiOperation(value = "查询完成数", notes = "[author:10027705]")
    public JsonResultVo listDone(@RequestParam Map<String, String> requestParams) {
        JsonResultVo resultObj = new JsonResultVo();
        HistoricTaskInstanceQuery query = createHistoricTaskInstanceQuery(requestParams);
        if (!BootAppUtil.isNullOrEmpty(requestParams.get(FlowableConstant.PROCESS_DEFINITION_KEY))) {
            boolean flag = wfConfService.getProcessAuditAuthCheck(requestParams.get(FlowableConstant.PROCESS_DEFINITION_KEY));
            if (flag) {
                query.finished().or().taskAssignee(LoginUtil.getOperatorId()).taskOwner(LoginUtil.getOperatorId()).endOr();
            }
        } else {
            query.finished().or().taskAssignee(LoginUtil.getOperatorId()).taskOwner(LoginUtil.getOperatorId()).endOr();
        }
        FlowablePage page = pageList(requestParams, query, TaskListWrapper.class, allowedSortProperties,
                HistoricTaskInstanceQueryProperty.START);
        resultObj.setData(page);
        return resultObj;
    }

    @GetMapping(value = "/listDone")
    @ApiOperation(value = "查询完成", notes = "[author:10027705]")
    public JsonResultVo listDoneCount(@RequestParam Map<String, String> requestParams) {
        JsonResultVo resultObj = new JsonResultVo();
        HistoricTaskInstanceQuery query = createHistoricTaskInstanceQuery(requestParams);
        String operatorId = LoginUtil.getOperatorId();
        log.debug("listDoneCount- OperatorId:" + operatorId);
        if (BootAppUtil.isnotNullOrEmpty(operatorId)) {
            if (!BootAppUtil.isNullOrEmpty(requestParams.get(FlowableConstant.PROCESS_DEFINITION_KEY))) {
                boolean flag = wfConfService.getProcessAuditAuthCheck(requestParams.get(FlowableConstant.PROCESS_DEFINITION_KEY));
                if (flag) {
                    query.finished().or().taskAssignee(operatorId).taskOwner(operatorId).endOr();
                }
            } else {
                query.finished().or().taskAssignee(operatorId).taskOwner(operatorId).endOr();
            }
        }
        long start;
        long end;
        start = System.currentTimeMillis();
        long doneCount = query.count();
        end = System.currentTimeMillis();
        printLog("listDoneCount", start, end);
        resultObj.setData(doneCount);
        return resultObj;
    }


    private void printLog(String method, long start, long end) {
        log.debug(method + " ------------------------------ start time:" + start + "; end time:" + end + "; Run Time:" + (end - start) + "(ms)");
    }

    @GetMapping(value = "/listTodo")
    @ApiOperation(value = "查询待办", notes = "[author:10027705]")
    public JsonResultVo listTodo(@RequestParam Map<String, String> requestParams) {
        JsonResultVo resultObj = new JsonResultVo();
        String userId = LoginUtil.getOperatorId();
        TaskQuery query = createTaskQuery(requestParams);
        if (!BootAppUtil.isNullOrEmpty(requestParams.get(FlowableConstant.PROCESS_DEFINITION_KEY))) {
            boolean flag = wfConfService.getProcessAuditAuthCheck(requestParams.get(FlowableConstant.PROCESS_DEFINITION_KEY));
            if (flag) {
                query.or().taskCandidateOrAssigned(userId).taskOwner(userId).endOr();
            }
        } else {
            query.or().taskCandidateOrAssigned(userId).taskOwner(userId).endOr();
        }
        FlowablePage page = pageList(requestParams, query, TaskTodoListWrapper.class, allowedSortProperties,
                TaskQueryProperty.CREATE_TIME);
        resultObj.setData(page);
        return resultObj;
    }

    @GetMapping(value = "/listTodoCount")
    @ApiOperation(value = "查询待办数量", notes = "[author:10027705]")
    public JsonResultVo listTodoCount(@RequestParam Map<String, String> requestParams) {
        JsonResultVo resultObj = new JsonResultVo();
        String userId = LoginUtil.getOperatorId();
        LoginKeyDO userDO = BootAppUtil.getLoginKey();
        UserDTO userDTO = new UserDTO();
        userDTO.setId(userDO.getOperatorId());
        List roles = new ArrayList();
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            String tenantId = request.getHeader("tenantId");
            long start;
            long end;
            start = System.currentTimeMillis();
            JsonResultVo roleList = sysFeignRemote.getRoleListByUserId(tenantId, userDTO);
            end = System.currentTimeMillis();


            printLog("listTodoCount feignRemote", start, end);

            if (roleList.getCode() == 500) {
                throw new FlowableTaskException(roleList.getMsg());
            } else {
                if (roleList.getData() != null) {
                    for (LinkedHashMap roleDO : (ArrayList<LinkedHashMap>) roleList.getData()) {
                        roles.add(roleDO.get("vrolecode").toString());
                    }
                }
            }
        }
        long start1;
        long end1;
        start1 = System.currentTimeMillis();
        Integer count = flowableTaskService.getTaskTodoCount(userId, roles);
        end1 = System.currentTimeMillis();
        printLog("listTodoCount getTaskTodoCount", start1, end1);
        resultObj.setData(count);
        return resultObj;
    }

    @GetMapping(value = "/queryById")
    @ApiOperation(value = "按照id查询", notes = "[author:10027705]")
    public JsonResultVo queryById(@RequestParam String taskId) {
        JsonResultVo resultObj = new JsonResultVo();
        TaskResponse task = flowableTaskService.getTask(taskId);
        resultObj.setData(task);
        return resultObj;
    }

    @PostMapping(value = "/update")
    @ApiOperation(value = "修改任务", notes = "[author:10027705]")
    public JsonResultVo update(@RequestBody TaskUpdateRequest taskUpdateRequest) {
        JsonResultVo resultObj = new JsonResultVo();
        TaskResponse task = flowableTaskService.updateTask(taskUpdateRequest);
        resultObj.setData(task);
        return resultObj;
    }

    @DeleteMapping(value = "/delete")
    @ApiOperation(value = "删除任务", notes = "[author:10027705]")
    public JsonResultVo delete(@RequestParam String taskId) {
        JsonResultVo resultObj = new JsonResultVo();
        flowableTaskService.deleteTask(taskId);
        String message = i18nUtil.getMessage("MSG.wf.common.saveSuccess");
        resultObj.setMsg(message);
        return resultObj;
    }

    @PostMapping(value = "/assign")
    @ApiOperation(value = "转办任务", notes = "[author:10027705]")
    public JsonResultVo assign(@RequestBody TaskRequest taskRequest) {
        JsonResultVo resultObj = new JsonResultVo();
        flowableTaskService.assignTask(taskRequest);
        return resultObj;
    }

    @PostMapping(value = "/delegate")
    @ApiOperation(value = "委派任务", notes = "[author:10027705]")
    public JsonResultVo delegate(@RequestBody TaskRequest taskRequest) {
        JsonResultVo resultObj = new JsonResultVo();
        flowableTaskService.delegateTask(taskRequest);
        return resultObj;
    }

    @PostMapping(value = "/claim")
    @ApiOperation(value = "认领任务", notes = "[author:10027705]")
    public JsonResultVo claim(@RequestBody TaskRequest taskRequest) {
        JsonResultVo resultObj = new JsonResultVo();
        flowableTaskService.claimTask(taskRequest);
        String message = i18nUtil.getMessage("MSG.wf.common.claimSuccess");
        resultObj.setMsg(message);
        return resultObj;
    }

    @PostMapping(value = "/unclaim")
    @ApiOperation(value = "取消认领任务", notes = "[author:10027705]")
    public JsonResultVo unclaim(@RequestBody TaskRequest taskRequest) {
        JsonResultVo resultObj = new JsonResultVo();
        flowableTaskService.unclaimTask(taskRequest);
        String message = i18nUtil.getMessage("MSG.wf.common.cancelClaimSuccess");
        resultObj.setMsg(message);
        return resultObj;
    }

    @PostMapping(value = "/getTaskButtonsCode")
    @ApiOperation(value = "获取按钮", notes = "[author:10027705]")
    public JsonResultVo getTaskButtonsCode(@RequestParam String taskDefKey, @RequestParam String processDefID) {
        JsonResultVo resultObj = new JsonResultVo();
        resultObj.setData(flowableTaskService.getTaskButtonsCode(taskDefKey, processDefID));
        return resultObj;
    }

    @PostMapping(value = "/complete")
    @ApiOperation(value = "完成任务", notes = "[author:10027705]")
    public JsonResultVo complete(@RequestParam(required = false) String taskId, @RequestParam(required = false) String message
            , @RequestParam(value = "file", required = false) MultipartFile[] file) {
        JsonResultVo resultObj = new JsonResultVo();
        TaskRequest taskRequest = new TaskRequest();
        taskRequest.setTaskId(taskId);
        taskRequest.setMessage(message);
        taskRequest.setAttchemnt(file);
        String msg = "";
        try {
            Map<String, Object> execMap = flowableTaskService.completeTask(taskRequest);
            if (null != execMap) {
                resultObj.setData(execMap);
            }
        } catch (Exception ex) {
            if (ex.getCause() != null && ex.getCause().getCause() != null && ex.getCause().getCause() instanceof FlowableTaskException) {
                msg = ex.getCause().getCause().getMessage();
            } else {
                msg = i18nUtil.getMessage("ERR.wf.common.failReason", ex.getMessage());
            }
            log.info("---error--"+"complete:", ex);
            resultObj.setMsgErr(msg);
        }
        return resultObj;
    }

    private String buildLock(String processDefKey, List<String> businessKeys) {
        StringBuilder sb = new StringBuilder();
        sb.append(processDefKey);
        sb.append(String.join(":", businessKeys));
        String lock = sb.toString().intern();
        log.info("[" + Thread.currentThread().getName() + "]构建了锁[" + lock + "]");
        return lock;
    }

    private String buildLock(String processDefKey, String businessKey, String operatorId) {
        StringBuilder sb = new StringBuilder();
        sb.append(processDefKey);
        sb.append("_");
        sb.append(businessKey);
        sb.append("_");
        sb.append(operatorId);
        String lock = sb.toString().intern();
        log.info("[" + Thread.currentThread().getName() + "]构建了锁[" + lock + "]");
        return lock;
    }

    private JsonResultVo completeByButton(Map<String, Object> requestParams, String processDefKey, List<String> businessKeys) {
        JsonResultVo resultObj = new JsonResultVo();
        String listenerType = "";
        if (null != requestParams.get("listenerType")) {
            listenerType = requestParams.get("listenerType").toString();
        }
        String errorMsgType = "";
        if (null != requestParams.get("errorMsgType")) {
            errorMsgType = requestParams.get("errorMsgType").toString();
        }
        String taskId = "";
        if (null != requestParams.get(FlowableConstant.TASK_ID)) {
            taskId = requestParams.get(FlowableConstant.TASK_ID).toString();
        }
        String message = "";
        List<Map> businessMsgs = new ArrayList<>();

        if (!BootAppUtil.isNullOrEmpty(requestParams.get(MESSAGE)) && requestParams.get(MESSAGE) instanceof String) {
            message = requestParams.get(MESSAGE).toString();
        } else if (!BootAppUtil.isNullOrEmpty(requestParams.get(MESSAGE)) && requestParams.get(MESSAGE) instanceof ArrayList) {
            businessMsgs = (List<Map>) requestParams.get(MESSAGE);
        }
        Date messageTime = null;
        if (!BootAppUtil.isNullOrEmpty(requestParams.get(FlowableConstant.MESSAGE_TIME))) {
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSSSSS");
            ParsePosition pos = new ParsePosition(0);
            messageTime = formatter.parse(requestParams.get(FlowableConstant.MESSAGE_TIME).toString(), pos);
        }
        List<String> msgs = new ArrayList<>();
        // 循环批量执行任务
        Integer failNum = 0;
        Integer successNum = 0;
        StringBuilder msgBuffer = new StringBuilder();
        String userId = LoginUtil.getOperatorId();
        //是否验证审批者权限
        boolean authflag = wfConfService.getProcessAuditAuthCheck(processDefKey);
        //这里调整，如果业务传递自定义标识，则用自定义标识拼接
        String customUniqueFlag = "";
        //请求信息移到循环外部，考虑批量执行性能
        TaskRequest taskRequest = new TaskRequest();
        if (businessMsgs.isEmpty()) {
            taskRequest.setMessage(message);
        }
        taskRequest.setMessageTime(messageTime);
        if (!BootAppUtil.isNullOrEmpty(requestParams.get("oprtor"))) {
            taskRequest.setOprtor(requestParams.get("oprtor").toString());
        }
        //操作按钮信息
        Map<String, Object> opr = new HashMap<>();
        opr.put("name", requestParams.get("name"));
        opr.put("code", requestParams.get("code"));
        opr.put(STATE_CODE, requestParams.get(STATE_CODE));
        //记录批量审核日志
        if (!BootAppUtil.isNullOrEmpty(requestParams.get("batchId"))) {
            String batchId = requestParams.get("batchId").toString();
            try {
                if (redissonLock.tryLock(batchId, -1, 3000)) {
                    workFlowLogService.saveBatchCompleteInit(requestParams);
                    redissonLock.unlock(batchId);
                } else {
                    log.info("saveBatchCompleteInit获取锁失败：" + batchId);
                }
            } catch (Exception ex) {
                log.info("saveBatchCompleteInit执行异常，释放锁batchId：" + batchId);
                redissonLock.unlock(batchId);
            }
        }
        //这里添加根据流程定义Key和按钮Code获取按钮分组
        if (BootAppUtil.isNullOrEmpty(requestParams.get("name")) || BootAppUtil.isNullOrEmpty(requestParams.get("code"))) {
            List<ProcessCommonButtonsDO> commonButtons = commonButtonsService.getButtons(processDefKey, requestParams.get("code").toString());
            if (!commonButtons.isEmpty()) {
                opr.put("group", commonButtons.get(0).getButtonType() == null ? "" : commonButtons.get(0).getButtonType());
                if (BootAppUtil.isNullOrEmpty(requestParams.get("name"))) {
                    opr.put("name", commonButtons.get(0).getName());
                    requestParams.put("name", commonButtons.get(0).getName());
                }
                if (BootAppUtil.isNullOrEmpty(requestParams.get(STATE_CODE))) {
                    opr.put(STATE_CODE, commonButtons.get(0).getName());
                }
            }
        }
        opr.put(FlowableConstant.TASK_ATTACH_VARIABLE, requestParams.get(FlowableConstant.TASK_ATTACH_VARIABLE));//添加额外执行变量
        Map<String, Object> values = new HashMap<>();
        values.put(FlowableConstant.PROCESS_INSTANCE_ACTION_INFO, opr);
        values.put(FlowableConstant.PROCESS_INSTANCE_VCONDITION, requestParams.get("code"));
        Object attachV = requestParams.get(FlowableConstant.TASK_ATTACH_CONDITION);
        if (null != attachV) {
            Map<String, Object> attachMap = new HashMap<>();
            if (attachV instanceof Map) {
                attachMap = (Map<String, Object>) attachV;

            } else if (attachV instanceof String && attachV != "") {
                attachMap = JSON.parseObject(attachV.toString());
            } else if (attachV instanceof List) {
                List<Object> attachList = (List<Object>) attachV;
                Map<String, Object> listMaps = Maps.newHashMap();
                for (Object o : attachList) {
                    if (o == null || !(o instanceof Map)) {
                        continue;
                    }
                    Map<String, Object> conditionVariable = (Map<String, Object>) o;
                    //BUSINESS_KEY
                    if (!conditionVariable.containsKey(FlowableConstant.BUSINESS_KEY)) {
                        continue;
                    }
                    values.put((String) conditionVariable.get(FlowableConstant.BUSINESS_KEY), conditionVariable);

                }
            }
            for (Map.Entry<String, Object> item : attachMap.entrySet()) {
                values.put(item.getKey(), item.getValue());
            }
        }
        if (!BootAppUtil.isNullOrEmpty(requestParams.get(FlowableConstant.NO_LISTENER))) {
            values.put(FlowableConstant.NO_LISTENER, requestParams.get(FlowableConstant.NO_LISTENER));
        }
        taskRequest.setValues(values);
        taskRequest.setProcessDefKey(processDefKey);
        taskRequest.setCode(requestParams.get("code").toString());
        // 同步查询流程实例，防止并发
        String lock = buildLock(processDefKey, businessKeys);
        List<String> failBusiKeys = new ArrayList<>();
        String exeMsg = "";
        synchronized (lock) {
            List<Task> taskList;
            List<Map> hisProcessInstances = new ArrayList<>();
            SynchronizedLockObj<List<String>> instance = SynchronizedLockObj.getInstance();
            instance.setObj(businessKeys);
            synchronized (instance) {
                //业务单据历史流程实例批量查询
                hisProcessInstances = flowableTaskService.hisProcInsQueryByBusinessKeys(processDefKey, businessKeys);
                //从list集合中，取出字段name的列表
                List<String> processInstanceIds = hisProcessInstances.stream().map(d -> d.get(PROC_INST_ID).toString()).collect(Collectors.toList());

                if (hisProcessInstances.isEmpty() || hisProcessInstances.size() < businessKeys.size()) {
                    List<String> errorBusiKeys = new ArrayList<>();
                    for (String businessKey : businessKeys) {
                        boolean isExist = hisProcessInstances.stream().anyMatch(m -> m.get(BUSINESS_KEY).equals(businessKey));
                        if (!isExist) {
                            errorBusiKeys.add(businessKey);
                        }
                    }
                    //工作流代码：" + processDefKey + " 业务主键：" + String.join(",", errorBusiKeys) + " 不存在工作流实例！
                    String msg = i18nUtil.getMessage("ERR.wf.TaskController.wfcodeError", processDefKey, String.join(",", errorBusiKeys));
                    setErrorMsg(errorMsgType, msg, resultObj);
                    if (!BootAppUtil.isNullOrEmpty(requestParams.get("batchId"))) {
                        Map countM = new HashMap();
                        failNum = businessKeys.size();
                        successNum = 0;
                        countM.put("successNum", successNum);
                        countM.put("failNum", failNum);
                        countM.put("failBusinessKeys", String.join(",", errorBusiKeys));
                        resultObj.setData(countM);
                        // 记录导出日志
                        String failReasonIs = i18nUtil.getMessage("ERR.wf.TaskController.failReasonIs");
                        workFlowLogService.saveBatchProcess(requestParams, failNum, successNum, String.join(",", errorBusiKeys) + requestParams.get("name") + failReasonIs + msg);
                    }
                    return resultObj;
                }

                // 查询待办节点
                TaskQuery taskQuery = taskService.createTaskQuery();
                if (BootAppUtil.isnotNullOrEmpty(taskId)) {
                    taskQuery.taskDefinitionKey(taskId);
                }
                taskQuery.processInstanceIdIn(processInstanceIds);
                if (authflag) {
                    taskQuery.or().taskCandidateOrAssigned(userId).taskOwner(userId).endOr();
                }
                //批量获取待办任务
                taskList = taskQuery.list();
                // 兼容PersonCode
                if (authflag && CollectionUtils.isEmpty(taskList)) {
                    //getPersonCode
                    userId = LoginUtil.getUserId();
                    taskQuery.or().taskCandidateOrAssigned(userId).taskOwner(userId).endOr();
                    taskList = taskQuery.list();
                }
            }

            //这里按照任务定义key进行分组
            Map<String, List<Task>> taskGroupMap = taskList.stream()
                    .collect(Collectors.groupingBy(Task::getTaskDefinitionKey));
            //说明是多实例情况，提取本审核按钮对应的任务
            if (taskList.size() > businessKeys.size()) {
                taskList = taskGroupMap.get(requestParams.get("code").toString());
            }
            if (businessKeys.size() > 1 && taskGroupMap.size() > 1 && taskList.size() < businessKeys.size()) {
                //说明存在状态不同的任务
                //批量" + requestParams.get("name") + "失败，请选择相同状态的单据进行处理！
                String msg = i18nUtil.getMessage("ERR.wf.TaskController.batchFail", StrUtil.toString(requestParams.get("name")));
                setErrorMsg(errorMsgType, msg, resultObj);
                if (!BootAppUtil.isNullOrEmpty(requestParams.get("batchId"))) {
                    Map countM = new HashMap();
                    failNum = businessKeys.size();
                    successNum = 0;
                    countM.put("successNum", successNum);
                    countM.put("failNum", failNum);
                    countM.put("failBusinessKeys", String.join(",", businessKeys));
                    resultObj.setData(countM);
                    workFlowLogService.saveBatchProcess(requestParams, failNum, successNum, String.join(",", businessKeys) + msg);
                }
                return resultObj;
            }
            if (taskList.isEmpty() || taskList.size() < businessKeys.size()) {
                List<String> errorBusiKeys = new ArrayList<>();
                if (!BootAppUtil.isNullOrEmpty(customUniqueFlag)) {
                    String errMsg = i18nUtil.getMessage("ERR.wf.task.errorMessage");
                    exeMsg = customUniqueFlag + requestParams.get("name") + errMsg;
                    setErrorMsg(errorMsgType, exeMsg, resultObj);
                } else {
                    if (businessKeys.size() == 1) {
                        if (!taskList.isEmpty()) {
                            String processInsId = taskList.get(0).getProcessInstanceId();
                            List<Map> hisProInstance = hisProcessInstances.stream().filter(m -> m.get(PROC_INST_ID).equals(processInsId)).collect(Collectors.toList());
                            String errMsg = i18nUtil.getMessage("ERR.wf.task.errorMessage");
                            exeMsg = hisProInstance.get(0).get(BUSINESS_KEY).toString() + requestParams.get("name") + errMsg;
                            setErrorMsg(errorMsgType, exeMsg, resultObj);
                        } else {

                            String errMsg = i18nUtil.getMessage("ERR.wf.task.errorMessage");
                            exeMsg = businessKeys.get(0) + requestParams.get("name") + errMsg;
                            setErrorMsg(errorMsgType, exeMsg, resultObj);
                        }
                    } else {
                        //查找具体哪些任务不存在待办任务
                        for (Map proInstance : hisProcessInstances) {
                            boolean isExist = taskList.stream().anyMatch(m -> m.getProcessInstanceId().equals(proInstance.get(PROC_INST_ID)));
                            if (!isExist) {
                                errorBusiKeys.add(proInstance.get(BUSINESS_KEY).toString());
                            }
                        }
                        String errMsg = i18nUtil.getMessage("ERR.wf.task.errorMessage");
                        exeMsg = String.join(",", errorBusiKeys) + requestParams.get("name") + errMsg;
                        setErrorMsg(errorMsgType, exeMsg, resultObj);
                    }
                }
                if (!BootAppUtil.isNullOrEmpty(requestParams.get("batchId"))) {
                    Map countM = new HashMap();
                    failNum = businessKeys.size();
                    successNum = 0;
                    countM.put("successNum", successNum);
                    countM.put("failNum", failNum);
                    countM.put("failBusinessKeys", String.join(",", errorBusiKeys));
                    resultObj.setData(countM);
                    workFlowLogService.saveBatchProcess(requestParams, failNum, successNum, exeMsg);
                }
                return resultObj;
            }

            // 这里做一下验证，当前的待办任务节点和按钮的任务节点是否对应上（因为所有待办节点所处任务相同，所以用一个节点验证即可）
            boolean flag = checkUserTaskButton(taskList.get(0).getTaskDefinitionKey(), processDefKey, requestParams.get("code").toString());
            if (!flag) {
                //校验失败，确定是哪条业务单据
                String processInsId = taskList.get(0).getProcessInstanceId();
                List<Map> hisProInstance = hisProcessInstances.stream().filter(m -> m.get(PROC_INST_ID).equals(processInsId)).collect(Collectors.toList());
                if (!BootAppUtil.isNullOrEmpty(customUniqueFlag)) {
                    String buttonTaskDiff = i18nUtil.getMessage("ERR.wf.TaskController.buttonTaskDiff");
                    exeMsg = customUniqueFlag + requestParams.get("name") + buttonTaskDiff;
                    setErrorMsg(errorMsgType, exeMsg, resultObj);
                } else {
                    String buttonTaskDiff = i18nUtil.getMessage("ERR.wf.TaskController.buttonTaskDiff");
                    exeMsg = hisProInstance.get(0).get(BUSINESS_KEY).toString() + requestParams.get("name") + buttonTaskDiff;
                    setErrorMsg(errorMsgType, exeMsg, resultObj);
                }
                if (!BootAppUtil.isNullOrEmpty(requestParams.get("batchId"))) {
                    Map countM = new HashMap();
                    failNum = businessKeys.size();
                    successNum = 0;
                    countM.put("successNum", successNum);
                    countM.put("failNum", failNum);
                    countM.put("failBusinessKeys", String.join(",", businessKeys));
                    resultObj.setData(countM);
                    workFlowLogService.saveBatchProcess(requestParams, failNum, successNum, exeMsg);
                }
                return resultObj;
            }

            if (listenerType.equals("last")) {
                try {
                    flowableTaskService.batchCompleteTask(taskList, taskRequest, msgs, resultObj,
                            requestParams, customUniqueFlag, businessKeys,
                            businessMsgs, hisProcessInstances, failNum);
                } catch (Exception ex) {
                    String msg = "";
                    if (ex.getCause() != null && ex.getCause().getCause() != null && ex.getCause().getCause() instanceof FlowableTaskException) {
                        msg = ex.getCause().getCause().getMessage();

                    } else {
                        msg = ex.getMessage();
                    }
                    failNum = businessKeys.size();
                    String failPre = i18nUtil.getMessage("ERR.wf.task.failMessage");
                    String failMsg = customUniqueFlag + requestParams.get("name") + failPre + msg;
                    log.info("---error--"+failMsg + RandomStringUtils.randomAlphanumeric(10));
                    log.info("---error--"+processDefKey + requestParams.get("name") + failPre + msg, ex);
                    msgs.add(failMsg);
                }
            } else {
                // 构造完成任务请求
                Map<String, Object> taskValues = taskRequest.getValues();
                if (null != taskValues) {
                    taskValues.put(FlowableConstant.BUSINESS_KEYS, businessKeys);
                }
                //开始批量处理
                for (int i = 0; i < taskList.size(); i++) {
                    taskRequest.setTaskId(taskList.get(i).getId());
                    Task currentTask = taskList.get(i);
                    taskValues.put(FlowableConstant.LISTENERTYPE, listenerType);
                    String msg = "";
                    String tishiMsg = "";
                    try {
                        Map<String, Object> execMap = flowableTaskService.completeTask(taskRequest);
                        if (null != execMap && !execMap.isEmpty()) {
                            resultObj.setData(execMap);
                        }
                        log.info("执行任务:" + taskList.get(0).getTaskDefinitionKey());
                    } catch (Exception ex) {
                        if (ex.getCause() != null && ex.getCause().getCause() != null && ex.getCause().getCause() instanceof FlowableTaskException) {
                            msg = ex.getCause().getCause().getMessage();
                            if (msg.indexOf(FlowableConstant.UPDATE_BUSI_TIMEOUT) > 0) {
                                // 业务资源占用中，请稍后刷新重试！
                                tishiMsg = i18nUtil.getMessage("ERR.wf.FlowableConstant.busiSourceUsing");
                            } else {
                                tishiMsg = msg;
                            }
                        } else {
                            msg = ex.getMessage();
                            if (msg.indexOf(FlowableConstant.UPDATE_WF_TIMEOUT) > 0)
                                // 资源占用中，请稍后刷新重试！
                                tishiMsg = i18nUtil.getMessage("ERR.wf.FlowableConstant.sourceUsing");
                            else
                                tishiMsg = msg;
                        }
                        failNum += 1;
                        Map<String, Object> processInsVariables = new HashMap<>();
                        if (null != requestParams.get(FlowableConstant.TASK_ATTACH_CONDITION) && requestParams.get(FlowableConstant.TASK_ATTACH_CONDITION) instanceof Map) {
                            Map attachVariable = (Map) requestParams.get(FlowableConstant.TASK_ATTACH_CONDITION);
                            if (null != attachVariable.get(FlowableConstant.CUSTOM_UNIQUE_FLAG)) {
                                customUniqueFlag = attachVariable.get(FlowableConstant.CUSTOM_UNIQUE_FLAG).toString();
                            }
                        }
                        //获得流程变量
                        try {
                            ProcessInstance rpi = runtimeService    //与正在的任务相关的Service
                                    .createProcessInstanceQuery()    //创建流程实例查询对象
                                    .processInstanceId(currentTask.getProcessInstanceId())
                                    .singleResult();     //返回唯一结果集
                            failBusiKeys.add(rpi.getBusinessKey());
                            if (rpi == null) {
                                List<HistoricVariableInstance> hisVariablelist = historyService.createHistoricVariableInstanceQuery().processInstanceId(currentTask.getProcessInstanceId()).list();
                                for (HistoricVariableInstance hisVariable : hisVariablelist) {
                                    processInsVariables.put(hisVariable.getVariableName(), hisVariable.getValue());
                                }
                            } else {
                                processInsVariables = runtimeService.getVariables(currentTask.getProcessInstanceId());
                            }
                        } catch (Exception exception) {
                            log.info("获取流程变量失败：", exception);
                        }
                        String failMsg = "";
                        if (null != processInsVariables.get(FlowableConstant.CUSTOM_UNIQUE_FLAG)) {
                            customUniqueFlag = processInsVariables.get(FlowableConstant.CUSTOM_UNIQUE_FLAG).toString();
                        }
                        String failPre = i18nUtil.getMessage("ERR.wf.task.failMessage");
                        if (!BootAppUtil.isNullOrEmpty(customUniqueFlag)) {
                            failMsg = customUniqueFlag + requestParams.get("name") + failPre + tishiMsg;
                            msgs.add(failMsg);
                        } else {
                            String processInsId = currentTask.getProcessInstanceId();
                            List<Map> hisProInstance = hisProcessInstances.stream().filter(m -> m.get(PROC_INST_ID).equals(processInsId)).collect(Collectors.toList());
                            failMsg = hisProInstance.get(0).get(BUSINESS_KEY).toString() + requestParams.get("name") + failPre + tishiMsg;
                            msgs.add(failMsg);
                        }
                        log.info("---error--"+failMsg + RandomStringUtils.randomAlphanumeric(10));
                        log.info("---error--"+processDefKey + requestParams.get("name") + failPre + msg, ex);
                    }
                }
            }
        }
        if (businessKeys.size() == 1 && msgs.size() == 1) {
            msgBuffer.append(msgs.get(0));
        } else {
            for (String msg : msgs) {
                msgBuffer.append("^");
                msgBuffer.append(msg);
            }
        }
        if (failNum == 0) {
            exeMsg = requestParams.get("name") +" "+ i18nUtil.getMessage("MSG.wf.common.success");
            resultObj.setMsg(exeMsg);
        } else {
            if (businessKeys.size() == 1 && msgs.size() == 1) {
                exeMsg = msgBuffer.toString();
                setErrorMsg(errorMsgType, exeMsg, resultObj);
            } else {
                //failNum + "个任务" + requestParams.get("name") + "失败！"
                String taskFailPrompt = i18nUtil.getMessage("ERR.wf.TaskController.taskFailPrompt"
                        , failNum.toString(), StrUtil.toString(requestParams.get("name")));
                exeMsg = taskFailPrompt + msgBuffer.toString();
                setErrorMsg(errorMsgType, exeMsg, resultObj);
            }
        }
        successNum = businessKeys.size() - failNum;
        if (resultObj.getData() != null && resultObj.getData() instanceof Map) {
            Map<String, Object> results = (Map<String, Object>) resultObj.getData();
            results.put("successNum", successNum);
            results.put("failNum", failNum);
            results.put("failBusinessKeys", String.join(",", failBusiKeys));
            resultObj.setData(results);
        } else {
            Map countM = new HashMap();
            countM.put("successNum", successNum);
            countM.put("failNum", failNum);
            countM.put("failBusinessKeys", String.join(",", failBusiKeys));
            resultObj.setData(countM);
        }
        if (!BootAppUtil.isNullOrEmpty(requestParams.get("batchId"))) {
            workFlowLogService.saveBatchProcess(requestParams, failNum, successNum, exeMsg);
        }
        return resultObj;
    }

    @PostMapping(value = "/completeByButton")
    @ApiOperation(value = "按钮完成任务", notes = "[author:10027705]")
    public JsonResultVo completeByButton(@RequestBody Map<String, Object> requestParams) {
        String processDefKey = requestParams.get(FlowableConstant.PROCESS_DEFINITION_KEY).toString();
        List<String> businessKeys = (List<String>) requestParams.get(BUSINESS_KEYS);
        JsonResultVo resultObj = new JsonResultVo();
        if (!businessKeys.isEmpty() && businessKeys.size() == 1) {
            log.debug("执行completeByButton businessKeys:" + businessKeys.get(0) + " buttonCode:" + requestParams.get("code"));
            String repeatFlag;
            String message = i18nUtil.getMessage("ERR.wf.TaskController.processing");
            String repeatMsg = message;
            if (null != requestParams.get(FlowableConstant.REPEAT_VALID_FLAG)) {
                repeatFlag = requestParams.get(FlowableConstant.REPEAT_VALID_FLAG).toString();
                String sysRepeatValidFlag = "false";
                JsonResultVo paraResult = sysFeignRemote.getSysParamValue(BootAppUtil.getLoginKey().getTenantId(), BootAppUtil.getLoginKey().getCompanyId(), "REPEATVALIDFLAG");
                if (paraResult.getData() != null && !((ArrayList<LinkedHashMap>) paraResult.getData()).isEmpty()) {
                    sysRepeatValidFlag = ((ArrayList<LinkedHashMap>) paraResult.getData()).get(0).get("vparavalue").toString();
                    if (repeatFlag.equals("true") && sysRepeatValidFlag.equals("true")) {
                        if (BootAppUtil.isnotNullOrEmpty(requestParams.get(FlowableConstant.REPEAT_MSG).toString()))
                            repeatMsg = requestParams.get(FlowableConstant.REPEAT_MSG).toString();
                        String lock = buildLock(processDefKey, businessKeys.get(0), LoginUtil.getOperatorId());
                        try {
                            log.debug(lock + "进入重复提交验证...");
                            if (redissonLock.tryLock(lock, 3, 10)) {
                                resultObj = completeByButton(requestParams, processDefKey, businessKeys);
                            } else {
                                log.info("completeByButton获取锁失败lock：" + lock);
                                resultObj.setMsgErr(repeatMsg);
                                return resultObj;
                            }
                        } catch (Exception ex) {
                            throw ex;
                        } finally {
                            redissonLock.unlock(lock);
                        }
                    } else {
                        resultObj = completeByButton(requestParams, processDefKey, businessKeys);
                    }
                } else {
                    resultObj = completeByButton(requestParams, processDefKey, businessKeys);
                }
            } else {
                resultObj = completeByButton(requestParams, processDefKey, businessKeys);
            }
        } else {
            resultObj = completeByButton(requestParams, processDefKey, businessKeys);
        }
        return resultObj;
    }

    /**
     * 設置錯誤消息
     *
     * @param errorMsgType
     * @param errorMsg
     * @param resultObj
     */
    private void setErrorMsg(String errorMsgType, String errorMsg, JsonResultVo resultObj) {
        resultObj.setMsgErr(errorMsg);
        if (errorMsgType.equals(FlowableConstant.MSG_NO_PROMPT))
            resultObj.setCode(JsonResultVo.CODE_ERR_NO_PROMPT);// 设置特殊错误代码。前端做逻辑判断使用
    }

    /**
     * 处理会签一票否决-退回上一节点
     *
     * @param processDefKey
     * @param commonButtons
     * @return
     */
    @SuppressWarnings("squid:UnusedPrivateMethod")
    private JsonResultVo counterSignRejectHandle(String processDefKey, String processInstanceId, String
            taskDefinitionKey, String taskID,
                                                 List<ProcessCommonButtonsDO> commonButtons) {
        JsonResultVo resultObj = new JsonResultVo();
        resultObj.setCode(JsonResultVo.CODE_ERR);
        //定义查询构造器
        QmQueryWrapper<ProcessCounterSignDO> queryWrapper = new QmQueryWrapper<>();
        //人工任务名称
        queryWrapper.eq("taskKey", taskDefinitionKey);
        //流程定义Key 对应bpmn中process的ID
        queryWrapper.eq("processDefKey", processDefKey);
        //会签类型，（比例通过制、一票否决制、自定义）
        queryWrapper.eq("type", "02");
        List<ProcessCounterSignDO> processCounterSignDOS = processCounterSignService.list(queryWrapper);
        if (CollectionUtils.isEmpty(processCounterSignDOS)) {
            return resultObj;
        }
        // 会签策略不为空，开始取出当前执行的按钮，和会签策略对比，看是否是一票否决
        ProcessCommonButtonsDO button = CollectionUtils.isNotEmpty(commonButtons) ? commonButtons.get(0) : new ProcessCommonButtonsDO();
        // 查询所有待办任务(可能是并行网关进来的)
        TaskQuery taskQuery = taskService.createTaskQuery();
        taskQuery.processInstanceId(processInstanceId);
        Class<? extends IListWrapper> taskTodoListWrapper = TaskTodoListWrapper.class;
        List<Task> taskListSearch = taskQuery.list();
        IListWrapper listWrapper = SpringContextUtils.getBean(taskTodoListWrapper);
        List<TaskResponse> taskList = listWrapper.execute(taskListSearch);
        // 拒绝按钮判断
        if (CollectionUtils.isNotEmpty(taskList) && !BootAppUtil.isNullOrEmpty(button.getButtonType()) && "reject".equals(button.getButtonType())) {
            // 寻找一票否决会签策略
            for (ProcessCounterSignDO processCounterSignDO : processCounterSignDOS) {
                if ("02".equals(processCounterSignDO.getType())) {
                    // 02就是一票否决，找到他了，给我回退上一节点
                    // 根据当前节点ID查询可退回的节点
                    List<FlowNodeResponse> datas = flowableTaskService.getBackNodes(taskID);
                    FlowNodeResponse node = datas.get(datas.size() - 1);
                    // 执行退回
                    TaskResponse response = taskList.get(0);
                    TaskRequest req = new TaskRequest();
                    req.setTaskId(response.getId());
                    req.setActivityId(node.getNodeId());
                    req.setActivityName(node.getNodeName());
                    String message = i18nUtil.getMessage("MSG.wf.CommentTypeEnum.return");
                    req.setMessage(message);
                    flowableTaskService.backTask(req);
                    // 删除轨迹
                    Set<String> deleteSets = handleHistoryNode(processInstanceId, taskList);
                    for (String deleteKey : deleteSets) {
                        deleteActivity(deleteKey, processInstanceId);
                    }
                    // 流程变量重置
                    Map<String, Object> processInsVariables = runtimeService.getVariables(processInstanceId);
                    processInsVariables.put(processCounterSignDO.getGrpName() + "_" + FlowableConstant.BUTTONTYPE_REJECT, 0);
                    processInsVariables.put(processCounterSignDO.getGrpName() + "_" + FlowableConstant.BUTTONTYPE_AGREE, 0);
                    processInsVariables.put(processCounterSignDO.getGrpName() + "_" + "nrOfCompletedInstances", 0);
                    runtimeService.setVariables(processInstanceId, processInsVariables);
                    resultObj.setCode(JsonResultVo.CODE_OK);
                    String rejectedBack = i18nUtil.getMessage("ERR.wf.TaskController.rejectedBack");
                    resultObj.setMsg(rejectedBack);
                }
            }
        }
        return resultObj;
    }

    /**
     * 处理历史节点
     *
     * @param processInstanceId 实例ID
     * @param taskList          待办任务
     */
    private Set<String> handleHistoryNode(String processInstanceId, List<TaskResponse> taskList) {
        // 要删除的节点集合
        Set<String> deleteSets = new HashSet<>();
        // 1、取其中一个待办任务
        TaskResponse task = taskList.get(0);
        // 2、查询bpmnmodel
        ExecutionEntity executionEntity = (ExecutionEntity) processInstanceService.getProcessInstanceById(processInstanceId);
        BpmnModel bpmnModel = repositoryService.getBpmnModel(executionEntity.getProcessDefinitionId());
        // 3、上溯找到前并行网关
        FlowNode flowNode = (FlowNode) bpmnModel.getFlowElement(task.getTaskDefinitionKey());
        //  获取节点入线
        SequenceFlow sequenceFlow = flowNode.getIncomingFlows().get(0);
        //  通过入线获取来源，找到前并行网关
        FlowNode beforeParallelGatewayNode = (FlowNode) bpmnModel.getFlowElement(sequenceFlow.getSourceRef());
        deleteSets.add(beforeParallelGatewayNode.getIncomingFlows().get(0).getId()); // 网关节点前的线
        deleteSets.add(beforeParallelGatewayNode.getId()); // 前并行网关
        //  后并行网关
        deleteSets.add(flowNode.getOutgoingFlows().get(0).getTargetRef());
        // 4、通过前并行网关的出线，找到所有并行任务
        List<SequenceFlow> parallelGatewayOuts = beforeParallelGatewayNode.getOutgoingFlows();
        for (SequenceFlow parallelGatewayOut : parallelGatewayOuts) {
            // 对比看任务节点是否在待办任务中，不在需要删除任务
            boolean exists = true;
            for (TaskResponse taskResponse : taskList) {
                if (taskResponse.getTaskDefinitionKey().equals(parallelGatewayOut.getTargetRef())) {
                    exists = false;
                }
            }
            if (exists) {
                // 删除任务

            }
            // 任务自身的节点ID，前后线的节点ID
            deleteSets.add(parallelGatewayOut.getTargetRef());
            deleteSets.add(parallelGatewayOut.getId());
            FlowNode taskNode = (FlowNode) bpmnModel.getFlowElement(parallelGatewayOut.getTargetRef());
            deleteSets.add(taskNode.getOutgoingFlows().get(0).getId());
        }
        return deleteSets;
    }

    protected void deleteActivity(String disActivityId, String processInstanceId) {
        String tableName = managementService.getTableName(ActivityInstanceEntity.class);
        String sql = "select t.* from " + tableName + " t where t.PROC_INST_ID_=#{processInstanceId} and t.ACT_ID_ = #{disActivityId} " +
                " order by t.END_TIME_ ASC";
        List<ActivityInstance> disActivities = runtimeService.createNativeActivityInstanceQuery().sql(sql)
                .parameter("processInstanceId", processInstanceId)
                .parameter("disActivityId", disActivityId).list();
        //删除运行时和历史节点信息
        if (CollectionUtils.isNotEmpty(disActivities)) {
            ActivityInstance activityInstance = disActivities.get(0);
            sql = "select t.* from " + tableName + " t where t.PROC_INST_ID_=#{processInstanceId} and (t.END_TIME_ >= #{endTime} or t.END_TIME_ is null)";
            List<ActivityInstance> datas = runtimeService.createNativeActivityInstanceQuery().sql(sql).parameter("processInstanceId", processInstanceId)
                    .parameter("endTime", activityInstance.getEndTime()).list();
            List<String> runActivityIds = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(datas)) {
                datas.forEach(ai -> runActivityIds.add(ai.getId()));
                runFlowableActinstDao.deleteRunActinstsByIds(runActivityIds);
                hisFlowableActinstDao.deleteHisActinstsByIds(runActivityIds);
            }
        }
    }

    public void addComment(String taskId, String processInstanceId, String userId, String type,
                           String message) {
        Authentication.setAuthenticatedUserId(userId);
        type = type == null ? CommentTypeEnum.SP.getName() : type;
        message = (message == null || message.length() == 0) ? "" : message;
        taskService.addComment(taskId, processInstanceId, type, message);
    }

    /**
     * @description 校验按钮是否是当前任务的
     * <AUTHOR>
     * @date 2020/9/16 8:37
     */
    private boolean checkUserTaskButton(String taskId, String processDefKey, String buttonCode) {
        //这里添加redis缓存
        boolean flag = false;
        String key = redisUtils.keyBuilder(Constants.MODULE, Constants.BUTTON_BELONG_TASK, processDefKey, taskId, buttonCode);
        Object resultFlag = redisUtils.get(key);
        //存在，直接获取并返回，不存在，则计算
        if (!BootAppUtil.isNullOrEmpty(resultFlag)) {
            flag = (boolean) resultFlag;
        } else {
            ProcessDefinition p = processDefinitionService.getProcessDefinitionByPKey(processDefKey);
            if (!BootAppUtil.isNullOrEmpty(p)) {
                //通过流程定义ID获取流程模板
                BpmnModel bpmnModel = repositoryService.getBpmnModel(p.getId());
                if (bpmnModel != null) {
                    // 获取模板对应的流程
                    List<Process> processs = bpmnModel.getProcesses();
                    for (Process process : processs) {
                        // 获取人工任务节点
                        Collection<UserTask> flowElements = process.findFlowElementsOfType(UserTask.class);
                        // 循环人工任务
                        for (UserTask userTask : flowElements) {
                            if (userTask.getId().equals(taskId)) {
                                List<ExtensionElement> elements = userTask.getExtensionElements().get("button");
                                if (!BootAppUtil.isNullOrEmpty(elements)) {
                                    for (ExtensionElement button : elements) {
                                        String code = button.getAttributes().get("code").get(0).getValue();
                                        if (buttonCode.equals(code)) {
                                            flag = true;
                                            break;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            //存入缓存
            redisUtils.set(key, flag, 600);
        }
        return flag;
    }

    @ApiOperation(value = "结束流程实例", notes = "[author:10027705]")
    @PostMapping(value = "/stopProcessInstance")
    public JsonResultVo stopProcessInstance(@RequestBody TaskRequest taskRequest) {
        JsonResultVo resultObj = new JsonResultVo();
        flowableTaskService.stopProcessInstance(taskRequest);
        return resultObj;
    }

    @ApiOperation(value = "结束流程实例", notes = "[author:10027705]")
    @PostMapping(value = "/stopProcessInstanceByBusinessKey")
    public JsonResultVo stopProcessInstanceByBusinessKey(@RequestBody Map<String, Object> requestParams) {
        JsonResultVo resultObj = new JsonResultVo();
        //这里如果传的是businesskey和流程定义key，需要查找任务id
        List<String> businessKeys = (List<String>) requestParams.get(BUSINESS_KEYS);
        if (!businessKeys.isEmpty()) {
            log.debug("执行completeByButton businessKeys:" + businessKeys.get(0) + " buttonCode:" + requestParams.get("code"));
        }
        String processDefKey = requestParams.get(FlowableConstant.PROCESS_DEFINITION_KEY).toString();
        //业务单据历史流程实例批量查询
        List<Map> hisProcessInstances = flowableTaskService.hisProcInsQueryByBusinessKeys(processDefKey, businessKeys);
        //从list集合中，取出字段name的列表
        List<String> processInstanceIds = hisProcessInstances.stream().map(d -> d.get(PROC_INST_ID).toString()).collect(Collectors.toList());
        // 查询待办节点
        TaskQuery taskQuery = taskService.createTaskQuery();
        taskQuery.processInstanceIdIn(processInstanceIds);
        //批量获取待办任务
        List<Task> taskList = taskQuery.list();
        TaskRequest taskRequest = new TaskRequest();
        for (Task task : taskList) {
            taskRequest.setTaskId(task.getId());
        }
        if (!BootAppUtil.isNullOrEmpty(requestParams.get(MESSAGE))) {
            taskRequest.setMessage(requestParams.get(MESSAGE).toString());
        }
        if (null != requestParams.get("name")) {
            taskRequest.setActivityName(requestParams.get("name").toString());
            taskRequest.setOprType(requestParams.get("name").toString());
        }
        flowableTaskService.stopProcessInstance(taskRequest);
        return resultObj;
    }

    @GetMapping(value = "/renderedTaskForm")
    @ApiOperation(value = "任务格式", notes = "[author:10027705]")
    public JsonResultVo renderedTaskForm(@RequestParam String taskId) {
        JsonResultVo resultObj = new JsonResultVo();
        permissionService.validateReadPermissionOnTask2(taskId, LoginUtil.getOperatorId(), true, true);
        Object renderedTaskForm = formService.getRenderedTaskForm(taskId);
        resultObj.setData(EscapeUnescape.escape(renderedTaskForm.toString()));
        return resultObj;
    }

    @GetMapping(value = "/getTaskButton")
    @ApiOperation(value = "获取任务按钮", notes = "[author:10027705]")
    public JsonResultVo getTaskButton(@RequestParam String taskId) {
        JsonResultVo resultObj = new JsonResultVo();
        Object renderedTaskForm = flowableTaskService.getTaskButtons(taskId, "");
        resultObj.setData(renderedTaskForm);
        return resultObj;
    }

    @GetMapping(value = "/executeTaskData")
    @ApiOperation(value = "执行任务数据", notes = "[author:10027705]")
    public JsonResultVo executeTaskData(@RequestParam String taskId) {
        JsonResultVo resultObj = new JsonResultVo();
        Task task = permissionService.validateReadPermissionOnTask2(taskId, LoginUtil.getOperatorId(), true, true);
        String startFormKey = formService.getStartFormKey(task.getProcessDefinitionId());
        String taskFormKey = formService.getTaskFormKey(task.getProcessDefinitionId(), task.getTaskDefinitionKey());
        Object renderedStartForm = formService.getRenderedStartForm(task.getProcessDefinitionId());
        Object renderedTaskForm = formService.getRenderedTaskForm(taskId);
        Map<String, Object> variables = runtimeService.getVariables(task.getProcessInstanceId());
        String formUrl = "";
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                .processInstanceId(task.getProcessInstanceId()).singleResult();
        Map<String, Object> ret = new HashMap<>(7);
        ret.put("startUserId", processInstance.getStartUserId());
        ret.put("startFormKey", startFormKey);
        ret.put("taskFormKey", taskFormKey);
        if (renderedStartForm != null) {
            ret.put("renderedStartForm", EscapeUnescape.escape(renderedStartForm.toString()));
            if (null != renderedTaskForm)
                ret.put("renderedTaskForm", EscapeUnescape.escape(renderedTaskForm.toString()));
            if (null != variables.get(FlowableConstant.PROCESS_INSTANCE_FORM_DATA)) {
                variables.put(FlowableConstant.PROCESS_INSTANCE_FORM_DATA, EscapeUnescape.escape(variables.get(FlowableConstant.PROCESS_INSTANCE_FORM_DATA).toString()));
            }
            ret.put("variables", variables);
        } else {
            String processDefPkey = processInstance.getProcessDefinitionKey();
            WfConf wfConf = wfConfService.getWfConfByWF(processDefPkey);
            if (wfConf != null) {
                formUrl = wfConf.getVbillRoute();
            }
            ret.put("variables", variables);
            ret.put("formUrl", formUrl);
        }
        boolean showBusinessKey = isShowBusinessKey(task.getProcessDefinitionId());
        ret.put("showBusinessKey", showBusinessKey);
        ret.put(FlowableConstant.BUSINESS_KEY, processInstance.getBusinessKey());
        // 当前任务是发起者
        if (FlowableConstant.INITIATOR.equals(task.getTaskDefinitionKey())) {
            ret.put("isInitiator", true);
        }
        resultObj.setData(ret);
        return resultObj;
    }

    @GetMapping(value = "/backNodes")
    @ApiOperation(value = "后退节点", notes = "[author:10027705]")
    public JsonResultVo backNodes(@RequestParam String taskId) {
        JsonResultVo resultObj = new JsonResultVo();
        List<FlowNodeResponse> datas = flowableTaskService.getBackNodes(taskId);
        resultObj.setData(datas);
        return resultObj;
    }

    @PostMapping(value = "/backNodesByButton")
    @ApiOperation(value = "按钮完成任务", notes = "[author:10027705]")
    @SuppressWarnings("squid:S1860")
    public JsonResultVo backNodesByButton(@RequestBody Map<String, Object> requestParams) {
        long start1;
        long end1;
        start1 = System.currentTimeMillis();
        JsonResultVo resultObj = new JsonResultVo();
        List<String> businessKeys = (List<String>) requestParams.get(BUSINESS_KEYS);
        String processDefKey = requestParams.get(FlowableConstant.PROCESS_DEFINITION_KEY).toString();
        String taskKey = "";
        boolean hasComment = true;
        boolean deleteLastComment = false;
        if (null != requestParams.get(FlowableConstant.TASK_ID)) {
            taskKey = requestParams.get(FlowableConstant.TASK_ID).toString();
        }
        if (null != requestParams.get("hasComment")) {
            hasComment = Boolean.parseBoolean(requestParams.get("hasComment").toString());
        }
        if (null != requestParams.get("deleteLastComment")) {
            deleteLastComment = Boolean.parseBoolean(requestParams.get("deleteLastComment").toString());
        }
        //这里需要根据当前taskkey与按钮找线的目标所指
        for (int i = 0; i < businessKeys.size(); i++) {
            String businessKey = businessKeys.get(i);
            // 根据流程定义key和businessKey查询当前实例
            HistoricProcessInstanceQuery query = historyService.createHistoricProcessInstanceQuery();
            query.processDefinitionKey(processDefKey);
            query.processInstanceBusinessKey(businessKey);
            List<HistoricProcessInstance> procInsListSearch = query.list();
            List<HistoricProcessInstanceResponse> procInsList = new ArrayList<>();
            Class<? extends IListWrapper> procInsListWrapperClass = ProcInsListWrapper.class;
            // 同步查询流程实例，防止并发
            synchronized (businessKey) {
                if (procInsListWrapperClass != null) {
                    IListWrapper listWrapper = SpringContextUtils.getBean(procInsListWrapperClass);
                    procInsList = listWrapper.execute(procInsListSearch);
                }
            }
            // 先判断实例，再根据实例查询当前待办节点任务
            if (CollectionUtils.isNotEmpty(procInsList)) {
                // 查询待办节点
                HistoricProcessInstanceResponse instance = procInsList.get(0);
                TaskQuery taskQuery = taskService.createTaskQuery();
                taskQuery.processInstanceId(instance.getId());
                Class<? extends IListWrapper> taskTodoListWrapper = TaskTodoListWrapper.class;
                List<Task> taskListSearch = taskQuery.list();
                List<TaskResponse> taskList = new ArrayList<>();
                if (taskTodoListWrapper != null) {
                    IListWrapper listWrapper = SpringContextUtils.getBean(taskTodoListWrapper);
                    taskList = listWrapper.execute(taskListSearch);
                }
                if (!taskList.isEmpty()) {
                    TaskRequest taskRequest = new TaskRequest();
                    taskRequest.setTaskId(taskList.get(0).getId());
                    if (null != requestParams.get("code")) {
                        taskRequest.setActivityId(requestParams.get("code").toString().substring(2));
                    }
                    if (null != requestParams.get("name")) {
                        taskRequest.setActivityName(requestParams.get("name").toString());
                        taskRequest.setOprType(requestParams.get("name").toString());
                    }
                    taskRequest.setActivityId(taskKey);
                    taskRequest.setNoMessage(true);
                    taskRequest.setHasComment(hasComment);
                    taskRequest.setDeleteLastComment(deleteLastComment);
                    if (ObjectUtils.isNotEmpty(requestParams.get(MESSAGE))) {
                        taskRequest.setMessage(requestParams.get(MESSAGE).toString());
                    }
                    if (ObjectUtils.isNotEmpty(requestParams.get("targetActivityId"))) {
                        taskRequest.setActivityId(requestParams.get("targetActivityId").toString());
                    }
                    if (ObjectUtils.isNotEmpty(requestParams.get("sourceActivityId"))) {
                        taskRequest.setSourceActivityId(requestParams.get("sourceActivityId").toString());
                    }
                    flowableTaskService.backTask(taskRequest);
                }
            }

        }
        end1 = System.currentTimeMillis();
        printLog("backNodesByButton", start1, end1);
        return resultObj;
    }


    @ApiOperation(value = "退户任务", notes = "[author:10027705]")
    @PostMapping(value = "/back")
    public JsonResultVo back(@RequestBody TaskRequest taskRequest) {
        JsonResultVo resultObj = new JsonResultVo();
        flowableTaskService.backTask(taskRequest);
        return resultObj;
    }



    @PostMapping(value = "/getNextTaskDetail")
    @ApiOperation(value = "下一步操作清单", notes = "[author:10027705]")
    public JsonResultVo getNextTaskDetail(@RequestBody Map<String, Object> requestParams) {
        log.info("--下一步操作清单--请求参数--requestParams--"+requestParams);
        //processDefinitionKey
        String processDefKey = requestParams.get("processDefKey").toString();
        //businesskey
        List<String> businessKeys = (List<String>) requestParams.get(BUSINESS_KEYS);
        List resList =new ArrayList();
        String currentStatus ="";//当前状态
        HashMap resultMap = new HashMap();

        JsonResultVo resultObj = new JsonResultVo();
        if (!BootAppUtil.isNullOrEmpty(businessKeys)) {

            //业务单据历史流程实例批量查询
            List<Map> hisProcessInstances = flowableTaskService.hisProcInsQueryByBusinessKeys(processDefKey, businessKeys);
            //从list集合中，取出字段name的列表
            List<String> processInstanceIds = hisProcessInstances.stream().map(d -> d.get(PROC_INST_ID).toString()).collect(Collectors.toList());
            log.info("-----processInstanceIds-------"+processInstanceIds);

            if (hisProcessInstances.isEmpty() || hisProcessInstances.size() < businessKeys.size()) {
                List<String> errorBusiKeys = new ArrayList<>();
                for (String businessKey : businessKeys) {
                    boolean isExist = hisProcessInstances.stream().anyMatch(m -> m.get(BUSINESS_KEY).equals(businessKey));
                    if (!isExist) {
                        errorBusiKeys.add(businessKey);
                    }
                }
                //工作流代码：" + processDefKey + " 业务主键：" + String.join(",", errorBusiKeys) + " 不存在工作流实例！
                String msg = i18nUtil.getMessage("ERR.wf.TaskController.wfcodeError", processDefKey, String.join(",", errorBusiKeys));
                log.info("---error--"+msg);
                return resultObj;
            }


            //1）根据参数获取待办任务
            log.info("-------1）根据参数获取待办任务----------");
            // 查询所有待办任务
            //查询待办节点
            TaskQuery taskQuery = taskService.createTaskQuery();
            taskQuery.processInstanceIdIn(processInstanceIds);

            /*TaskQuery taskQuery = taskService.createTaskQuery();
            taskQuery.processInstanceId(processDefKey);
            taskQuery.processInstanceBusinessKey(businessKeys);*/
            Class<? extends IListWrapper> taskTodoListWrapper = TaskTodoListWrapper.class;


            List<Task> taskListSearch = taskQuery.list();
            List<TaskResponse> taskList = new ArrayList<>();
            if (taskTodoListWrapper != null) {
                IListWrapper listWrapper = SpringContextUtils.getBean(taskTodoListWrapper);
                taskList = listWrapper.execute(taskListSearch);
            }
            log.info("--1--taskList-----"+taskList);
            /*if (!taskList.isEmpty()) {
                TaskRequest taskRequest = new TaskRequest();
                taskRequest.setTaskId(taskList.get(0).getId());
            }*/

            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                String tenantId = request.getHeader("tenantId");
                //2）根据待办任务，获取任务上的（主操作）按钮
                for (TaskResponse task : taskList) {

                    currentStatus = task.getName();

                    List buttonList = flowableTaskService.getTaskButtonsCodeBy(task.getTaskDefinitionKey(), task.getProcessDefinitionId());
                    log.info("--2--buttonList-----" + buttonList);


                    if(!buttonList.isEmpty()){

                        //3、根据processDefinitionKey，任务名，按钮名获取用户和角色。
                        TaskAuthDTO tempDTO = new TaskAuthDTO();
                        //String processDefinitionKey = task.getProcessDefinitionKey();
                        //String taskName = task.getTaskDefinitionKey();
                        Map buttonInfo = (Map) buttonList.get(0);
                        //String buttonCode =buttonInfo.get("code").toString();
                        //定义查询构造器
                        QmQueryWrapper<TaskAuthDO> queryWrapper = new QmQueryWrapper<>();
                        //拼装实体属性查询条件
                        LambdaQueryWrapper<TaskAuthDO> lambdaWrapper = queryWrapper.lambda();
                        //人工任务名称
                        if (!BootAppUtil.isNullOrEmpty(task.getTaskDefinitionKey())) {
                            lambdaWrapper.eq(TaskAuthDO::getTaskName, task.getTaskDefinitionKey());
                            tempDTO.setTaskName(task.getTaskDefinitionKey());
                        }
                        //流程定义Key 对应bpmn中process的ID
                        if (!BootAppUtil.isNullOrEmpty(task.getProcessDefinitionKey())) {
                            lambdaWrapper.eq(TaskAuthDO::getProcessDefKey, task.getProcessDefinitionKey());
                            tempDTO.setProcessDefKey(task.getProcessDefinitionKey());
                        }
                        // 按钮代码
                        if (!BootAppUtil.isNullOrEmpty(buttonInfo.get("code"))) {
                            lambdaWrapper.eq(TaskAuthDO::getButtonCode, buttonInfo.get("code"));
                            tempDTO.setButtonCode(buttonInfo.get("code").toString());
                        }
                        //查询数据，使用table函数。
                        QmPage<TaskAuthDO> actionCodelist = taskAuthService.table(queryWrapper, tempDTO);

                        log.info("--3--actionCodelist-----" + actionCodelist);
                        List<TaskAuthDO> taskAuthList = actionCodelist.getItems();
                        if (!taskAuthList.isEmpty()) {
                            log.info("--3.1----查询工作流用户授权信息----------");
                            for (TaskAuthDO taskAuthDO : taskAuthList) {
                                String actioncode = taskAuthDO.getActionCode();//参与者代码
                                String actiontype = taskAuthDO.getActionType();//用户标识， 0：用户 ； 1：角色

                                if ("0".equals(actiontype)) {
                                    log.info("---授权为用户：0-----------");
                                    //调用sys 接口查询用户信息

                                    PersonnelDTO paramDTO = new PersonnelDTO();
                                    paramDTO.setId(actioncode);//人员编码
                                    paramDTO.setVstop("0");
                                    paramDTO.setCurrentPage(1);
                                    paramDTO.setPageSize(50);
                                    JsonResultVo resultVo = sysFeignRemote.getPersonnelInfo(tenantId, paramDTO);
                                    log.info("-----resultVo------" + JSON.toJSONString(resultVo));
                                    HashMap repMap = (HashMap) resultVo.getData();
                                    List item = (List) repMap.get("items");
                                    //List item = Collections.singletonList(data.getString("items"));
                                    HashMap rperMap = (HashMap) item.get(0);
                                    log.info("--4--rperMap-----" + rperMap);

                                    HashMap tempMap = new HashMap();
                                    tempMap.put("buttonCode", buttonInfo.get("code").toString());//操作名称
                                    tempMap.put("vpersoncode", rperMap.get("vpersoncode"));//操作者代码
                                    tempMap.put("vpersonname", rperMap.get("vpersonname"));//操作者名称
                                    tempMap.put("deptname", rperMap.get("deptname"));//操作部门
                                    resList.add(tempMap);
                                } else {
                                    log.info("---授权为角色：1-----------");
                                    RoleDTO roleDTO = new RoleDTO();
                                    roleDTO.setId(actioncode);
                                    roleDTO.setVlocalcompanyflag("1");
                                    JsonResultVo roleList = sysFeignRemote.getListByRoleId(tenantId, roleDTO);
                                    if (roleList.getCode() == 500) {
                                        throw new FlowableTaskException(roleList.getMsg());
                                    }
                                    if (roleList.getData() != null) {
                                        for (LinkedHashMap roleDO : (ArrayList<LinkedHashMap>) roleList.getData()) {
                                            HashMap tempMap = new HashMap();
                                            tempMap.put("buttonCode", buttonInfo.get("code").toString());//操作名称
                                            tempMap.put("vpersoncode", roleDO.get("vpersoncode"));//操作者代码
                                            tempMap.put("vpersonname", roleDO.get("vpersonname"));//操作者名称
                                            tempMap.put("deptname", roleDO.get("vorgname"));//操作部门
                                            resList.add(tempMap);

                                        }
                                    }

                                }

                            }
                           /* resultMap.put("resList",resList);
                            resultMap.put("currentStatus",currentStatus);
                            resultObj.setData(resList);*/
                        } else {

                            List restultList = new ArrayList();
                            log.info("--3.2----无工作流授权信息，查询菜单的用户信息----------");
                            //查询事务码
                            TaskTranAuthDTO tempDTO2 = new TaskTranAuthDTO();
                            //定义查询构造器
                            QmQueryWrapper<TaskTranAuthDO> queryWrapper2 = new QmQueryWrapper<>();
                            //拼装实体属性查询条件
                            LambdaQueryWrapper<TaskTranAuthDO> lambdaWrapper2 = queryWrapper2.lambda();
                            //人工任务名称
                            if (!BootAppUtil.isNullOrEmpty(task.getTaskDefinitionKey())) {
                                lambdaWrapper2.eq(TaskTranAuthDO::getTaskName, task.getTaskDefinitionKey());
                                tempDTO2.setTaskName(task.getTaskDefinitionKey());
                            }
                            //流程定义Key 对应bpmn中process的ID
                            if (!BootAppUtil.isNullOrEmpty(task.getProcessDefinitionKey())) {
                                lambdaWrapper2.eq(TaskTranAuthDO::getProcessDefKey, task.getProcessDefinitionKey());
                                tempDTO2.setProcessDefKey(task.getProcessDefinitionKey());
                            }
                            // 按钮代码
                            if (!BootAppUtil.isNullOrEmpty(buttonInfo.get("code"))) {
                                lambdaWrapper2.eq(TaskTranAuthDO::getButtonCode, buttonInfo.get("code"));
                                tempDTO2.setButtonCode(buttonInfo.get("code").toString());
                            }
                            //查询数据，使用table函数。
                            QmPage<TaskTranAuthDO> tranlist = taskTranAuthService.table(queryWrapper2, tempDTO2);

                            log.info("--3.2.1--tranlist-----" + tranlist);
                            List<TaskTranAuthDO> trannAuthList = tranlist.getItems();
                            if (!trannAuthList.isEmpty()) {
                                log.info("--3.3----查询工作流用户授权信息----------");
                                for (TaskTranAuthDO transAuthDO : trannAuthList) {
                                    String transcode = transAuthDO.getTransCode();//事务码
                                    log.info("---4.2--通过事务码查询用户信息-----");
                                    JsonResultVo roleList = sysFeignRemote.getRoleListByTransCode(tenantId, transcode, buttonInfo.get("code").toString());
                                    if (roleList.getCode() == 500) {
                                        throw new FlowableTaskException(roleList.getMsg());
                                    }
                                    if (roleList.getData() != null) {
                                        restultList.addAll((Collection) roleList.getData());
                                    }
                                }
                            }
                            log.info("-------restultList------" + restultList.size());
                            resList = (List) restultList.stream().distinct().collect(Collectors.toList());
                            log.info("-------resList------" + resList.size());
                        }
                        resultMap.put("resList",resList);
                        resultMap.put("currentStatus",currentStatus);
                        resultObj.setData(resultMap);
                    }else{
                        log.info("---error--"+"------待办任务中无主操作按钮！-------");
                        resultObj.setMsgErr("待办任务中无主操作按钮！");
                    }

                }
            }
        }

        return resultObj;
    }
}
