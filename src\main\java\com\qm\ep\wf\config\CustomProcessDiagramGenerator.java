package com.qm.ep.wf.config;

import com.qm.ep.wf.domain.vo.CustomProcessDiagramCanvasVO;
import com.qm.ep.wf.domain.vo.CustomProcessDiagramGeneratorVO;
import org.flowable.bpmn.model.Process;
import org.flowable.bpmn.model.*;
import org.flowable.image.impl.DefaultProcessDiagramGenerator;

import java.awt.*;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> @date
 */
public class CustomProcessDiagramGenerator extends DefaultProcessDiagramGenerator {

    public CustomProcessDiagramGenerator() {
        super(1.0);
    }

    public InputStream generateCustomDiagram(CustomProcessDiagramGeneratorVO vo) {
        return generateCustomProcessDiagram(vo).generateImage(vo.getImageType());
    }

    protected CustomProcessDiagramCanvas generateCustomProcessDiagram(CustomProcessDiagramGeneratorVO vo) {

        prepareBpmnModel(vo.getBpmnModel());
        CustomProcessDiagramCanvas processDiagramCanvas = initCustomProcessDiagramCanvas(vo);

        // Draw pool shape, if process is participant in collaboration
        for (Pool pool : vo.getBpmnModel().getPools()) {
            GraphicInfo graphicInfo = vo.getBpmnModel().getGraphicInfo(pool.getId());
            processDiagramCanvas.drawPoolOrLane(pool.getName(), graphicInfo, vo.getScaleFactor());
        }

        // Draw lanes
        for (Process process : vo.getBpmnModel().getProcesses()) {
            for (Lane lane : process.getLanes()) {
                GraphicInfo graphicInfo = vo.getBpmnModel().getGraphicInfo(lane.getId());
                processDiagramCanvas.drawPoolOrLane(lane.getName(), graphicInfo, vo.getScaleFactor());
            }
        }

        // Draw activities and their sequence-flows
        for (Process process : vo.getBpmnModel().getProcesses()) {
            for (FlowNode flowNode : process.findFlowElementsOfType(FlowNode.class)) {
                if (!isPartOfCollapsedSubProcess(flowNode, vo.getBpmnModel())) {
                    drawCustomActivity(processDiagramCanvas, flowNode, vo);
                }
            }
        }

        // Draw artifacts
        for (Process process : vo.getBpmnModel().getProcesses()) {

            for (Artifact artifact : process.getArtifacts()) {
                drawArtifact(processDiagramCanvas, vo.getBpmnModel(), artifact);
            }

            List<SubProcess> subProcesses = process.findFlowElementsOfType(SubProcess.class, true);
            if (subProcesses != null) {
                for (SubProcess subProcess : subProcesses) {

                    GraphicInfo graphicInfo = vo.getBpmnModel().getGraphicInfo(subProcess.getId());
                    if (graphicInfo != null && graphicInfo.getExpanded() != null) {
                        boolean expanded = graphicInfo.getExpanded();
                        if (!expanded) {
                            continue;
                        }

                    }

                    if (!isPartOfCollapsedSubProcess(subProcess, vo.getBpmnModel())) {
                        for (Artifact subProcessArtifact : subProcess.getArtifacts()) {
                            drawArtifact(processDiagramCanvas, vo.getBpmnModel(), subProcessArtifact);
                        }
                    }
                }
            }
        }

        return processDiagramCanvas;
    }

    protected static CustomProcessDiagramCanvas initCustomProcessDiagramCanvas(CustomProcessDiagramGeneratorVO vo) {

        // We need to calculate maximum values to know how big the image will be in its entirety
        double minX = Double.MAX_VALUE;
        double maxX = 0;
        double minY = Double.MAX_VALUE;
        double maxY = 0;

        for (Pool pool : vo.getBpmnModel().getPools()) {
            GraphicInfo graphicInfo = vo.getBpmnModel().getGraphicInfo(pool.getId());
            minX = graphicInfo.getX();
            maxX = graphicInfo.getX() + graphicInfo.getWidth();
            minY = graphicInfo.getY();
            maxY = graphicInfo.getY() + graphicInfo.getHeight();
        }

        List<FlowNode> flowNodes = gatherAllFlowNodes(vo.getBpmnModel());
        for (FlowNode flowNode : flowNodes) {

            GraphicInfo flowNodeGraphicInfo = vo.getBpmnModel().getGraphicInfo(flowNode.getId());

            // width
            if (flowNodeGraphicInfo.getX() + flowNodeGraphicInfo.getWidth() > maxX) {
                maxX = flowNodeGraphicInfo.getX() + flowNodeGraphicInfo.getWidth();
            }
            if (flowNodeGraphicInfo.getX() < minX) {
                minX = flowNodeGraphicInfo.getX();
            }
            // height
            if (flowNodeGraphicInfo.getY() + flowNodeGraphicInfo.getHeight() > maxY) {
                maxY = flowNodeGraphicInfo.getY() + flowNodeGraphicInfo.getHeight();
            }
            if (flowNodeGraphicInfo.getY() < minY) {
                minY = flowNodeGraphicInfo.getY();
            }

            for (SequenceFlow sequenceFlow : flowNode.getOutgoingFlows()) {
                List<GraphicInfo> graphicInfoList = vo.getBpmnModel().getFlowLocationGraphicInfo(sequenceFlow.getId());
                if (graphicInfoList != null) {
                    for (GraphicInfo graphicInfo : graphicInfoList) {
                        // width
                        if (graphicInfo.getX() > maxX) {
                            maxX = graphicInfo.getX();
                        }
                        if (graphicInfo.getX() < minX) {
                            minX = graphicInfo.getX();
                        }
                        // height
                        if (graphicInfo.getY() > maxY) {
                            maxY = graphicInfo.getY();
                        }
                        if (graphicInfo.getY() < minY) {
                            minY = graphicInfo.getY();
                        }
                    }
                }
            }
        }

        List<Artifact> artifacts = gatherAllArtifacts(vo.getBpmnModel());
        for (Artifact artifact : artifacts) {

            GraphicInfo artifactGraphicInfo = vo.getBpmnModel().getGraphicInfo(artifact.getId());

            if (artifactGraphicInfo != null) {
                // width
                if (artifactGraphicInfo.getX() + artifactGraphicInfo.getWidth() > maxX) {
                    maxX = artifactGraphicInfo.getX() + artifactGraphicInfo.getWidth();
                }
                if (artifactGraphicInfo.getX() < minX) {
                    minX = artifactGraphicInfo.getX();
                }
                // height
                if (artifactGraphicInfo.getY() + artifactGraphicInfo.getHeight() > maxY) {
                    maxY = artifactGraphicInfo.getY() + artifactGraphicInfo.getHeight();
                }
                if (artifactGraphicInfo.getY() < minY) {
                    minY = artifactGraphicInfo.getY();
                }
            }

            List<GraphicInfo> graphicInfoList = vo.getBpmnModel().getFlowLocationGraphicInfo(artifact.getId());
            if (graphicInfoList != null) {
                for (GraphicInfo graphicInfo : graphicInfoList) {
                    // width
                    if (graphicInfo.getX() > maxX) {
                        maxX = graphicInfo.getX();
                    }
                    if (graphicInfo.getX() < minX) {
                        minX = graphicInfo.getX();
                    }
                    // height
                    if (graphicInfo.getY() > maxY) {
                        maxY = graphicInfo.getY();
                    }
                    if (graphicInfo.getY() < minY) {
                        minY = graphicInfo.getY();
                    }
                }
            }
        }

        int nrOfLanes = 0;
        for (Process process : vo.getBpmnModel().getProcesses()) {
            for (Lane l : process.getLanes()) {

                nrOfLanes++;

                GraphicInfo graphicInfo = vo.getBpmnModel().getGraphicInfo(l.getId());
                // // width
                if (graphicInfo.getX() + graphicInfo.getWidth() > maxX) {
                    maxX = graphicInfo.getX() + graphicInfo.getWidth();
                }
                if (graphicInfo.getX() < minX) {
                    minX = graphicInfo.getX();
                }
                // height
                if (graphicInfo.getY() + graphicInfo.getHeight() > maxY) {
                    maxY = graphicInfo.getY() + graphicInfo.getHeight();
                }
                if (graphicInfo.getY() < minY) {
                    minY = graphicInfo.getY();
                }
            }
        }

        // Special case, see https://activiti.atlassian.net/browse/ACT-1431
        if (flowNodes.isEmpty() && vo.getBpmnModel().getPools().isEmpty() && nrOfLanes == 0) {
            // Nothing to show
            minX = 0;
            minY = 0;
        }
        CustomProcessDiagramCanvasVO canvasVO = new CustomProcessDiagramCanvasVO();
        canvasVO.setWidth((int) (maxX + 10));
        canvasVO.setHeight((int) (maxY + 10));
        canvasVO.setMinX((int) minX);
        canvasVO.setMinY((int) minY);
        canvasVO.setImageType(vo.getImageType());
        canvasVO.setActivityFontName(vo.getActivityFontName());
        canvasVO.setLabelFontName(vo.getLabelFontName());
        canvasVO.setAnnotationFontName(vo.getAnnotationFontName());
        canvasVO.setCustomClassLoader(vo.getCustomClassLoader());

        return new CustomProcessDiagramCanvas(canvasVO);
    }

    protected void drawCustomActivity(CustomProcessDiagramCanvas canvas, FlowNode flowNode, CustomProcessDiagramGeneratorVO vo) {

//        CustomProcessDiagramCanvas processDiagramCanvas, BpmnModel bpmnModel,
//                FlowNode flowNode, List<String> highLightedActivities, List<String> runningActivitiIdList,
//                List<String> highLightedFlows, double scaleFactor, boolean drawSequenceFlowNameWithNoLabelDi

        drawInstruction(canvas, flowNode, vo);

        // Outgoing transitions of activity
        for (SequenceFlow sequenceFlow : flowNode.getOutgoingFlows()) {
            boolean highLighted = (vo.getHighLightedFlows().contains(sequenceFlow.getId()));
            String defaultFlow = null;
            if (flowNode instanceof Activity) {
                defaultFlow = ((Activity) flowNode).getDefaultFlow();
            } else if (flowNode instanceof Gateway) {
                defaultFlow = ((Gateway) flowNode).getDefaultFlow();
            }

            boolean isDefault = false;
            if (defaultFlow != null && defaultFlow.equalsIgnoreCase(sequenceFlow.getId())) {
                isDefault = true;
            }
            boolean drawConditionalIndicator = sequenceFlow.getConditionExpression() != null
                    && !(flowNode instanceof Gateway);

            String sourceRef = sequenceFlow.getSourceRef();
            String targetRef = sequenceFlow.getTargetRef();
            FlowElement sourceElement = vo.getBpmnModel().getFlowElement(sourceRef);
            FlowElement targetElement = vo.getBpmnModel().getFlowElement(targetRef);
            List<GraphicInfo> graphicInfoList = vo.getBpmnModel().getFlowLocationGraphicInfo(sequenceFlow.getId());
            if (graphicInfoList != null && !graphicInfoList.isEmpty()) {
                graphicInfoList = connectionPerfectionizer(canvas, vo.getBpmnModel(), sourceElement,
                        targetElement, graphicInfoList);
                int[] xPoints = new int[graphicInfoList.size()];
                int[] yPoints = new int[graphicInfoList.size()];

                for (int i = 1; i < graphicInfoList.size(); i++) {
                    GraphicInfo graphicInfo = graphicInfoList.get(i);
                    GraphicInfo previousGraphicInfo = graphicInfoList.get(i - 1);

                    if (i == 1) {
                        xPoints[0] = (int) previousGraphicInfo.getX();
                        yPoints[0] = (int) previousGraphicInfo.getY();
                    }
                    xPoints[i] = (int) graphicInfo.getX();
                    yPoints[i] = (int) graphicInfo.getY();

                }

                canvas.drawSequenceflow(xPoints, yPoints, drawConditionalIndicator, isDefault,
                        highLighted, vo.getScaleFactor());

                // Draw sequenceflow label
                GraphicInfo labelGraphicInfo = vo.getBpmnModel().getLabelGraphicInfo(sequenceFlow.getId());
                if (labelGraphicInfo != null) {
                    canvas.drawLabel(sequenceFlow.getName(), labelGraphicInfo, false);
                } else {
                    if (vo.getDrawSequenceFlowNameWithNoLabelDi()) {
                        GraphicInfo lineCenter = getLineCenter(graphicInfoList);
                        canvas.drawLabel(sequenceFlow.getName(), lineCenter, false);
                    }

                }
            }
        }

        // Nested elements
        if (flowNode instanceof FlowElementsContainer) {
            for (FlowElement nestedFlowElement : ((FlowElementsContainer) flowNode).getFlowElements()) {
                if (nestedFlowElement instanceof FlowNode
                        && !isPartOfCollapsedSubProcess(nestedFlowElement, vo.getBpmnModel())) {
                    drawCustomActivity(canvas, (FlowNode) nestedFlowElement, vo);
                }
            }
        }
    }

    private void drawInstruction(CustomProcessDiagramCanvas canvas, FlowNode flowNode, CustomProcessDiagramGeneratorVO vo) {
        ActivityDrawInstruction drawInstruction = activityDrawInstructions.get(flowNode.getClass());
        if (drawInstruction != null) {

            drawInstruction.draw(canvas, vo.getBpmnModel(), flowNode);

            // Gather info on the multi instance marker
            boolean multiInstanceSequential = false;
            boolean multiInstanceParallel = false;
            boolean collapsed = false;
            if (flowNode instanceof Activity) {
                Activity activity = (Activity) flowNode;
                MultiInstanceLoopCharacteristics multiInstanceLoopCharacteristics = activity.getLoopCharacteristics();
                if (multiInstanceLoopCharacteristics != null) {
                    multiInstanceSequential = multiInstanceLoopCharacteristics.isSequential();
                    multiInstanceParallel = !multiInstanceSequential;
                }
            }

            // Gather info on the collapsed marker
            GraphicInfo graphicInfo = vo.getBpmnModel().getGraphicInfo(flowNode.getId());
            if (flowNode instanceof SubProcess) {
                collapsed = graphicInfo.getExpanded() != null && !graphicInfo.getExpanded();
            } else if (flowNode instanceof CallActivity) {
                collapsed = true;
            }

            BigDecimal bigDecimal1 = BigDecimal.valueOf(1.0);
            BigDecimal scaleFactorBigDecimal = BigDecimal.valueOf(vo.getScaleFactor());

            if (bigDecimal1.equals(scaleFactorBigDecimal)) {
                // Actually draw the markers
                canvas.drawActivityMarkers((int) graphicInfo.getX(), (int) graphicInfo.getY(),
                        (int) graphicInfo.getWidth(), (int) graphicInfo.getHeight(), multiInstanceSequential,
                        multiInstanceParallel, collapsed);
            }

            if (vo.getRunningActivitiIdList().contains(flowNode.getId())) {
                canvas.drawCustomHighLight((int) graphicInfo.getX(), (int) graphicInfo.getY(),
                        (int) graphicInfo.getWidth(), (int) graphicInfo.getHeight(), new Color(30, 144, 255));
            } else if (vo.getHighLightedActivities().contains(flowNode.getId())) {
                canvas.drawHighLight((int) graphicInfo.getX(), (int) graphicInfo.getY(),
                        (int) graphicInfo.getWidth(), (int) graphicInfo.getHeight());

            }
        }
    }
}
