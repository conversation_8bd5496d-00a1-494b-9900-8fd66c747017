package com.qm.ep.wf.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qm.ep.wf.domain.dto.ProcessDefinitionQueryDTO;
import com.qm.ep.wf.domain.vo.IdentityRequest;
import com.qm.ep.wf.domain.vo.ProcessDefinitionRequest;
import org.flowable.engine.impl.persistence.entity.ProcessDefinitionEntityImpl;
import org.flowable.engine.repository.ProcessDefinition;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR> @date
 */
public interface ProcessDefinitionService {

    /**
     * 查询单一流程定义
     *
     * @param processDefinitionId
     * @return
     */
    ProcessDefinition getProcessDefinitionById(String processDefinitionId);

    ProcessDefinition getProcessDefinitionByPKey(String processDefinitionPKey);

    /**
     * 删除流程定义
     *
     * @param processDefinitionId
     * @param cascade
     */
    void delete(String processDefinitionId, Boolean cascade);

    /**
     * 激活流程定义
     *
     * @param actionRequest
     */
    void activate(ProcessDefinitionRequest actionRequest);

    /**
     * 挂起流程定义
     *
     * @param actionRequest
     */
    void suspend(ProcessDefinitionRequest actionRequest);

    /**
     * 导入流程定义
     *
     * @param tenantId
     * @param request
     */
    void doImport(String tenantId, HttpServletRequest request);

    void saveRedisProcessDefinitionTask(String processDefinitionId);

    /**
     * 保存流程授权
     *
     * @param identityRequest
     */
    void saveProcessDefinitionIdentityLink(IdentityRequest identityRequest);

    /**
     * 删除流程授权
     *
     * @param processDefinitionId
     * @param identityId
     * @param type
     */
    void deleteProcessDefinitionIdentityLink(String processDefinitionId, String identityId, String type);


    /**
     * 根据参数查询流程定义
     *
     * @param dto
     * @return
     */
    IPage<ProcessDefinitionEntityImpl> selectLatestProcessByParams(ProcessDefinitionQueryDTO dto);
}
