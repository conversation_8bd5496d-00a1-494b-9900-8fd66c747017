package com.qm.ep.wf.domain.dto;

import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-17
 */
@ApiModel(value = "对象TaskAuthDTO对象", description = "对象TaskAuthDTO对象")
@Data
public class TaskAuthDTO extends JsonParamDto {

    @ApiModelProperty("串行版 uid")
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "主键ID")
    private String id;
    @ApiModelProperty(value = "人工任务名称")
    private String taskName;
    @ApiModelProperty(value = "参与者类型 字典项（用户、角色）")
    private String actionType;
    @ApiModelProperty(value = "参与者代码")
    private String actionCode;
    @ApiModelProperty(value = "流程定义Key 对应bpmn中process的ID")
    private String processDefKey;
    @ApiModelProperty(value = "按钮CODE")
    private String buttonCode;
    @ApiModelProperty(value = "事务码")
    private String transCode;

    @ApiModelProperty(value = "流程定义主键", hidden = true)
    private String processDefinitionId;
}
