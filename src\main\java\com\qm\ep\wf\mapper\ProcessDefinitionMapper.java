package com.qm.ep.wf.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qm.ep.wf.domain.dto.ProcessDefinitionQueryDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.flowable.engine.impl.persistence.entity.ProcessDefinitionEntityImpl;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/5/26
 */
@Mapper
public interface ProcessDefinitionMapper {


    IPage<ProcessDefinitionEntityImpl> selectLatestProcessByParams(IPage page, @Param("dto") ProcessDefinitionQueryDTO dto);
}
