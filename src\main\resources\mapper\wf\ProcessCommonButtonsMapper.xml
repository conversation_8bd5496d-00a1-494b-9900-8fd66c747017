<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qm.ep.wf.mapper.ProcessCommonButtonsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qm.ep.wf.domain.bean.ProcessCommonButtonsDO">
        <id column="ID" property="id"/>
        <result column="NAME" property="name"/>
        <result column="CODE" property="code"/>
        <result column="STATE_CODE" property="stateCode"/>
        <result column="DIRECTION" property="direction"/>
        <result column="ORDER_NO" property="orderNo"/>
        <result column="ACTION_URL" property="actionUrl"/>
        <result column="PROCESS_DEF_KEY" property="processDefKey"/>
        <result column="CORE_FLAG" property="coreFlag"/>
        <result column="VISABLE" property="visable"/>
        <result column="DTSTAMP" property="dtstamp"/>
        <result column="IS_STATE" property="isState"/>
        <result column="BUTTON_TYPE" property="buttonType"/>
        <result column="STYLE_TYPE" property="styleType"/>
        <result column="STYLE" property="style"/>
        <result column="HORIZONTAL_ALIGN" property="horizontalAlign"/>
        <result column="REMARK" property="remark"/>
        <result column="MUTEX" property="mutex"/>
        <result column="OPERATION_FLAG" property="operationFlag"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
    ID, NAME, CODE, STATE_CODE, DIRECTION, ORDER_NO, ACTION_URL, PROCESS_DEF_KEY, CORE_FLAG, VISABLE, DTSTAMP, IS_STATE, BUTTON_TYPE,STYLE_TYPE,HORIZONTAL_ALIGN,REMARK,STYLE,MUTEX,
    OPERATION_FLAG
    </sql>

    <!-- 公共查询 -->
    <sql id="QuerySQL">
        select * from (
            select
                a.NAME,
                a.CODE,
                a.STATE_CODE As stateCode,
                a.DIRECTION,
                a.ORDER_NO orderNo,
                a.ACTION_URL As actionUrl,
                a.PROCESS_DEF_KEY As processDefKey,
                a.CORE_FLAG As coreFlag,
                a.VISABLE,
                a.ID,
                a.REMARK,
                a.DTSTAMP,
                IS_STATE As isState,
                BUTTON_TYPE As buttonType,
                a.STYLE_TYPE As styleType,
                a.STYLE As style,
                a.HORIZONTAL_ALIGN As horizontalAlign,
                a.MUTEX As mutex,
                a.OPERATION_FLAG As operationFlag
            from act_ex_button a
        ) innerTable
    </sql>

    <!-- 复写MP自带函数 -->
    <select id="selectByIdNew" resultType="com.qm.ep.wf.domain.bean.ProcessCommonButtonsDO">
        <include refid="QuerySQL"/>
        where id = #{id}
    </select>
    <select id="selectBatchIdsNew" resultType="com.qm.ep.wf.domain.bean.ProcessCommonButtonsDO">
        <include refid="QuerySQL"/>
        <if test="coll != null and !coll.isEmpty">
            <where>
                id in (<foreach collection="coll" item="item" separator=",">#{item}</foreach>)
            </where>
        </if>
    </select>
    <select id="selectByMapNew" resultType="com.qm.ep.wf.domain.bean.ProcessCommonButtonsDO">
        <include refid="QuerySQL"/>
        <if test="cm != null and !cm.isEmpty">
            <where>
                <foreach collection="cm" index="k" item="v" separator="AND">
                    <choose>
                        <when test="v == null">${k} IS NULL</when>
                        <otherwise>${k} = #{v}</otherwise>
                    </choose>
                </foreach>
            </where>
        </if>
    </select>
    <select id="selectOne" resultType="com.qm.ep.wf.domain.bean.ProcessCommonButtonsDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectCount" resultType="java.lang.Integer">
        select count(1) from (<include refid="QuerySQL"/>${ew.customSqlSegment} ) countTable
    </select>
    <select id="selectList" resultType="com.qm.ep.wf.domain.bean.ProcessCommonButtonsDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectMaps" resultType="com.qm.ep.wf.domain.bean.ProcessCommonButtonsDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectObjs" resultType="com.qm.ep.wf.domain.bean.ProcessCommonButtonsDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectPage" resultType="com.qm.ep.wf.domain.bean.ProcessCommonButtonsDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectMapsPage" resultType="com.qm.ep.wf.domain.bean.ProcessCommonButtonsDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>

    <select id="selectButtonsByAuth" resultType="com.qm.ep.wf.domain.dto.ProcessCommonButtonsDTO">
        select * from (
            SELECT DISTINCT
                b.*,
                auth.TASK_NAME,
                auth.ACTION_CODE,
                auth.TRANS_CODE
            FROM
                act_ex_taskactorauth auth
                LEFT JOIN act_ex_button b ON auth.PROCESS_DEF_KEY = b.PROCESS_DEF_KEY
                AND auth.BUTTON_CODE = b.CODE
        ) innerTable
        ${ew.customSqlSegment}
    </select>

    <select id="selectButtonsByProc" resultType="com.qm.ep.wf.domain.dto.ProcessCommonButtonsDTO"
            parameterType="com.qm.ep.wf.domain.dto.TaskTranAuthDTO">
        SELECT DISTINCT
        b.*,
        t.TASK_NAME
        FROM
        act_ex_button b
        LEFT JOIN act_ex_tasktranauth t ON t.BUTTON_CODE = b.CODE and   b.PROCESS_DEF_KEY = t.PROCESS_DEF_KEY
        WHERE
        b.PROCESS_DEF_KEY = #{processDefKey}
        AND t.TRANS_CODE = #{transCode}
        <if test="isState != null">
            b.IS_STATE = #{isState}
        </if>
        <if test="authorization !=null">
            AND t.AUTHORIZATION = #{authorization}
        </if>
    </select>

    <select id="selectButtonsByParams" resultType="com.qm.ep.wf.domain.dto.ProcessCommonButtonsDTO"
            parameterType="com.qm.ep.wf.domain.dto.TaskTranAuthDTO">
        SELECT DISTINCT
        b.*
        FROM
        act_ex_button b
        LEFT JOIN act_ex_tasktranauth tran ON b.`CODE` = tran.BUTTON_CODE
        LEFT JOIN act_ex_taskactorauth actor ON b.`CODE` = actor.BUTTON_CODE
        WHERE
        b.PROCESS_DEF_KEY = #{processDefKey}
        <if test="taskName != null">
            AND tran.TASK_NAME = #{taskName}
        </if>
        <if test="transCode != null">
            AND tran.TRANS_CODE = #{transCode}
        </if>
        <if test="actionCode != null">
            AND actor.ACTION_CODE = #{actionCode}
        </if>
        <if test="buttonCode != null">
            AND b.CODE = #{buttonCode}
        </if>
        <if test="isState != null">
            AND b.is_state = #{isState}
        </if>
    </select>


    <select id="selectButtonLang" resultType="com.qm.ep.wf.domain.bean.ProcessCommonButtonsDO"
            parameterType="com.qm.ep.wf.domain.bean.ProcessCommonButtonsDO">
        select
        b.VTEXT AS NAME,
        a.CODE,
        a.STATE_CODE As stateCode,
        a.DIRECTION,
        a.ORDER_NO orderNo,
        a.ACTION_URL As actionUrl,
        a.PROCESS_DEF_KEY As processDefKey,
        a.CORE_FLAG As coreFlag,
        a.VISABLE,
        a.ID,
        a.REMARK,
        a.DTSTAMP,
        IS_STATE As isState,
        BUTTON_TYPE As buttonType,
        a.STYLE_TYPE As styleType,
        a.STYLE As style,
        a.HORIZONTAL_ALIGN As horizontalAlign,
        a.MUTEX As mutex,
        b.VLANGUAGECODE
        from act_ex_button a
        left join SYSC000_M b on a.id = b.NMAINID
        <where>
            <if test="code != null">
                AND a.CODE = #{code}
            </if>
            <if test="processDefKey != null">
                AND a.PROCESS_DEF_KEY = #{processDefKey}
            </if>
            <if test="vlanguagecode != null">
                AND b.VLANGUAGECODE = #{vlanguagecode}
            </if>
        </where>

    </select>


    <select id="selectButtonLangList" resultType="com.qm.ep.wf.domain.bean.ProcessCommonButtonsDO"
            parameterType="com.qm.ep.wf.domain.bean.ProcessCommonButtonsDO">
        select
        b.VTEXT AS NAME,
        a.CODE,
        a.STATE_CODE As stateCode,
        a.DIRECTION,
        a.ORDER_NO orderNo,
        a.ACTION_URL As actionUrl,
        a.PROCESS_DEF_KEY As processDefKey,
        a.CORE_FLAG As coreFlag,
        a.VISABLE,
        a.ID,
        a.REMARK,
        a.DTSTAMP,
        IS_STATE As isState,
        BUTTON_TYPE As buttonType,
        a.STYLE_TYPE As styleType,
        a.STYLE As style,
        a.HORIZONTAL_ALIGN As horizontalAlign,
        a.MUTEX As mutex,
        b.VLANGUAGECODE
        from act_ex_button a
        left join SYSC000_M b on a.id = b.NMAINID
        <where>
            <if test="code != null">
                AND a.CODE = #{code}
            </if>
            <if test="processDefKey != null">
                AND a.PROCESS_DEF_KEY = #{processDefKey}
            </if>
            <if test="vlanguagecode != null">
                AND b.VLANGUAGECODE = #{vlanguagecode}
            </if>
            <if test="isState != null">
                AND a.IS_STATE = #{isState}
            </if>
        </where>

    </select>
</mapper>
