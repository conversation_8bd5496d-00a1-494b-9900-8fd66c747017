package com.qm.ep.wf.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-15
 */
@ApiModel(value = "ProcessCommonButtonsVO对象", description = "")
@Data
public class ProcessCommonButtonsVO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private String id;

    @ApiModelProperty(value = "Name")
    private String name;

    @ApiModelProperty(value = "代码")
    private String code;

    @ApiModelProperty(value = "状态码")
    private String stateCode;

    @ApiModelProperty(value = "正向/方向，字典项")
    private String direction;

    @ApiModelProperty(value = "排序")
    private Integer orderNo;

    @ApiModelProperty(value = "动作Url")
    private String actionUrl;

    @ApiModelProperty(value = "流程定义Key")
    private String processDefKey;

    @ApiModelProperty(value = "核心标识")
    private Integer coreFlag;

    @ApiModelProperty(value = "是否隐藏")
    private Integer visable;

    @ApiModelProperty(value = "是否显示状态0否1是")
    private Integer isState;

    @ApiModelProperty(value = "按钮状态 reject 拒绝 agree 同意 giveup 弃权")
    private String buttonType;
}
