package com.qm.ep.wf.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qm.ep.wf.common.BaseFlowableController;
import com.qm.ep.wf.domain.bean.TaskAuthDO;
import com.qm.ep.wf.domain.dto.ProcessCommonButtonsDTO;
import com.qm.ep.wf.domain.dto.ProcessDefinitionQueryDTO;
import com.qm.ep.wf.domain.dto.TaskAuthDTO;
import com.qm.ep.wf.domain.vo.UserRoleCodeVO;
import com.qm.ep.wf.exception.FlowableTaskException;
import com.qm.ep.wf.remote.SysFeignRemote;
import com.qm.ep.wf.service.ProcessDefinitionService;
import com.qm.ep.wf.service.TaskAuthService;
import com.qm.ep.wf.util.ObjectUtils;
import com.qm.ep.wf.wapper.ProcDefListWrapper;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.I18nUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * Controller
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-17
 */
@Slf4j
@RestController
@RequestMapping("/taskAuth")
public class TaskAuthController extends BaseFlowableController {

    @Autowired
    private TaskAuthService taskAuthService;
    @Autowired
    private I18nUtil i18nUtil;

    @Autowired
    private SysFeignRemote sysFeignRemote;

    @Autowired
    private ProcessDefinitionService processDefinitionService;


    @Autowired
    private ProcDefListWrapper procDefListWrapper;

    /**
     * 使用系统默认的保存/修改 方法
     */
    @ApiOperation(value = "使用系统默认的保存/修改 方法", notes = "[author:10027705]")
    @PostMapping("/save")
    public JsonResultVo<TaskAuthDO> save(@RequestBody TaskAuthDO tempDO) {
        JsonResultVo<TaskAuthDO> resultObj = new JsonResultVo<>();
        boolean flag = taskAuthService.saveOrUpdate(tempDO);
        if (flag) {
            resultObj.setData(tempDO);
        } else {
            String message = i18nUtil.getMessage("ERR.wf.common.saveFail");
            resultObj.setMsgErr(message);
        }
        return resultObj;
    }

    /**
     * @description 批量保存授权
     * <AUTHOR>
     * @date 2020/8/19 9:32
     */
    @ApiOperation(value = "使用系统默认的保存/修改 方法", notes = "[author:10027705]")
    @PostMapping("/saveBatch")
    public JsonResultVo<TaskAuthDO> saveBatch(@RequestBody List<TaskAuthDO> list) {
        JsonResultVo<TaskAuthDO> resultObj = new JsonResultVo<>();
        boolean flag = taskAuthService.saveOrUpdateBatchForRedis(list);
        if (flag) {
            String message = i18nUtil.getMessage("MSG.wf.common.saveSuccess");
            resultObj.setMsg(message);
        } else {
            String message = i18nUtil.getMessage("ERR.wf.common.saveFail");
            resultObj.setMsgErr(message);
        }
        return resultObj;
    }

    @ApiOperation(value = "检测是否走默认权限（新增）", notes = "[author:10027705]")
    @PostMapping("/checkIsFirst")
    public JsonResultVo<TaskAuthDO> checkIsFirst(@RequestBody List<TaskAuthDO> list) {
        JsonResultVo<TaskAuthDO> resultObj = new JsonResultVo<>();
        StringBuffer reStr = new StringBuffer();
        Set<String> set = new HashSet<>();
        for (TaskAuthDO item : list) {
            StringBuffer sb = new StringBuffer();
            String taskName = item.getTaskName();
            String processDefKey = item.getProcessDefKey();
            String buttonCode = item.getButtonCode();
            String transCode = item.getTransCode();
            List<TaskAuthDO> taskAuthDOS = taskAuthService.selectTaskAuthByFiveParams(taskName, transCode, processDefKey, buttonCode, null);
            if (taskAuthDOS.size() == 0) {
                String defination = i18nUtil.getMessage("MSG.wf.flow.defination");
                String task = i18nUtil.getMessage("ERR.wf.TaskAuthController.task");
                String code = i18nUtil.getMessage("MSG.wf.transaction.code");
                String button = i18nUtil.getMessage("ERR.wf.TaskAuthController.button");
                sb.append(defination)
                        .append(processDefKey)
                        .append(",")
                        .append(task)
                        .append(taskName)
                        .append(",")
                        .append(code)
                        .append(transCode)
                        .append(",")
                        .append(button)
                        .append(buttonCode)
                        .append("。");
                set.add(sb.toString());
            }
        }

        if (set.size() > 0) {

            for (String item : set) {
                reStr.append(item);
            }
            String message = i18nUtil.getMessage("ERR.wf.TaskAuthController.java");
            reStr.append(message);
            resultObj.setMsg(reStr.toString());
        } else {
            resultObj.setMsg("");
        }
        return resultObj;
    }

    @ApiOperation(value = "检测是否走默认权限（删除）", notes = "[author:10027705]")
    @PostMapping("/checkIsLast")
    public JsonResultVo<TaskAuthDO> checkIsLast(@RequestBody Map tempDO) {
        JsonResultVo<TaskAuthDO> resultObj = new JsonResultVo<>();
        StringBuffer reStr1 = new StringBuffer();
        Object objTask = tempDO.get("TASK_NAME");
        Object objProcess = tempDO.get("PROCESS_DEF_KEY");
        if (objTask != null && objProcess != null) {
            String taskName = tempDO.get("TASK_NAME").toString();
            String processDefKey = tempDO.get("PROCESS_DEF_KEY").toString();
            String transCode = tempDO.get("TRANS_CODE").toString();
            String buttonCode = tempDO.get("BUTTON_CODE").toString();
            List<TaskAuthDO> taskAuthDOS = taskAuthService.selectTaskAuthByFiveParams(taskName, transCode, processDefKey, buttonCode, null);
            if (taskAuthDOS.size() == 1) {
                String flowDefine = i18nUtil.getMessage("MSG.wf.flow.defination");
                String taskDefine = i18nUtil.getMessage("MSG.wf.task.name");
                String transacDefine = i18nUtil.getMessage("MSG.wf.transaction.code");
                String terminator = i18nUtil.getMessage("MSG.wf.common.terminator");
                reStr1.append(flowDefine).append(processDefKey).append(",")
                        .append(taskDefine).append(taskName).append(",")
                        .append(transacDefine).append(transCode).append(",")
                        .append(terminator);
            }

            if (reStr1.length() > 0) {
                String message = i18nUtil.getMessage("MSG.wf.taskAuth.checkIsLast");
                reStr1.append(message);
                resultObj.setMsg(reStr1.toString());
            } else {
                resultObj.setMsg("");
            }
        } else {
            resultObj.setMsg("");
        }

        return resultObj;
    }

    /**
     * 根据传入的id删除数据
     */
    @ApiOperation(value = "根据传入的id删除数据", notes = "[author:10027705]")
    @PostMapping("/deleteById")
    public JsonResultVo deleteById(@RequestBody TaskAuthDO tempDO) {
        JsonResultVo resultObj = new JsonResultVo();
        boolean flag = taskAuthService.removeById(tempDO.getId());
        if (flag) {
            log.warn("在方法taskAuth/deleteById中 processDefKey:{} ,transCode:{} ,actionCode:{}被删除", tempDO.getProcessDefKey(), tempDO.getTransCode(), tempDO.getActionCode());
            String message = i18nUtil.getMessage("MSG.wf.common.delSuccess");
            resultObj.setMsg(message);
        } else {
            String message = i18nUtil.getMessage("ERR.wf.common.delFail");
            resultObj.setMsgErr(message);
        }
        return resultObj;
    }

    /**
     * 根据传入的map删除信息
     */
    @ApiOperation(value = "根据传入的map删除信息", notes = "[author:10027705]")
    @PostMapping("/deleteByMap")
    public JsonResultVo deleteByMap(@RequestBody Map map) {
        JsonResultVo resultObj = new JsonResultVo();
        boolean flag = taskAuthService.removeByMapForRedis(map);
        if (flag) {
            log.warn("在方法taskAuth/deleteByMap processDefKey:{} ,transCode:{} ,actionCode:{}被删除", map.get("PROCESS_DEF_KEY"), map.get("TRANS_CODE"), map.get("ACTION_CODE"));
            String message = i18nUtil.getMessage("ERR.wf.ProcessCommonButtonsController.operateSuccess");
            resultObj.setMsg(message);
        } else {
            String message = i18nUtil.getMessage("MSG.wf.ProcessCommonButtonsController.operateFail");
            resultObj.setMsgErr(message);
        }
        return resultObj;
    }

    /**
     * 根据传入的实体信息进行查询
     */
    @ApiOperation(value = "根据传入的实体信息进行查询", notes = "[author:10027705]")
    @PostMapping("/table")
    public JsonResultVo<QmPage<TaskAuthDO>> table(@RequestBody TaskAuthDTO tempDTO) {
        //定义查询构造器
        QmQueryWrapper<TaskAuthDO> queryWrapper = new QmQueryWrapper<>();
        //拼装实体属性查询条件
        LambdaQueryWrapper<TaskAuthDO> lambdaWrapper = queryWrapper.lambda();
        //主键ID
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getId())) {
            lambdaWrapper.eq(TaskAuthDO::getId, tempDTO.getId());
        }
        //人工任务名称
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getTaskName())) {
            lambdaWrapper.eq(TaskAuthDO::getTaskName, tempDTO.getTaskName());
        }
        //参与者类型 字典项（用户、角色）
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getActionType())) {
            lambdaWrapper.eq(TaskAuthDO::getActionType, tempDTO.getActionType());
        }
        //参与者代码
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getActionCode())) {
            lambdaWrapper.eq(TaskAuthDO::getActionCode, tempDTO.getActionCode());
        }
        //流程定义Key 对应bpmn中process的ID
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getProcessDefKey())) {
            lambdaWrapper.eq(TaskAuthDO::getProcessDefKey, tempDTO.getProcessDefKey());
        }
        // 按钮代码
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getButtonCode())) {
            lambdaWrapper.eq(TaskAuthDO::getButtonCode, tempDTO.getButtonCode());
        }
        // 用例代码 hyh
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getTransCode())) {
            lambdaWrapper.eq(TaskAuthDO::getTransCode, tempDTO.getTransCode());
        }
        //查询数据，使用table函数。
        QmPage<TaskAuthDO> list = taskAuthService.table(queryWrapper, tempDTO);
        JsonResultVo<QmPage<TaskAuthDO>> ret = new JsonResultVo<>();
        ret.setData(list);
        return ret;
    }

    @ApiOperation(value = "根据传入的实体信息进行查询", notes = "[author:10027705]")
    @PostMapping("/tableButtons")
    public JsonResultVo<QmPage<ProcessCommonButtonsDTO>> tableButtons(@RequestBody TaskAuthDTO tempDTO) {
        JsonResultVo<QmPage<ProcessCommonButtonsDTO>> ret = new JsonResultVo<>();
        ret.setData(taskAuthService.searchButtonsTwo(tempDTO));
        return ret;
    }

    @GetMapping(value = "/procDefList")
    @ApiOperation(value = "查询流程定义列表", notes = "[author:10027705]")
    public JsonResultVo procDefList(ProcessDefinitionQueryDTO queryDTO) {
        JsonResultVo resultObj = new JsonResultVo();
        queryDTO.setAuthorization(0);
        IPage process = processDefinitionService.selectLatestProcessByParams(queryDTO);
        List list = procDefListWrapper.execute(process.getRecords());

        List records = taskAuthService.procDefList(list);

        process.setRecords(records);

        resultObj.setData(process);
        return resultObj;
    }

    @ApiOperation(value = "清除按钮的事务和操作权限", notes = "[author:10027705]")
    @PostMapping("/clearAuth")
    public JsonResultVo<TaskAuthDO> clearAuth(@RequestBody List<TaskAuthDTO> tempDTOS) {
        return taskAuthService.clearAuth(tempDTOS);
    }

    @ApiOperation(value = "清除按钮的事务和操作权限", notes = "[author:10027705]")
    @PostMapping("/clearAuth/processDefinitionId")
    public JsonResultVo clearAuthByProcessDefinitionId(@RequestBody TaskAuthDTO dto) {
        if (ObjectUtils.isEmpty(dto) && StringUtils.isEmpty(dto.getProcessDefinitionId())) {
            JsonResultVo vo = new JsonResultVo();
            String message = i18nUtil.getMessage("MSG.wf.common.operateSuccess");
            vo.setMsg(message);
            return vo;
        }
        return taskAuthService.clearAuthByProcessDefinitionId(dto.getProcessDefinitionId());
    }

    @ApiOperation(value = "查询按钮对应的人员/角色授权", notes = "[author:10027705]")
    @PostMapping("/selectButtonAuth")
    public JsonResultVo<String> selectButtonAuth(@RequestBody TaskAuthDTO tempDTO) {
        //定义查询构造器
        QmQueryWrapper<TaskAuthDO> queryWrapper = new QmQueryWrapper<>();
        //拼装实体属性查询条件
        LambdaQueryWrapper<TaskAuthDO> lambdaWrapper = queryWrapper.lambda();
        //人工任务名称
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getTaskName())) {
            lambdaWrapper.eq(TaskAuthDO::getTaskName, tempDTO.getTaskName());
        }
        //流程定义Key 对应bpmn中process的ID
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getProcessDefKey())) {
            lambdaWrapper.eq(TaskAuthDO::getProcessDefKey, tempDTO.getProcessDefKey());
        }
        // 按钮代码
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getButtonCode())) {
            lambdaWrapper.eq(TaskAuthDO::getButtonCode, tempDTO.getButtonCode());
        }
        //查询数据，使用table函数。
        List<TaskAuthDO> list = taskAuthService.list(queryWrapper);
        List<String> userCodes = new ArrayList<>();
        List<String> roleCodes = new ArrayList<>();
        // 分别取出用户和角色code
        if (CollectionUtils.isNotEmpty(list)) {
            userCodes = list.stream().filter(e -> "0".equals(e.getActionType())).map(TaskAuthDO::getActionCode).distinct().collect(Collectors.toList());
            roleCodes = list.stream().filter(e -> "1".equals(e.getActionType())).map(TaskAuthDO::getActionCode).distinct().collect(Collectors.toList());
        }
        // 若角色存在，取角色对应的用户code
        // 查询所有角色对应code
        if (CollectionUtils.isNotEmpty(roleCodes)) {
            JsonResultVo<Map<String, List<UserRoleCodeVO>>> roleResult = sysFeignRemote.getPersonCodeByRoleCode(BootAppUtil.getLoginKey().getTenantId(), "");
            if (roleResult.getCode() == 500) {
                throw new FlowableTaskException(roleResult.getMsg());
            }
            for (String roleCode : roleCodes) {
                Map<String, List<UserRoleCodeVO>> map = roleResult.getData();
                List<UserRoleCodeVO> userRoleCodes = map.get(roleCode);
                if (!BootAppUtil.isNullOrEmpty(userRoleCodes) && CollectionUtils.isNotEmpty(userRoleCodes)) {
                    userCodes.addAll(userRoleCodes.stream().map(UserRoleCodeVO::getVPERSONCODE).collect(Collectors.toList()));
                }
            }
        }
        // 如果用户存在，访问sys获取用户ID集合
        JsonResultVo<String> ret = new JsonResultVo<>();
        if (CollectionUtils.isNotEmpty(userCodes)) {
            List<String> codes = userCodes.stream().distinct().collect(Collectors.toList());
            JsonResultVo<String> result = sysFeignRemote.selectUserIdByCode(BootAppUtil.getLoginKey().getTenantId(), codes);
            ret.setDataList(result.getDataList());
        }
        return ret;
    }

    /**
     * 业务需要-不做校验----20220411
     * @param dto
     * @return
     */
    @ApiOperation(value = "查询工作流按钮的权限", notes = "[author:10027705]")
    @PostMapping("/flow/selectButtonAuth")
    public JsonResultVo flowSelectButtonAuth(@RequestBody TaskAuthDTO dto) {
        if (ObjectUtils.isEmpty(dto)) {
            JsonResultVo vo = new JsonResultVo();
            String message = i18nUtil.getMessage("MSG.wf.common.operateSuccess");
            vo.setMsg(message);
            return vo;
        }
        return taskAuthService.selectButtonAuth(dto.getTaskName(),dto.getProcessDefKey(),dto.getButtonCode());
    }
}
