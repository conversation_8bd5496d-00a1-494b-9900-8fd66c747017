<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.qm.ep</groupId>
        <artifactId>tds-service-parent-nacos-bastnew</artifactId>
        <version>4.0.0-hq-SNAPSHOT</version>
    </parent>
    <groupId>com.qm.ep</groupId>
    <artifactId>common-wf</artifactId>
    <version>0.0.9-hq-sw-SNAPSHOT</version>
    <name>common-wf</name>
    <description>工作流</description>
    <packaging>jar</packaging>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-boot-starter</artifactId>
                <version>3.0.0</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>io.swagger.core.v3</groupId>
                <artifactId>swagger-annotations</artifactId>
                <version>2.2.9</version>
            </dependency>
            <dependency>
                <groupId>io.swagger.core.v3</groupId>
                <artifactId>swagger-models</artifactId>
                <version>2.2.9</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <!--flowable -->
        <dependency>
            <groupId>org.flowable</groupId>
            <artifactId>flowable-spring-boot-starter</artifactId>
            <version>6.6.0</version>
            <exclusions><!-- 需要排除flowable的mybatis依赖，不然会跟mybatis-plus冲突 -->
                <exclusion>
                    <groupId>org.mybatis</groupId>
                    <artifactId>mybatis</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baomidou</groupId>
                    <artifactId>mybatis-plus-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.liquibase</groupId>
                    <artifactId>liquibase-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.flowable</groupId>
            <artifactId>flowable-idm-spring</artifactId>
            <version>6.6.0</version>
            <exclusions><!-- 需要排除flowable的mybatis依赖，不然会跟mybatis-plus冲突 -->
                <exclusion>
                    <groupId>org.mybatis</groupId>
                    <artifactId>mybatis</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baomidou</groupId>
                    <artifactId>mybatis-plus-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.liquibase</groupId>
                    <artifactId>liquibase-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.flowable</groupId>
            <artifactId>flowable-idm-spring-configurator</artifactId>
            <version>6.6.0</version>
            <exclusions><!-- 需要排除flowable的mybatis依赖，不然会跟mybatis-plus冲突 -->
                <exclusion>
                    <groupId>org.mybatis</groupId>
                    <artifactId>mybatis</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baomidou</groupId>
                    <artifactId>mybatis-plus-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.liquibase</groupId>
                    <artifactId>liquibase-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.flowable</groupId>
            <version>6.6.0</version>
            <artifactId>flowable-json-converter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.liquibase</groupId>
                    <artifactId>liquibase-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- MQ -->
        <dependency>
            <groupId>com.qm.tds</groupId>
            <artifactId>tds-base-mq</artifactId>
            <version>4.0.0-hq-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-bus</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-function-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.qm.tds</groupId>
            <artifactId>tds-base-lock</artifactId>
            <version>4.0.0-hq-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-bus</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-function-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-ui</artifactId>
            <version>1.7.0</version>
        </dependency>
    </dependencies>

    <!-- Maven仓库地址 -->
    <repositories>
        <repository>
            <id>qm_maven_center</id>
            <name>qm dms maven center</name>
            <url>http://yldevrepo.faw.com:8080/repository/maven-snapshots/</url>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
                <checksumPolicy>warn</checksumPolicy>
            </snapshots>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>


    <!-- 上传jar包至私有maven仓库-->
    <distributionManagement>
        <repository>
            <id>dayu-maven-releases</id>
            <url>https://devops-nexus.faw.cn/repository/bp4655969727-releases/</url>
        </repository>
        <snapshotRepository>
            <id>dayu-maven-snapshots</id>
            <url>https://devops-nexus.faw.cn/repository/bp4655969727-snapshots/</url>
            <uniqueVersion>true</uniqueVersion>
        </snapshotRepository>
    </distributionManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>

