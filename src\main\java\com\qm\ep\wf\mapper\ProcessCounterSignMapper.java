package com.qm.ep.wf.mapper;

import com.qm.ep.wf.domain.bean.ProcessCounterSignDO;
import com.qm.ep.wf.domain.dto.ProcessCounterSignDTO;
import com.qm.tds.api.mp.mapper.QmBaseMapper;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-18
 */
public interface ProcessCounterSignMapper extends QmBaseMapper<ProcessCounterSignDO> {

    List<ProcessCounterSignDO> selectCountersignGroupName(ProcessCounterSignDTO processCounterSignDTO);

    Integer updateCountersignByKey(ProcessCounterSignDO processCounterSignDO);
}
