package com.qm.ep.wf.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qm.ep.wf.domain.bean.ProcessCommonButtonsDO;
import com.qm.ep.wf.domain.dto.ProcessCommonButtonsDTO;
import com.qm.ep.wf.domain.dto.TaskAuthDTO;
import com.qm.ep.wf.domain.dto.TaskTranAuthDTO;
import com.qm.tds.api.mp.mapper.QmBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-15
 */
public interface ProcessCommonButtonsMapper extends QmBaseMapper<ProcessCommonButtonsDO> {

    List<ProcessCommonButtonsDTO> selectButtonsByProc(TaskTranAuthDTO taskTranAuthDTO);

    List<ProcessCommonButtonsDTO> selectButtonsByParams(TaskTranAuthDTO taskTranAuthDTO);

    List<ProcessCommonButtonsDTO> selectButtonsByAuth(IPage<TaskAuthDTO> page, @Param("ew") Wrapper<TaskAuthDTO> queryWrapper);

    ProcessCommonButtonsDO selectButtonLang(ProcessCommonButtonsDO param);

    List<ProcessCommonButtonsDO> selectButtonLangList(ProcessCommonButtonsDO param);
}
