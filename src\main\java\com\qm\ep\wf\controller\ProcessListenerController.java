package com.qm.ep.wf.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qm.ep.wf.domain.bean.ProcessListenerDO;
import com.qm.ep.wf.domain.dto.ProcessListenerDTO;
import com.qm.ep.wf.service.ProcessListenerService;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonParamDto;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.I18nUtil;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <p>
 * Controller
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-15
 */
@RestController
@RequestMapping("/processListener")
public class ProcessListenerController extends BaseController {
    @Autowired
    private ProcessListenerService processListenerService;
    @Autowired
    private I18nUtil i18nUtil;

    /**
     * 使用系统默认的保存/修改 方法
     */
    @ApiOperation(value = "使用系统默认的保存/修改 方法", notes = "[author:10027705]")
    @PostMapping("/save")
    public JsonResultVo<ProcessListenerDO> save(@RequestBody ProcessListenerDO tempDO) {
        JsonResultVo<ProcessListenerDO> resultObj = new JsonResultVo<>();
        boolean flag = processListenerService.saveOrUpdate(tempDO);
        if (flag) {
            resultObj.setData(tempDO);
        } else {
            String message = i18nUtil.getMessage("ERR.wf.common.saveFail");
            resultObj.setMsgErr(message);
        }
        return resultObj;
    }

    /**
     * 根据传入的id删除数据
     */
    @ApiOperation(value = "根据传入的id删除数据", notes = "[author:10027705]")
    @PostMapping("/deleteById")
    public JsonResultVo deleteById(@RequestBody ProcessListenerDO tempDO) {
        JsonResultVo resultObj = new JsonResultVo();
        boolean flag = processListenerService.removeById(tempDO.getId());
        if (flag) {
            String message = i18nUtil.getMessage("MSG.wf.common.saveSuccess");
            resultObj.setMsg(message);
        } else {
            String message = i18nUtil.getMessage("ERR.wf.common.saveFail");
            resultObj.setMsgErr(message);
        }
        return resultObj;
    }

    /**
     * 根据传入的map删除信息
     */
    @ApiOperation(value = "根据传入的map删除信息", notes = "[author:10027705]")
    @PostMapping("/deleteByMap")
    public JsonResultVo deleteByMap(@RequestBody Map map) {
        JsonResultVo resultObj = new JsonResultVo();
        boolean flag = processListenerService.removeByMap(map);
        if (flag) {
            String message = i18nUtil.getMessage("ERR.wf.ProcessCommonButtonsController.operateSuccess");
            resultObj.setMsg(message);
        } else {
            String message = i18nUtil.getMessage("MSG.wf.ProcessCommonButtonsController.operateFail");
            resultObj.setMsgErr(message);
        }
        return resultObj;
    }

    /**
     * 根据传入的实体信息进行查询
     */
    @ApiOperation(value = "根据传入的实体信息进行查询", notes = "[author:10027705]")
    @PostMapping("/table")
    public JsonResultVo<QmPage<ProcessListenerDO>> table(@RequestBody ProcessListenerDTO tempDTO) {
        //定义查询构造器
        QmQueryWrapper<ProcessListenerDO> queryWrapper = new QmQueryWrapper<>();
        //拼装实体属性查询条件
        LambdaQueryWrapper<ProcessListenerDO> lambdaWrapper = queryWrapper.lambda();
        //主键ID
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getId())) {
            lambdaWrapper.eq(ProcessListenerDO::getId, tempDTO.getId());
        }
        //名称
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getName())) {
            lambdaWrapper.eq(ProcessListenerDO::getName, tempDTO.getName());
        }
        //监听器类型  字典项（执行监听器executionListener；任务监听器taskListener）
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getListenerType())) {
            lambdaWrapper.eq(ProcessListenerDO::getListenerType, tempDTO.getListenerType());
        }
        //事件类型 执行监听器对应（start；take；end）任务监听器对应（assignment；create；complete；delete）
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getEventType())) {
            lambdaWrapper.eq(ProcessListenerDO::getEventType, tempDTO.getEventType());
        }
        //值类型 字典项（类；表达式；委托表达式）
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getValueType())) {
            lambdaWrapper.eq(ProcessListenerDO::getValueType, tempDTO.getValueType());
        }
        //类/表达式/委托表达式
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getExpression())) {
            lambdaWrapper.eq(ProcessListenerDO::getExpression, tempDTO.getExpression());
        }
        //流程定义Key 对应bpmn中process的ID
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getProcessDefKey())) {
            lambdaWrapper.eq(ProcessListenerDO::getProcessDefKey, tempDTO.getProcessDefKey());
        }
        //查询数据，使用table函数。
        QmPage<ProcessListenerDO> list = processListenerService.table(queryWrapper, tempDTO);
        JsonResultVo<QmPage<ProcessListenerDO>> ret = new JsonResultVo<>();
        ret.setData(list);
        return ret;
    }

    /**
     * 查询数据，使用Map进行查找数据集合。
     */
    @ApiOperation(value = "查询数据，使用Map进行查找数据集合", notes = "[author:10027705]")
    @PostMapping("/tableByMap")
    public JsonResultVo<QmPage<ProcessListenerDO>> tableByMap(@RequestBody Map map) {
        QmQueryWrapper<ProcessListenerDO> qmQueryWrapper = new QmQueryWrapper<>();
        //拼装实体属性查询条件
        LambdaQueryWrapper<ProcessListenerDO> lambdaWrapper = qmQueryWrapper.lambda();
        // 主键ID
        if (!BootAppUtil.isNullOrEmpty(map.get("ID"))) {
            lambdaWrapper.eq(ProcessListenerDO::getId, map.get("ID"));
        }
        // 名称
        if (!BootAppUtil.isNullOrEmpty(map.get("NAME"))) {
            lambdaWrapper.eq(ProcessListenerDO::getName, map.get("NAME"));
        }
        // 监听器类型  字典项（执行监听器executionListener；任务监听器taskListener）
        if (!BootAppUtil.isNullOrEmpty(map.get("LISTENER_TYPE"))) {
            lambdaWrapper.eq(ProcessListenerDO::getListenerType, map.get("LISTENER_TYPE"));
        }
        // 事件类型 执行监听器对应（start；take；end）任务监听器对应（assignment；create；complete；delete）
        if (!BootAppUtil.isNullOrEmpty(map.get("EVENT_TYPE"))) {
            lambdaWrapper.eq(ProcessListenerDO::getEventType, map.get("EVENT_TYPE"));
        }
        // 值类型 字典项（类；表达式；委托表达式）
        if (!BootAppUtil.isNullOrEmpty(map.get("VALUE_TYPE"))) {
            lambdaWrapper.eq(ProcessListenerDO::getValueType, map.get("VALUE_TYPE"));
        }
        // 类/表达式/委托表达式
        if (!BootAppUtil.isNullOrEmpty(map.get("EXPRESSION"))) {
            lambdaWrapper.eq(ProcessListenerDO::getExpression, map.get("EXPRESSION"));
        }
        // 流程定义Key 对应bpmn中process的ID
        if (!BootAppUtil.isNullOrEmpty(map.get("PROCESS_DEF_KEY"))) {
            lambdaWrapper.eq(ProcessListenerDO::getProcessDefKey, map.get("PROCESS_DEF_KEY"));
        }
        //获取分页信息
        JsonParamDto jsonParamDto = getParaFromMap(map);
        //查询数据，使用table函数。
        QmPage<ProcessListenerDO> list = processListenerService.table(qmQueryWrapper, jsonParamDto);
        JsonResultVo<QmPage<ProcessListenerDO>> ret = new JsonResultVo<>();
        ret.setData(list);
        return ret;
    }
}
