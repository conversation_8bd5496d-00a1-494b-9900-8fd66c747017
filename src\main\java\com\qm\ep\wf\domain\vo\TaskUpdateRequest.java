package com.qm.ep.wf.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> @date
 */
@ApiModel(description = "任务更新请求")
@Data
public class TaskUpdateRequest {
    @ApiModelProperty("数据id")
    private String id;
    @ApiModelProperty("名字")
    private String name;
    @ApiModelProperty("受让人")
    private String assignee;
    @ApiModelProperty("所有者")
    private String owner;
    @ApiModelProperty("期限")
    private Date dueDate;
    @ApiModelProperty("类别")
    private String category;
    @ApiModelProperty("描述")
    private String description;
    @ApiModelProperty("优先权")
    private int priority;
}
