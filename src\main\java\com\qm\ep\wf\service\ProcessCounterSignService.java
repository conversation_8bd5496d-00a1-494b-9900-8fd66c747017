package com.qm.ep.wf.service;

import com.qm.ep.wf.domain.bean.ProcessCounterSignDO;
import com.qm.ep.wf.domain.dto.ProcessCounterSignDTO;
import com.qm.ep.wf.domain.dto.UpdateCounterSignTaskKeyDTO;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.service.IQmBaseService;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-18
 */
public interface ProcessCounterSignService extends IQmBaseService<ProcessCounterSignDO> {

    List<ProcessCounterSignDO> selectCountersignGroupName(ProcessCounterSignDTO processCounterSignDTO);

    List<ProcessCounterSignDO> getCountersignInfo(ProcessCounterSignDTO processCounterSignDTO);

    JsonResultVo<ProcessCounterSignDO> customeSaveOrUpdate(ProcessCounterSignDO tempDO);

    JsonResultVo<ProcessCounterSignDO> updateCountersignByKey(ProcessCounterSignDO tempDO);

    /**
     * 根据ID修改 TaskKey
     *
     * @param dtoList
     * @return
     */
    JsonResultVo<String> updateTaskKeyById(List<UpdateCounterSignTaskKeyDTO> dtoList);
}
