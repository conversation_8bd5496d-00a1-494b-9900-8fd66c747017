#!/usr/bin/env groovy
properties(
        [parameters(
                [string(defaultValue: "deploy_qm02", description: "测试云docker用户名", name: "DOCKER_USER"),
                 string(defaultValue: "Dms_5906928", description: "测试云docker密码", name: "DOCKER_PASSWORD"),
                 string(defaultValue: "************:31104/tsf_100000149/prod-common-service-wf", description: "测试云镜像地址", name: "REGISTRY_URL"),
                 string(defaultValue: "common-service-wf", description: "服务名称", name: "APP_NAME"),
                 gitParameter(branch: "",
                         branchFilter: "origin/(.*)",
                         defaultValue: "sit",
                         description: "",
                         name: "<PERSON><PERSON><PERSON>",
                         quickFilterEnabled: false,
                         selectedValue: "NONE",
                         sortMode: "NONE",
                         tagFilter: "*",
                         type: "PT_BRANCH")
                ]
          )
        ])

node("master") {
        withEnv(["PATH+EXTRA=/usr/sbin:/usr/bin:/sbin:/bin:/opt/apache-maven-3.5.4/bin"]) {
        stage("checkout") {
            echo "======检出代码分支名称======:" + "${params.BRANCH}"
            git branch: "${params.BRANCH}", credentialsId: 'e3113506-9fae-4184-8e61-2bddeecce125', url: 'http://*************/spring/common-service-wf.git'
          }
        stage("build") {
            echo "======进行代码构建======:"
            withMaven(jdk: "JDK  8u181", maven: "Maven 3.5.4") {
            sh 'mvn -U clean install -Dmaven.test.skip=true'
            }
        }
        /*
        stage("merge") {
        echo "======合并uat到prod分支======:"
        sh '''
         git push http://*************/spring/common-service-wf.git uat:prod
         git push https://wangjiquan_qm:<EMAIL>/ep_hq/common-wf.git uat:prod
         ''' 
        }
        */
       // stage("docker-login") {
           // echo "======进行镜像仓库登录======:"
          //  sh "docker login -u ${params.DOCKER_USER} -p ${params.DOCKER_PASSWORD} ${params.REGISTRY_URL}"
       // }

      //  dateVersion = "${params.BRANCH}" + "-" + sh(script: "date \"+%Y%m%d%H%M%S\"", returnStdout: true).trim()
     //   echo "dateVersion = ${dateVersion}"

      //  stage("docker-build") {
        //    echo "======进行镜像仓库构建======:"
            //sh "cp target/*.jar ../${APP_NAME}"

         //   echo "======进行镜像======:"
        //    def image = docker.build("${params.REGISTRY_URL}:${APP_NAME}-${dateVersion}-v${BUILD_ID}")

          //  echo "======进行推送======:"
           // echo "${image}"
           // image.push()
            
           // echo "======删除本地镜像======:"
         //   sh "docker rmi -f ${params.REGISTRY_URL}:${APP_NAME}-${dateVersion}-v${BUILD_ID}"
        
       // }
    }

}
