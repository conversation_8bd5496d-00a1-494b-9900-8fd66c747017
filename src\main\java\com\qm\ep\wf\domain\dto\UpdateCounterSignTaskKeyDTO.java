package com.qm.ep.wf.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/4/7
 */
@ApiModel(value = "修改会签TaskKey对象", description = "修改会签TaskKey对象")
@Data
public class UpdateCounterSignTaskKeyDTO implements Serializable {

    @ApiModelProperty(value = "主键id")
    private String id;

    @ApiModelProperty(value = "任务Key")
    private String taskKey;
}
