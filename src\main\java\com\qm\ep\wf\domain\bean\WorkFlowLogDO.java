package com.qm.ep.wf.domain.bean;


import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.qm.tds.api.ser.QmDateSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

/**
 * <p>
 * 导出Excel日志
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("act_ex_batchlog")
@ApiModel(value = "workFlowDO对象", description = "导出Excel日志")
public class WorkFlowLogDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty(value = "事务码代码")
    @TableField("VTRANSCODE")
    private String vtranscode;

    @ApiModelProperty(value = "用例名称、菜单名称")
    @TableField("VMENUNAME")
    private String vmenuname;

    @ApiModelProperty(value = "公司ID")
    @TableField(value = "NCOMPANYID")
    private String ncompanyid;

    @ApiModelProperty(value = "操作员ID")
    @TableField("NOPR")
    private String nopr;

    @ApiModelProperty(value = "操作员代码")
    @TableField("VOPR")
    private String vopr;

    @ApiModelProperty(value = "操作员名称")
    @TableField("VOPRNAME")
    private String voprname;

    @ApiModelProperty(value = "开始时间")
    @TableField("DBEGIN")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date dbegin;

    @ApiModelProperty(value = "结束时间")
    @TableField("DEND")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date dend;

    @ApiModelProperty(value = "心跳时间。判断后台进程是否已经中断")
    @TableField("DPULSE")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date dpulse;

    @ApiModelProperty(value = "进度")
    @TableField("NPROCESS")
    private Integer nprocess;

    @ApiModelProperty(value = "流程定义Key")
    @TableField("PROCESSDEFKEY")
    private String processdefkey;

    @ApiModelProperty(value = "业务主键Key")
    @TableField("BUSINESSKEY")
    private String businesskey;

    @ApiModelProperty(value = "审批按钮名称")
    @TableField("BUTTONNAME")
    private String buttonname;

    @ApiModelProperty(value = "TraceId")
    @TableField("TRACEID")
    private String traceid;

    @ApiModelProperty(value = "执行条数")
    @TableField("NBATCHCOUNT")
    private Integer nbatchcount;

    @ApiModelProperty(value = "执行参数")
    @TableField("VPARA")
    private String vpara;

    @ApiModelProperty(value = "时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    @Version
    private Timestamp dtstamp;

    @ApiModelProperty(value = "执行结果")
    @TableField("RESULT")
    private String result;

    @ApiModelProperty(value = "删除标识")
    @TableField("VDELETE")
    private String vdelete;

    @ApiModelProperty(value = "批量提交Id")
    @TableField("VBATCHID")
    private String batchId;

    @ApiModelProperty(value = "批量完成数量")
    @TableField("COMPLETENUM")
    private int completeNum;

    @ApiModelProperty(value = "失败数量")
    @TableField("FAILNUM")
    private int failNum;

    @ApiModelProperty(value = "成功数量")
    @TableField("SUCCESSNUM")
    private int successNum;
}
