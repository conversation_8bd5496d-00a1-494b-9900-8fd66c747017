package com.qm.ep.wf.domain.bean;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.flowable.engine.task.Comment;

import java.io.Serializable;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("act_hi_comment_code")
@ApiModel(value = "工作流标记拓展表", description = "")
public class ActHiCommentCodeDO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty(value = "com表id")
    @TableField("COMMENTID")
    private String commentId;

    @ApiModelProperty(value = "流程ID")
    @TableField("PROC_INST_ID_")
    private String procInstId;

    @ApiModelProperty(value = "工作流按钮编码")
    @TableField("BUTTONCODE")
    private String buttonCode;

    @ApiModelProperty(value = "工作流按钮名称")
    @TableField("BUTTONNAME")
    private String buttonName;

    @ApiModelProperty(value = "工作流按钮名称")
    @TableField("PROCESSDEFKEY")
    private String processDefKey;

    @ApiModelProperty(value = "备注1")
    @TableField("VREV1")
    private String vrev1;

    @ApiModelProperty(value = "备注2")
    @TableField("VREV2")
    private String vrev2;

    @ApiModelProperty(value = "备注3")
    @TableField("VREV3")
    private String vrev3;

    @ApiModelProperty(value = "备注4")
    @TableField("VREV4")
    private String vrev4;

}
