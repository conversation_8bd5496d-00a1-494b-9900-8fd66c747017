package com.qm.ep.wf.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("act_ex_tasktranauth")
@ApiModel(value = "对象TaskTranAuthDO对象", description = "对象TaskTranAuthDO对象")
public class TaskTranAuthDO implements Serializable {

    @ApiModelProperty("串行版 uid")
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty(value = "任务名称")
    @TableField("TASK_NAME")
    private String taskName;

    @ApiModelProperty(value = "事务码")
    @TableField("TRANS_CODE")
    private String transCode;

    @ApiModelProperty(value = "工作流模版Key")
    @TableField("PROCESS_DEF_KEY")
    private String processDefKey;

    @ApiModelProperty(value = "时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    @Version
    private Timestamp dtstamp;

    @ApiModelProperty(value = "按钮CODE")
    @TableField("BUTTON_CODE")
    private String buttonCode;

    @ApiModelProperty(value = "非操作授权，0 || 1")
    @TableField("AUTHORIZATION")
    private Integer authorization;
}
