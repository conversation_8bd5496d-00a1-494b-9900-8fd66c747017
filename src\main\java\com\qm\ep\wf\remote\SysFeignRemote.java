package com.qm.ep.wf.remote;

import com.qm.ep.wf.domain.bean.TransCodeDO;
import com.qm.ep.wf.domain.dto.PersonnelDTO;
import com.qm.ep.wf.domain.dto.RoleDTO;
import com.qm.ep.wf.domain.dto.UserDTO;
import com.qm.ep.wf.domain.vo.RoleVO;
import com.qm.ep.wf.domain.vo.UserRoleCodeVO;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Repository
@FeignClient(name = "tds-service-sys", fallbackFactory = SysFeignFallbackFactory.class)
public interface SysFeignRemote {

    /**
     * 根据用户ID获取
     *
     * @param userDTO
     * @return
     */
    @PostMapping("/role/getRoleListByUserId")
    JsonResultVo getRoleListByUserId(@RequestHeader("tenantId") String tenantId, @RequestBody UserDTO userDTO);

    /**
     * 根据系统参数代码和公司ID获取系统参数值
     *
     * @param tenantId
     * @param nCompanyId
     * @param vParamCode
     * @return
     */
    @GetMapping("/sysParam/getSysParamValue")
    JsonResultVo getSysParamValue(@RequestHeader("tenantId") String tenantId, @RequestParam(required = false) String nCompanyId, @RequestParam(required = true) String vParamCode);

    /**
     * 根据事务码代码集合获取事务码数据
     *
     * @param codes
     * @return
     */
    @PostMapping("/transCode/tableByTransCodes")
    JsonResultVo<QmPage<TransCodeDO>> getTransCode(@RequestHeader("tenantId") String tenantId, @RequestBody List<String> codes);

    /**
     * 获取所有事务码map
     *
     * @return
     */
    @PostMapping("/transCode/selectTransAllMap")
    JsonResultVo<Map<String, TransCodeDO>> selectTransAllMap(@RequestHeader("tenantId") String tenantId);

    @PostMapping("/personnel/getPersonCodeByRoleCode")
    JsonResultVo<Map<String, List<UserRoleCodeVO>>> getPersonCodeByRoleCode(@RequestHeader("tenantId") String tenantId, @RequestParam String roleCode);

    /**
     * @description 根据事务码名称查询事务码代码
     * <AUTHOR>
     * @date 2020/10/23 9:14
     */
    @PostMapping("/transCode/selectTransCodeByName")
    JsonResultVo<List<String>> selectTransCodeByName(@RequestHeader("tenantId") String tenantId, @RequestParam String transName);

    /**
     * 获取系统用户列表
     *
     * @return
     */
    @PostMapping("/user/getAll")
    JsonResultVo getUserList();

    /**
     * 获取系统用户列表
     *
     * @return
     */
    @GetMapping("/user/findById")
    JsonResultVo findUserById(@RequestParam String id);

    @PostMapping("/personnel/selectUserIdByCode")
    JsonResultVo<String> selectUserIdByCode(@RequestHeader("tenantId") String tenantId, @RequestBody List<String> codes);

    @PostMapping("/role/getAllRoles")
    JsonResultVo<List<RoleVO>> getAllRoles();


    /**
     * 根据用户ID获取
     *
     * @return
     */
    @PostMapping("/personnel/getPersonnelInfo")
    JsonResultVo getPersonnelInfo(@RequestHeader("tenantId") String tenantId, @RequestBody PersonnelDTO tempDTO);
    /**
     * 按角色id查询用户
     * @return
     */
    @PostMapping("role/getListByRoleId")
    JsonResultVo getListByRoleId(@RequestHeader("tenantId") String tenantId, @RequestBody RoleDTO roleDTO);

    /**
     * 通过事务码查询用户信息
     * @return
     */
    @PostMapping("role/getRoleListByTransCode")
    JsonResultVo getRoleListByTransCode(@RequestHeader("tenantId") String tenantId, @RequestParam String transcode,
                                        @RequestParam String buttonCode);

}
