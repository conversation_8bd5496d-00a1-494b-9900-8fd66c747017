<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.qm.ep.wf.mapper.WfConfMapper">
    <!-- 公共查询 -->
    <sql id="QuerySQL">
        select * from wf_conf
    </sql>
    <!-- 复写MP自带函数 -->
    <select id="selectByIdNew" resultType="com.qm.ep.wf.domain.entity.WfConf">
        <include refid="QuerySQL"/>
        where id = #{id}
    </select>
</mapper>
