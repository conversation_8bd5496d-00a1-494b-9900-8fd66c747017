<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.qm.ep.wf.mapper.RunFlowableActinstMapper">
    <delete id="deleteRunActinstsByIds" parameterType="java.util.List">
        delete from act_ru_actinst where ID_ in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <delete id="deleteRunActByInst" parameterType="java.util.List">
        delete from act_ru_actinst where PROC_INST_ID_ in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <delete id="deleteRunTimerJob" parameterType="java.util.List">
        delete from act_ru_timer_job where PROCESS_INSTANCE_ID_ in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <delete id="deleteRunRunJob" parameterType="java.util.List">
        delete from act_ru_job where PROCESS_INSTANCE_ID_ in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <delete id="deleteRunExecution" parameterType="java.util.List">
        delete from ACT_RU_EXECUTION where PROC_INST_ID_ in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <delete id="deleteRunTask" parameterType="java.util.List">
        delete from ACT_RU_TASK where PROC_INST_ID_ in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <delete id="deleteRunIdentityLink" parameterType="java.util.List">
        delete from ACT_RU_IDENTITYLINK where PROC_INST_ID_ in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <select id="selectRunGeByte" parameterType="java.util.List" resultType="java.util.HashMap">
        SELECT DISTINCT
        b.ID_
        FROM
        act_ru_variable a,
        act_ge_bytearray b
        WHERE
        a.BYTEARRAY_ID_ = b.ID_
        AND a.PROC_INST_ID_ in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectRunTaskLink" parameterType="java.util.List" resultType="string">
        SELECT DISTINCT
        a.ID_
        FROM
        act_ru_identitylink a,
        act_ru_task b
        WHERE
        a.TASK_ID_ = b.ID_
        AND b.PROC_INST_ID_ in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <delete id="deleteRunTaskLink" parameterType="java.util.List">
        delete from act_ru_identitylink where ID_ in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <delete id="deleteGeByte" parameterType="java.util.List">
        delete from act_ge_bytearray where ID_ in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <delete id="deleteRunVariable" parameterType="java.util.List">
        delete from act_ru_variable where PROC_INST_ID_ in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

</mapper>
