package com.qm.ep.wf.common.cmd;

import lombok.SneakyThrows;
import org.flowable.common.engine.impl.interceptor.Command;
import org.flowable.common.engine.impl.interceptor.CommandContext;
import org.flowable.engine.impl.persistence.entity.CommentEntity;
import org.flowable.engine.impl.util.CommandContextUtil;
import org.flowable.engine.task.Comment;

import java.util.Date;

public class AddCommentCmd implements Command<Comment> {

    protected String taskId;
    protected String processInstanceId;
    protected String type;
    protected String message;
    protected String oprator;
    protected Date oprDate;

    public AddCommentCmd(String taskId, String processInstanceId, String message) {
        this.taskId = taskId;
        this.processInstanceId = processInstanceId;
        this.message = message;
    }

    public AddCommentCmd(String taskId, String processInstanceId, String type, String message, String oprator, Date oprDate) {
        this.taskId = taskId;
        this.processInstanceId = processInstanceId;
        this.type = type;
        this.message = message;
        this.oprator = oprator;
        this.oprDate = oprDate;
    }

    @SneakyThrows
    public Comment execute(CommandContext commandContext) {
        CommentEntity comment = (CommentEntity) CommandContextUtil.getCommentEntityManager(commandContext).create();
        comment.setUserId(oprator);
        comment.setType(this.type == null ? "comment" : this.type);
        comment.setTime(oprDate);
        comment.setTaskId(this.taskId);
        comment.setProcessInstanceId(this.processInstanceId);
        comment.setAction("AddComment");
        String eventMessage = this.message.replaceAll("\\s+", " ");
        if (eventMessage.length() > 163) {
            eventMessage = eventMessage.substring(0, 160) + "...";
        }

        comment.setMessage(eventMessage);
        comment.setFullMessage(this.message);
        CommandContextUtil.getCommentEntityManager(commandContext).insert(comment);
        return comment;
    }

    protected String getSuspendedTaskException() {
        return "Cannot add a comment to a suspended task";
    }

    protected String getSuspendedExceptionMessage() {
        return "Cannot add a comment to a suspended execution";
    }
}
