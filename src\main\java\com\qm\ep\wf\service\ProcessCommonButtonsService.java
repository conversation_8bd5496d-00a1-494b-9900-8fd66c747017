package com.qm.ep.wf.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qm.ep.wf.domain.bean.ProcessCommonButtonsDO;
import com.qm.ep.wf.domain.dto.ProcessCommonButtonsDTO;
import com.qm.ep.wf.domain.dto.TaskAuthDTO;
import com.qm.ep.wf.domain.dto.TaskTranAuthDTO;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.service.IQmBaseService;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-15
 */
public interface ProcessCommonButtonsService extends IQmBaseService<ProcessCommonButtonsDO> {

    public List<ProcessCommonButtonsDTO> getButtonsByProc(TaskTranAuthDTO taskTranAuthDTO);

    List<ProcessCommonButtonsDO> getButtons(String processDefKey, String buttonCode);

    public List<ProcessCommonButtonsDTO> getButtonsByParams(TaskTranAuthDTO taskTranAuthDTO);

    List<ProcessCommonButtonsDTO> selectButtonsByAuth(IPage<TaskAuthDTO> page, @Param("ew") Wrapper<TaskAuthDTO> queryWrapper);

    /**
     * @description 复制按钮
     * <AUTHOR>
     * @date 2020/9/25 16:17
     */
    JsonResultVo copyButton(Map<String, String> map);

    String findButtonLangInfo(String buttonCode, String processDefKey);

    List<ProcessCommonButtonsDO> getButtonsBy(String processDefKey, String buttonCode,Integer operationFlag);
}
