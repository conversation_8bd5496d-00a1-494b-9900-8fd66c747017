package com.qm.ep.wf.constant;

/**
 * <AUTHOR> @date
 */
public class FlowableConstant {
    /**
     * 这个类不能实例化
     */
    private FlowableConstant() {
    }

    /**
     * 约定的发起者节点id-taskDefinitionKey
     */
    public static final String INITIATOR = "__initiator__";
    public static final String SPECIAL_GATEWAY_BEGIN_SUFFIX = "_begin";
    public static final String SPECIAL_GATEWAY_END_SUFFIX = "_end";
    public static final String MSG_NO_PROMPT = "noPrompt";
    public static final String PROCESS_INSTANCE_FORM_DATA = "processInstanceFormData";
    public static final String PROCESS_INSTANCE_ACTION_INFO = "processInstanceActionInfo";
    /**
     * 自动跳过节点设置属性
     */
    public static final String FLOWABLE_SKIP_EXPRESSION_ENABLED = "_FLOWABLE_SKIP_EXPRESSION_ENABLED";
    public static final String PROCESS_TASK_MESSAGE = "processTaskMessage";
    public static final String PROCESS_TASK_MEGS = "processTaskMessages";
    public static final String PROCESS_INSTANCE_VCONDITION = "outcome";
    public static final String MULTI_INSTANCE_RESULT = "outResult";
    public static final String TASK_ATTACH_VARIABLE = "execVariable";
    public static final String TASK_ATTACH_CONDITION = "conditionVariable";
    public static final String PROCESS_BUSSINESS_TABLE = "busiTableName";
    public static final String PROCESS_STATE_FIELDNAME = "stateFieldName";
    public static final String PROCESS_KEY_FIELDNAME = "busiKeyFieldName";
    public static final String IDENTITY_USER = "1";
    public static final String IDENTITY_GROUP = "2";
    public static final String ID = "id";
    public static final String CATEGORY = "category";
    public static final String KEY = "key";
    public static final String MODULE = "vmodule";
    public static final String NAME = "name";
    public static final String VERSION = "version";
    public static final String SUSPENDED = "suspended";
    public static final String LATEST_VERSION = "latestVersion";
    public static final String STARTABLE_BY_USER = "startableByUser";
    public static final String TENANT_ID = "t_Id";
    public static final String PROCESS_INSTANCE_ID = "processInstanceId";
    public static final String PROCESS_INSTANCE_NAME = "processInstanceName";
    public static final String PROCESS_DEFINITION_NAME = "processDefinitionName";
    public static final String PROCESS_DEFINITION_KEY = "processDefinitionKey";
    public static final String PROCESS_DEFINITION_ID = "processDefinitionId";
    public static final String BUSINESS_KEY = "businessKey";
    public static final String BUSINESS_KEYS = "businessKeys";
    public static final String INVOLVED_USER = "involvedUser";
    public static final String FINISHED = "finished";
    public static final String SUPER_PROCESS_INSTANCE_ID = "superProcessInstanceId";
    public static final String EXCLUDE_SUBPROCESSES = "excludeSubprocesses";
    public static final String FINISHED_AFTER = "finishedAfter";
    public static final String FINISHED_BEFORE = "finishedBefore";
    public static final String STARTED_AFTER = "startedAfter";
    public static final String START_TIME = "startTime";
    public static final String STARTED_BEFORE = "startedBefore";
    public static final String STARTED_BY = "startedBy";
    public static final String START_BY_ME = "startByMe";
    public static final String TASK_ID = "taskId";
    public static final String REPEAT_VALID_FLAG = "repeatValidFlag";
    public static final String REPEAT_MSG = "repeatMsg";
    public static final String TASK_NAME = "taskName";
    public static final String TASK_DESCRIPTION = "taskDescription";
    public static final String TASK_DEFINITION_KEY = "taskDefinitionKey";
    public static final String TASK_ASSIGNEE = "taskAssignee";
    public static final String TASK_OWNER = "taskOwner";
    public static final String TASK_INVOLVED_USER = "taskInvolvedUser";
    public static final String TASK_PRIORITY = "taskPriority";
    public static final String PARENT_TASK_ID = "parentTaskId";
    public static final String DUE_DATE_AFTER = "dueDateAfter";
    public static final String DUE_DATE_BEFORE = "dueDateBefore";
    public static final String TASK_CREATED_BEFORE = "taskCreatedBefore";
    public static final String TASK_CREATED_AFTER = "taskCreatedAfter";
    public static final String TASK_COMPLETED_BEFORE = "taskCompletedBefore";
    public static final String TASK_COMPLETED_AFTER = "taskCompletedAfter";
    public static final String TASK_CANDIDATE_USER = "taskCandidateUser";
    public static final String TASK_CANDIDATE_GROUP = "taskCandidateGroup";
    public static final String TASK_CANDIDATE_GROUPS = "taskCandidateGroups";
    public static final String PROCESS_INSTANCE_BUSINESS_KEY = "processInstanceBusinessKey";
    public static final String PROCESS_FINISHED = "processFinished";
    public static final String EXECUTION_ID = "executionId";
    public static final String FILE_EXTENSION_BAR = ".bar";
    public static final String FILE_EXTENSION_ZIP = ".zip";
    public static final String NO_PERMISSION = "User does not have permission";
    public static final String SUB_PROCESS_MSG = "This is a subProcess";
    public static final String OUT_GOING = "outgoing";
    public static final String MESSAGE_TIME = "messageTime";
    public static final String NO_LISTENER = "noListener";
    public static final String NO_EXEC_LISTENER = "noExecListener";
    public static final String LASTEST_BUTTON_CODE = "lastestButtonCode";
    public static final String SKIP_BUSI = "skipbusi";
    public static final String BUTTONTYPE_AGREE = "agree";
    public static final String BUTTONTYPE_REJECT = "reject";
    public static final String BUTTONTYPE_GIVEUP = "giveup";
    public static final String CUSTOM_UNIQUE_FLAG = "customUniqueFlag";
    public static final String SKIP_SIGN = "skipSign";
    public static final String LISTENERTYPE = "listenerType";
    public static final String UPDATE_BUSI_TIMEOUT = "feign.RetryableException: Read timed out executing POST";
    public static final String UPDATE_WF_TIMEOUT = "Lock wait timeout exceeded; try restarting transaction";
}
