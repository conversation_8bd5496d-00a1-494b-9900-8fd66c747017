package com.qm.ep.wf.common;

import com.qm.ep.wf.constant.FlowableConstant;
import com.qm.ep.wf.domain.vo.*;
import com.qm.ep.wf.exception.FlowableTaskException;
import com.qm.ep.wf.remote.SysFeignRemote;
import com.qm.ep.wf.util.CommonUtil;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.RedisUtils;
import org.apache.commons.lang3.StringUtils;
import org.flowable.engine.FormService;
import org.flowable.engine.HistoryService;
import org.flowable.engine.TaskService;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.impl.persistence.entity.CommentEntityImpl;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.engine.task.Comment;
import org.flowable.identitylink.api.IdentityLink;
import org.flowable.identitylink.api.IdentityLinkInfo;
import org.flowable.identitylink.api.IdentityLinkType;
import org.flowable.identitylink.api.history.HistoricIdentityLink;
import org.flowable.task.api.Task;
import org.flowable.task.api.TaskInfo;
import org.flowable.task.api.TaskQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR> @date
 */
@Component
public class ResponseFactory {
    private final FormService formService;
    private final HistoryService historyService;
    @Autowired
    protected TaskService taskService;

    @Autowired
    private SysFeignRemote sysFeignRemote;

    @Autowired
    private RedisUtils redisUtils;

    private static final String KEY = "wf:userList";
    private static final String MODULE = "common-wf";

    @Autowired
    public ResponseFactory(FormService formService, HistoryService historyService) {
        this.formService = formService;
        this.historyService = historyService;
    }

    public List<ProcessDefinitionResponse> createProcessDefinitionResponseList(
            List<ProcessDefinition> processDefinitions) {
        List<ProcessDefinitionResponse> responseList = new ArrayList<>();
        for (ProcessDefinition instance : processDefinitions) {
            responseList.add(createProcessDefinitionResponse(instance));
        }
        return responseList;
    }

    public ProcessDefinitionResponse createProcessDefinitionResponse(ProcessDefinition processDefinition) {
        if (processDefinition.hasStartFormKey()) {
            String startFormKey = formService.getStartFormKey(processDefinition.getId());
            return createProcessDefinitionResponse(processDefinition, startFormKey);
        } else {
            return createProcessDefinitionResponse(processDefinition, null);
        }
    }

    public ProcessDefinitionResponse createProcessDefinitionResponse(ProcessDefinition processDefinition,
                                                                     String formKey) {
        ProcessDefinitionResponse response = new ProcessDefinitionResponse();
        response.setId(processDefinition.getId());
        response.setKey(processDefinition.getKey());
        response.setVersion(processDefinition.getVersion());
        response.setCategory(processDefinition.getCategory());
        response.setName(processDefinition.getName());
        response.setDescription(processDefinition.getDescription());
        response.setSuspended(processDefinition.isSuspended() ? "true" : "false");
        response.setGraphicalNotationDefined(processDefinition.hasGraphicalNotation());
        response.setTenantId(processDefinition.getTenantId());
        response.setFormKey(formKey);
        return response;
    }

    public List<ProcessInstanceResponse> createProcessInstanceResponseList(List<ProcessInstance> processInstances) {
        List<ProcessInstanceResponse> responseList = new ArrayList<>();
        for (ProcessInstance instance : processInstances) {
            responseList.add(createProcessInstanceResponse(instance));
        }
        return responseList;
    }

    private ProcessInstanceResponse createProcessInstanceResponse(ProcessInstance processInstance) {
        ProcessInstanceResponse result = new ProcessInstanceResponse();
        result.setId(processInstance.getId());
        result.setSuspended(processInstance.isSuspended());
        result.setProcessDefinitionId(processInstance.getProcessDefinitionId());
        result.setProcessDefinitionKey(processInstance.getProcessDefinitionKey());
        result.setProcessDefinitionName(processInstance.getProcessDefinitionName());
        result.setProcessDefinitionVersion(processInstance.getProcessDefinitionVersion());
        result.setStartTime(processInstance.getStartTime());
        result.setStartUserId(processInstance.getStartUserId());
        result.setCurrentActivityId(processInstance.getActivityId());
        result.setBusinessKey(processInstance.getBusinessKey());
        result.setTenantId(processInstance.getTenantId());
        return result;
    }

    public List<HistoricProcessInstanceResponse> createHistoricProcessInstanceResponseList(
            List<HistoricProcessInstance> processInstances) {
        List<HistoricProcessInstanceResponse> responseList = new ArrayList<>();
        for (HistoricProcessInstance instance : processInstances) {
            responseList.add(createHistoricProcessInstanceResponse(instance));
        }
        return responseList;
    }

    private HistoricProcessInstanceResponse createHistoricProcessInstanceResponse(
            HistoricProcessInstance processInstance) {
        HistoricProcessInstanceResponse result = new HistoricProcessInstanceResponse();
        createHistoricProcessInstanceResponse(result, processInstance);
        return result;
    }

    public ProcessInstanceDetailResponse createProcessInstanceDetailResponse(HistoricProcessInstance hisProcessInstance,
                                                                             ProcessInstance processInstance) {
        ProcessInstanceDetailResponse result = new ProcessInstanceDetailResponse();
        createHistoricProcessInstanceResponse(result, hisProcessInstance);
        result.setStartUserName(getUserName(hisProcessInstance.getStartUserId()));
        result.setDeleteReason(hisProcessInstance.getDeleteReason());
        if (processInstance != null) {
            result.setSuspended(processInstance.isSuspended());
        }
        return result;
    }

    private void createHistoricProcessInstanceResponse(HistoricProcessInstanceResponse result,
                                                       HistoricProcessInstance processInstance) {
        result.setId(processInstance.getId());
        //根据流程实例ID，获取流程待办节点
        TaskQuery taskQuery = taskService.createTaskQuery().processInstanceId(processInstance.getId()).active();
        long taskCount = taskQuery.count();
        Task task = null;
        if (taskCount == 1) {
            task = taskQuery.singleResult();
        } else if (taskCount > 1) {
            task = taskQuery.list().get(0);
        }
        if (null != task) {
            result.setActiveTask(task.getName());
        } else {
            result.setActiveTask("");
        }
        result.setName(processInstance.getName());
        result.setBusinessKey(processInstance.getBusinessKey());
        result.setStartTime(processInstance.getStartTime());
        result.setEndTime(processInstance.getEndTime());
        result.setDurationInMillis(processInstance.getDurationInMillis());
        result.setProcessDefinitionId(processInstance.getProcessDefinitionId());
        result.setProcessDefinitionKey(processInstance.getProcessDefinitionKey());
        result.setProcessDefinitionName(processInstance.getProcessDefinitionName());
        result.setProcessDefinitionVersion(processInstance.getProcessDefinitionVersion());
        result.setStartActivityId(processInstance.getStartActivityId());
        result.setStartUserId(processInstance.getStartUserId());
        result.setSuperProcessInstanceId(processInstance.getSuperProcessInstanceId());
        result.setTenantId(processInstance.getTenantId());
    }

    public List<TaskResponse> createTaskResponseList(List<TaskInfo> tasks) {
        //新增businesskey
        Map<String, String> processInstanceNames = new HashMap<>(16);
        Set<String> processInstanceIds = new HashSet<>();
        for (TaskInfo task : tasks) {
            if (task.getProcessInstanceId() != null) {
                processInstanceIds.add(task.getProcessInstanceId());
            }
        }
        if (!processInstanceIds.isEmpty()) {
            //这里添加businesskey处理
            historyService.createHistoricProcessInstanceQuery().processInstanceIds(processInstanceIds).list().forEach(
                    processInstance -> {
                        processInstanceNames.put(processInstance.getId(), processInstance.getName());
                        processInstanceNames.put(processInstance.getId() + "businessKey", processInstance.getBusinessKey());
                        processInstanceNames.put(processInstance.getId() + "processDefKey", processInstance.getProcessDefinitionKey());
                    }
            );

        }
        List<TaskResponse> responseList = new ArrayList<>();
        for (TaskInfo task : tasks) {

            TaskResponse result = new TaskResponse(task, processInstanceNames.get(task.getProcessInstanceId()),
                    processInstanceNames.get(task.getProcessInstanceId() + "businessKey"), processInstanceNames.get(task.getProcessInstanceId() + "processDefKey"));

            result.setAssignee(getUserName(task.getAssignee()));
            responseList.add(result);
        }
        return responseList;
    }

    public TaskResponse createTaskResponse(Task taskInstance) {
        TaskResponse result = new TaskResponse();
        createTaskResponse(result, taskInstance);
        return result;
    }

    private void createTaskResponse(TaskResponse result, Task taskInstance) {
        result.setId(taskInstance.getId());
        result.setName(taskInstance.getName());
        result.setOwner(taskInstance.getOwner());
        result.setTaskDefinitionKey(taskInstance.getTaskDefinitionKey());
        result.setCreateTime(taskInstance.getCreateTime());
        result.setAssignee(taskInstance.getAssignee());
        result.setDescription(taskInstance.getDescription());
        result.setDueDate(taskInstance.getDueDate());
        result.setDelegationState(taskInstance.getDelegationState());
        result.setFormKey(taskInstance.getFormKey());
        result.setParentTaskId(taskInstance.getParentTaskId());
        result.setPriority(taskInstance.getPriority());
        result.setSuspended(taskInstance.isSuspended());
        result.setTenantId(taskInstance.getTenantId());
        result.setCategory(taskInstance.getCategory());
        result.setProcessDefinitionId(taskInstance.getProcessDefinitionId());
        result.setProcessInstanceId(taskInstance.getProcessInstanceId());
    }

    public List<IdentityResponse> createTaskIdentityResponseList(List<HistoricIdentityLink> historicIdentityLinks) {
        List<IdentityResponse> responseList = new ArrayList<>();
        for (HistoricIdentityLink identityLink : historicIdentityLinks) {
            if (IdentityLinkType.CANDIDATE.equals(identityLink.getType())) {
                responseList.add(createIdentityResponse(identityLink));
            }
        }
        return responseList;
    }

    public List<IdentityResponse> createIdentityResponseList(List<IdentityLink> identityLinks) {
        List<IdentityResponse> responseList = new ArrayList<>();
        for (IdentityLinkInfo identityLink : identityLinks) {
            responseList.add(createIdentityResponse(identityLink));
        }
        return responseList;
    }

    private IdentityResponse createIdentityResponse(IdentityLinkInfo identityLink) {
        IdentityResponse identityResponse = new IdentityResponse();
        if (identityLink.getGroupId() != null) {
            identityResponse.setIdentityType(FlowableConstant.IDENTITY_GROUP);
            identityResponse.setIdentityId(identityLink.getGroupId());
            identityResponse.setIdentityName(getGroupName(identityLink.getGroupId()));
        } else if (identityLink.getUserId() != null) {
            identityResponse.setIdentityType(FlowableConstant.IDENTITY_USER);
            identityResponse.setIdentityId(identityLink.getUserId());
            identityResponse.setIdentityName(getUserName(identityLink.getUserId()));
        }
        return identityResponse;
    }

    public List<CommentResponse> createCommentResponseList(List<Comment> comments) {
        List<CommentResponse> responseList = new ArrayList<>();
        if (!comments.isEmpty()) {
            List taskInfos = new ArrayList();
            if (BootAppUtil.isnotNullOrEmpty(comments.get(0).getProcessInstanceId()))
                taskInfos = historyService.createHistoricTaskInstanceQuery().processInstanceId(comments.get(0).getProcessInstanceId()).list();
            for (Comment comment : comments) {
                responseList.add(createCommentResponse(comment, taskInfos));
            }
        }
        return responseList;
    }

    public CommentResponse createCommentResponse(Comment comment, List taskInfos) {
        CommentResponse commentResponse = new CommentResponse();
        commentResponse.setId(comment.getId());
        commentResponse.setUserId(comment.getUserId());
        commentResponse.setUserName(getUserName(comment.getUserId()));
        commentResponse.setType(comment.getType());
        commentResponse.setTypeDesc(CommonUtil.isEmptyStr(CommentTypeEnum.getEnumMsgByType(comment.getType())) ? comment.getType() : CommentTypeEnum.getEnumMsgByType(comment.getType()));
        commentResponse.setTime(comment.getTime());
        commentResponse.setProcessInstanceId(comment.getProcessInstanceId());
        commentResponse.setTaskId(comment.getTaskId());
        if (BootAppUtil.isnotNullOrEmpty(comment.getTaskId())) {
            if (!taskInfos.isEmpty()) {
                List<TaskInfo> taskInfoList = (List<TaskInfo>) taskInfos;
                TaskInfo task = taskInfoList.stream().filter(b -> comment.getTaskId().equals(b.getId())).findFirst()
                        .orElse(null);
                if (null != task)
                    commentResponse.setTaskName(task.getName());
            }
        }
        commentResponse.setFullMessage(comment.getFullMessage());
        if (comment.getType().equals("event")) {
            commentResponse.setAttachmentName(((CommentEntityImpl) comment).getMessage());
            commentResponse.setHasAttachment(true);
        }
        return commentResponse;
    }

    private String getUserName(String userId) {
        if (StringUtils.isEmpty(userId)) {
            return "";
        }
        Map userMap = new HashMap();
        //数据存储key
        String userListKey = redisUtils.keyBuilder(MODULE, "userName", userId);
        //先读取缓存数据
        Object users = redisUtils.get(userListKey);
        if (null != users) {
            userMap = (HashMap) users;
        } else {

            JsonResultVo userDataList = sysFeignRemote.findUserById(userId);
            if (userDataList.getCode() == 500) {
                throw new FlowableTaskException(userDataList.getMsg());
            }
            if (userDataList.getData() != null) {
                for (LinkedHashMap userDO : (ArrayList<LinkedHashMap>) userDataList.getData()) {
                    if (!BootAppUtil.isNullOrEmpty(userDO.get("id"))) {
                        userMap.put(userDO.get("id"), userDO.get("vrealname"));
                    }
                }
                redisUtils.set(userListKey, userMap, 600);
                //存修改标记key
                redisUtils.lPush(KEY, userListKey);
            }
        }
        return null != userMap.get(userId) ? userMap.get(userId).toString() : userId;
    }

    private String getGroupName(String groupId) {
        return groupId;
    }
}
