spring:
  profiles:
    active: dev #指定激活哪个环境配置，激活后，第一个文档内容失效;不指定时，以第一个文档为准
  application:
    name: common-wf-base
server:
  port: 8192
--- #"---"用于分隔不同的profiles（）文档块
spring:
  profiles: dev
  # 配置中心
  cloud:
    config:
      uri: http://localhost:7005/
      fail-fast: true
      name: ${spring.application.name}
      profile: ${spring.profiles.active}
--- #"---"用于分隔不同的profiles（）文档块
spring:
  profiles: nacos
  # 配置中心
  cloud:
    nacos:
      config:
        server-addr: 127.0.0.1:8848
        namespace: ep-dev
        # 组名也需要加上
        group: TDS_EP_NACOS_GROUP
        #配置文件类型，目前只支持 properties 和 yaml 类型，默认为 properties
        file-extension: yaml
        shared-configs[0]:
          data-id: global-config.yml # 配置文件名-Data Id
          group: TDS_EP_NACOS_GROUP   # 默认为DEFAULT_GROUP
