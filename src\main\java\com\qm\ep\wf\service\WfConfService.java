package com.qm.ep.wf.service;

import com.qm.ep.wf.domain.dto.WfConfDTO;
import com.qm.ep.wf.domain.entity.WfConf;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.service.IQmBaseService;

import java.util.Set;

/**
 *
 */
public interface WfConfService extends IQmBaseService<WfConf> {
    /**
     * 获取wf_conf列表
     *
     * @param query 查询条件
     * @return WfConf
     */
    QmPage<WfConf> getWfConf(WfConfDTO query);

    /**
     * @description 根据模块查询流程定义列表
     * <AUTHOR>
     * @date 2020/9/30 15:50
     */
    public Set<String> getWfConfByModule(String module, Integer type);

    /**
     * 根据流程名称获取表单信息
     *
     * @param wfName
     * @return
     */
    WfConf getWfConfByWF(String wfName);

    WfConf getWfConfByVmodel(String wfName);

    /**
     * 根据流程代码获取配置记录
     *
     * @param vwfCode 流程代码
     * @return wfConf
     */
    WfConf getConditionWfConf(String vwfCode);

    /**
     * 更新启用状态
     *
     * @param wfConf 实体
     */
    void updateWfConf(WfConf wfConf);

    boolean getProcessAuditAuthCheck(String procDefKey);
}
