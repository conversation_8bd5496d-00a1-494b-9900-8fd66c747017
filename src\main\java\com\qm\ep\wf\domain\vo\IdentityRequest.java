package com.qm.ep.wf.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> @date
 */
@ApiModel(description = "身份请求")
@Data
public class IdentityRequest {
    @ApiModelProperty("进程定义 ID")
    private String processDefinitionId;
    @ApiModelProperty("任务 ID")
    private String taskId;
    @ApiModelProperty("身份 ID")
    private String identityId;
    @ApiModelProperty("标识类型")
    private String identityType;
}
