package com.qm.ep.wf.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.google.common.collect.Lists;
import com.qm.ep.wf.common.CommentTypeEnum;
import com.qm.ep.wf.common.ResponseFactory;
import com.qm.ep.wf.common.cmd.AddCommentCmd;
import com.qm.ep.wf.common.cmd.BackUserTaskCmd;
import com.qm.ep.wf.common.exception.FlowableNoPermissionException;
import com.qm.ep.wf.constant.FlowableConstant;
import com.qm.ep.wf.domain.bean.ActHiCommentCodeDO;
import com.qm.ep.wf.domain.bean.ProcessCommonButtonsDO;
import com.qm.ep.wf.domain.bean.ProcessCounterSignDO;
import com.qm.ep.wf.domain.dto.ProcessCounterSignDTO;
import com.qm.ep.wf.domain.vo.*;
import com.qm.ep.wf.mapper.ActHiCommentCodeMapper;
import com.qm.ep.wf.mapper.HisFlowableActinstMapper;
import com.qm.ep.wf.service.*;
import com.qm.ep.wf.util.*;
import com.qm.ep.wf.wapper.IListWrapper;
import com.qm.ep.wf.wapper.TaskTodoListWrapper;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.I18nUtil;
import com.qm.tds.util.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.flowable.bpmn.constants.BpmnXMLConstants;
import org.flowable.bpmn.model.Process;
import org.flowable.bpmn.model.*;
import org.flowable.common.engine.api.FlowableException;
import org.flowable.common.engine.api.FlowableIllegalArgumentException;
import org.flowable.common.engine.api.FlowableObjectNotFoundException;
import org.flowable.common.engine.impl.identity.Authentication;
import org.flowable.engine.*;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.runtime.ActivityInstance;
import org.flowable.engine.runtime.Execution;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.engine.task.Comment;
import org.flowable.identitylink.api.IdentityLink;
import org.flowable.identitylink.api.IdentityLinkInfo;
import org.flowable.identitylink.api.IdentityLinkType;
import org.flowable.identitylink.api.history.HistoricIdentityLink;
import org.flowable.idm.api.Group;
import org.flowable.idm.api.User;
import org.flowable.task.api.Task;
import org.flowable.task.api.TaskInfo;
import org.flowable.task.api.TaskQuery;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.flowable.task.service.impl.persistence.entity.TaskEntity;
import org.flowable.variable.api.history.HistoricVariableInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> @date
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class FlowableTaskServiceImpl implements FlowableTaskService {
    @Autowired
    protected IdentityService identityService;
    @Autowired
    private ProcessCommonButtonsService processCommonButtonsService;
    @Autowired
    protected RepositoryService repositoryService;
    @Autowired
    protected TaskService taskService;
    @Autowired
    protected RuntimeService runtimeService;
    @Autowired
    protected HistoryService historyService;
    @Autowired
    protected PermissionServiceImpl permissionService;
    @Autowired
    protected ResponseFactory responseFactory;
    @Autowired
    protected ManagementService managementService;
    @Autowired
    protected FormService formService;
    @Autowired
    protected WfConfService wfConfService;
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private ProcessCounterSignService processCounterSignService;
    @Autowired
    private ProcessDefinitionService processDefinitionService;
    @Autowired
    private HisFlowableActinstMapper hisFlowableActinstMapper;
    @Autowired
    private I18nUtil i18nUtil;
    @Autowired
    private ActHiCommentCodeMapper actHiCommentCodeMapper;


    @Value("${ep.lang.multiFlag:false}")
    private Boolean multiFlag;

    @Override
    public TaskResponse getTask(String taskId) {
        String userId = LoginUtil.getOperatorId();
        HistoricTaskInstance taskHis = permissionService.validateReadPermissionOnTask(taskId, userId, true, true);
        TaskResponse rep = null;
        ProcessDefinition processDefinition = null;
        String formKey = null;
        Object renderedTaskForm = null;
        HistoricTaskInstance parentTask = null;
        if (StringUtils.isNotEmpty(taskHis.getProcessDefinitionId())) {
            processDefinition = repositoryService.getProcessDefinition(taskHis.getProcessDefinitionId());
            formKey = formService.getTaskFormKey(processDefinition.getId(), taskHis.getTaskDefinitionKey());
            if (null == taskHis.getEndTime() && null != formKey && formKey.length() > 0) {
                renderedTaskForm = formService.getRenderedTaskForm(taskId);
            }
        }
        if (StringUtils.isNotEmpty(taskHis.getParentTaskId())) {
            parentTask = historyService.createHistoricTaskInstanceQuery().taskId(taskHis.getParentTaskId())
                    .singleResult();
        }
        rep = new TaskResponse(taskHis, processDefinition, parentTask, null);
        rep.setFormKey(formKey);
        rep.setRenderedTaskForm(renderedTaskForm);

        fillPermissionInformation(rep, taskHis, userId);
        // Populate the people
        populateAssignee(taskHis, rep);
        rep.setInvolvedPeople(getInvolvedUsers(taskId));

        Task task = null;
        if (taskHis.getEndTime() == null) {
            task = taskService.createTaskQuery().taskId(taskId).singleResult();
            rep.setSuspended(task.isSuspended());
            rep.setDelegationState(task.getDelegationState());
        }
        rep.setOwnerName(getUserName(taskHis.getOwner()));
        rep.setAssigneeName(getUserName(taskHis.getAssignee()));
        return rep;
    }

    @Override
    public List<TaskResponse> getSubTasks(String taskId) {
        String userId = LoginUtil.getOperatorId();
        HistoricTaskInstance parentTask = permissionService.validateReadPermissionOnTask(taskId, userId, true, true);
        List<Task> subTasks = taskService.getSubTasks(taskId);
        List<TaskResponse> subTasksRepresentations = new ArrayList<>(subTasks.size());
        for (Task subTask : subTasks) {
            TaskResponse representation = new TaskResponse(subTask, parentTask);
            fillPermissionInformation(representation, subTask, userId);
            populateAssignee(subTask, representation);
            representation.setInvolvedPeople(getInvolvedUsers(subTask.getId()));
            subTasksRepresentations.add(representation);
        }
        return subTasksRepresentations;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TaskResponse updateTask(TaskUpdateRequest taskUpdateRequest) {
        String userId = LoginUtil.getOperatorId();
        permissionService.validateReadPermissionOnTask(taskUpdateRequest.getId(), userId, false, false);
        Task task = getTaskNotNull(taskUpdateRequest.getId());
        task.setName(taskUpdateRequest.getName());
        task.setDescription(taskUpdateRequest.getDescription());
        task.setAssignee(taskUpdateRequest.getAssignee());
        task.setOwner(taskUpdateRequest.getOwner());
        task.setDueDate(taskUpdateRequest.getDueDate());
        task.setPriority(taskUpdateRequest.getPriority());
        task.setCategory(taskUpdateRequest.getCategory());
        taskService.saveTask(task);
        return new TaskResponse(task);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void assignTask(TaskRequest taskRequest) {
        String taskId = taskRequest.getTaskId();
        String assignee = taskRequest.getUserId();
        String userId = LoginUtil.getOperatorId();
        Task task = permissionService.validateAssignPermissionOnTask(taskId, userId, assignee);
        addComment(taskId, task.getProcessInstanceId(), userId, CommentTypeEnum.ZB, taskRequest.getMessage());
        taskService.setAssignee(task.getId(), assignee);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void involveUser(String taskId, String involveUserId) {
        Task task = getTaskNotNull(taskId);
        String userId = LoginUtil.getOperatorId();
        permissionService.validateReadPermissionOnTask(task.getId(), userId, false, false);
        if (null != involveUserId && involveUserId.length() > 0) {
            taskService.addUserIdentityLink(taskId, involveUserId, IdentityLinkType.PARTICIPANT);
        } else {
            throw new FlowableException("User id is required");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeInvolvedUser(String taskId, String involveUserId) {
        Task task = getTaskNotNull(taskId);
        String userId = LoginUtil.getOperatorId();
        permissionService.validateReadPermissionOnTask(task.getId(), userId, false, false);
        if (null != involveUserId && involveUserId.length() > 0) {
            taskService.deleteUserIdentityLink(taskId, involveUserId, IdentityLinkType.PARTICIPANT);
        } else {
            throw new FlowableException("User id is required");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void claimTask(TaskRequest taskRequest) {
        String taskId = taskRequest.getTaskId();
        String userId = LoginUtil.getOperatorId();
        TaskInfo task = permissionService.validateReadPermissionOnTask2(taskId, userId, false, false);
        addComment(taskId, task.getProcessInstanceId(), userId, CommentTypeEnum.RL, taskRequest.getMessage());
        taskService.claim(taskId, userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void unclaimTask(TaskRequest taskRequest) {
        String taskId = taskRequest.getTaskId();
        String userId = LoginUtil.getOperatorId();
        TaskInfo task = getTaskNotNull(taskId);

        if (FlowableConstant.INITIATOR.equals(task.getTaskDefinitionKey())) {
            throw new FlowableNoPermissionException("Initiator cannot unclaim the task");
        }

        addComment(taskId, task.getProcessInstanceId(), userId, CommentTypeEnum.QXRL, taskRequest.getMessage());
        taskService.unclaim(taskId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addIdentiyLinkForUser(Task task, String userId, String linkType) {
        List<IdentityLink> identityLinks = taskService.getIdentityLinksForTask(task.getId());
        boolean isOldUserInvolved = false;
        for (IdentityLink identityLink : identityLinks) {
            isOldUserInvolved = userId.equals(identityLink.getUserId())
                    && (identityLink.getType().equals(IdentityLinkType.PARTICIPANT)
                    || identityLink.getType().equals(IdentityLinkType.CANDIDATE));
            if (isOldUserInvolved) {
                break;
            }
        }
        if (!isOldUserInvolved) {
            taskService.addUserIdentityLink(task.getId(), userId, linkType);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delegateTask(TaskRequest taskRequest) {
        String taskId = taskRequest.getTaskId();
        String delegater = taskRequest.getUserId();
        String userId = LoginUtil.getOperatorId();
        Task task = permissionService.validateDelegatePermissionOnTask(taskId, userId, delegater);
        addComment(taskId, task.getProcessInstanceId(), userId, CommentTypeEnum.WP, taskRequest.getMessage());
        taskService.delegateTask(task.getId(), delegater);
    }


    @Transactional(rollbackFor = Exception.class)
    @SuppressWarnings({"all", "squid:S1192"})
    public void batchCompleteTask(List<Task> taskList, TaskRequest taskRequest, List<String> msgs, JsonResultVo resultObj,
                                  Map<String, Object> requestParams, String customUniqueFlag, List<String> businessKeys,
                                  List<Map> businessMsgs, List<Map> hisProcessInstances, Integer failNum) {
        String listenerType = "";
        if (null != requestParams.get("listenerType")) {
            listenerType = requestParams.get("listenerType").toString();
        }

        //增加按钮code-----20220319--------
        taskRequest.setCode(requestParams.get("code").toString());

        Map<String, Object> taskValues = taskRequest.getValues();
        if (null != taskValues) {
            taskValues.put(FlowableConstant.BUSINESS_KEYS, businessKeys);
        }
        for (int i = 0; i < taskList.size(); i++) {


            taskRequest.setTaskId(taskList.get(i).getId());
            Task currentTask = taskList.get(i);
            //如果不是最后一条，不执行监听
            if (i != businessKeys.size() - 1) {
                taskValues.put(FlowableConstant.NO_LISTENER, true);
            } else {
                taskValues.put(FlowableConstant.NO_LISTENER, false);
            }
            //如果是批量last
            taskValues.put(FlowableConstant.PROCESS_TASK_MEGS, businessMsgs);
            taskValues.put(FlowableConstant.LISTENERTYPE, listenerType);
            Map<String, Object> execMap = completeTask(taskRequest);
            if (null != execMap && !execMap.isEmpty()) {
                resultObj.setData(execMap);
            }
            log.info("执行任务:" + taskList.get(0).getTaskDefinitionKey());
        }
    }

    private String buildLock(String lockKey) {
        StringBuilder sb = new StringBuilder();
        sb.append(lockKey);
        //sb.append("_");
        //sb.append(LoginUtil.getOperatorId());
        String lock = sb.toString().intern();
        log.info("[" + Thread.currentThread().getName() + "]构建了锁[" + lock + "]");
        return lock;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> completeTask(TaskRequest taskRequest) {
        String taskId = taskRequest.getTaskId();
        String currUserId = LoginUtil.getOperatorId();
        if (!BootAppUtil.isNullOrEmpty(taskRequest.getOprtor())) {
            currUserId = taskRequest.getOprtor();
        }
        Task task = getTaskNotNull(taskId);
        Map<String, Object> completeVariables = null;
        if (null != taskRequest.getValues() && !taskRequest.getValues().isEmpty()) {
            completeVariables = taskRequest.getValues();
            // 允许任务表单修改流程表单场景 begin
            // 与前端约定：流程表单变量名为 processInstanceFormData，且只有流程表单startFormKey=taskFormKey时才允许修改该变量的值，防止恶意节点修改流程表单内容
            if (completeVariables.containsKey(FlowableConstant.PROCESS_INSTANCE_FORM_DATA)) {
                String startFormKey = formService.getStartFormKey(task.getProcessDefinitionId());
                String taskFormKey = formService.getTaskFormKey(task.getProcessDefinitionId(),
                        task.getTaskDefinitionKey());
                boolean modifyProcessInstanceFormData = CommonUtil.isNotEmptyStr(startFormKey)
                        && CommonUtil.isNotEmptyStr(taskFormKey) && startFormKey.equals(taskFormKey);
                if (!modifyProcessInstanceFormData) {
                    throw new FlowableNoPermissionException("User does not have permission");
                }
            }
            // 允许任务表单修改流程表单场景 end
        }
        String actionName = "";
        String actionGrp = "";
        if (null != completeVariables) {
            Object actionInfo = completeVariables.get(FlowableConstant.PROCESS_INSTANCE_ACTION_INFO);
            if (actionInfo instanceof Map) {
                Map map = ((Map) actionInfo);
                actionName = BootAppUtil.isNullOrEmpty(map.get("name")) ? "" : map.get("name").toString();
                actionGrp = BootAppUtil.isNullOrEmpty(map.get("group")) ? "" : map.get("group").toString();
            } else {
                actionName = FlowableConstant.INITIATOR.equals(task.getTaskDefinitionKey()) ? CommentTypeEnum.CXTJ.getName() : CommentTypeEnum.WC.getName();
            }
            //流程变量中添加处理意见
            completeVariables.put(FlowableConstant.PROCESS_TASK_MESSAGE, taskRequest.getMessage());
            if (null != completeVariables.get(FlowableConstant.NO_LISTENER)) {
                if (Convert.toBool(completeVariables.get(FlowableConstant.NO_LISTENER))) {
                    completeVariables.put(FlowableConstant.NO_LISTENER, hasListener(task.getProcessDefinitionId(), task.getTaskDefinitionKey(), taskRequest.getProcessDefKey()));
                }
            }
        } else {
            actionName = FlowableConstant.INITIATOR.equals(task.getTaskDefinitionKey()) ? CommentTypeEnum.CXTJ.getName() : CommentTypeEnum.WC.getName();
        }
        String lock = buildLock(task.getProcessInstanceId());
        Map<String, Object> processInsVariables = new HashMap<>();
/*        try {
            if (redissonLock.tryLock(lock, 5000, 5000)) {*/
        //如果存在处理时间
        Comment repcom ;
        if (null != taskRequest.getMessageTime()) {
            repcom = addComment(taskId, task.getProcessInstanceId(), currUserId, actionName,
                    taskRequest.getMessage(), taskRequest.getMessageTime());
        } else {
            repcom = addComment(taskId, task.getProcessInstanceId(), currUserId, actionName,
                    taskRequest.getMessage());
        }

        if(multiFlag){
            //向工作流标记拓展表中插入数据--------202203190--strat--
            //QueryWrapper<ActHiCommentCodeDO> queryWrapper = new QueryWrapper<>();
            ActHiCommentCodeDO codeDo = new ActHiCommentCodeDO();
            codeDo.setId(IdWorker.getIdStr());//id
            codeDo.setCommentId(repcom.getId());//com表id
            codeDo.setProcInstId(task.getProcessInstanceId());//流程ID
            codeDo.setButtonCode(taskRequest.getCode());//工作流按钮编码
            codeDo.setButtonName(actionName);
            codeDo.setProcessDefKey(taskRequest.getProcessDefKey());
            actHiCommentCodeMapper.insert(codeDo);
            //向工作流标记拓展表中插入数据--------202203190--strat--
        }


        if (null != taskRequest.getAttchemnt()) {
            try {
                for (int k = 0; k < taskRequest.getAttchemnt().length; k++) {
                    MultipartFile file = taskRequest.getAttchemnt()[k];
                    taskService.createAttachment(file.getContentType(), taskId, task.getProcessInstanceId(), file.getOriginalFilename(),
                            file.getOriginalFilename(), file.getInputStream());
                }
            } catch (IOException e) {
                log.info("---error--"+"执行任务：", e);
            }
        }
        //执行任务前，判断任务是否涉及会签，根据流程定义key和任务定义key即可以确定
        String processDefKey = taskRequest.getProcessDefKey();
        if (ObjectUtils.isEmpty(processDefKey)) {

            ProcessDefinition processDefinition = processDefinitionService.getProcessDefinitionById(task.getProcessDefinitionId());
            processDefKey = processDefinition.getKey();
        }
        ProcessCounterSignDTO processCounterSignDTO = new ProcessCounterSignDTO();
        processCounterSignDTO.setProcessDefKey(processDefKey);
        processCounterSignDTO.setTaskKey(task.getTaskDefinitionKey());
        List<ProcessCounterSignDO> pCounterSigns = processCounterSignService.getCountersignInfo(processCounterSignDTO);
        if (!pCounterSigns.isEmpty()) {
            ProcessCounterSignDO counterSign = pCounterSigns.get(0);
            String csignGrpName = counterSign.getGrpName();
            processCounterSignDTO.setProcessDefKey(processDefKey);
            processCounterSignDTO.setTaskKey(null);
            processCounterSignDTO.setGrpName(csignGrpName);
            List<ProcessCounterSignDO> counterSigns = processCounterSignService.getCountersignInfo(processCounterSignDTO);

            String nrOfCompletedInstances = "_nrOfCompletedInstances";

            if (null != completeVariables && !ObjectUtils.isEmpty(actionGrp)) {
                Map<String, Object> processVariables = runtimeService.getVariables(task.getProcessInstanceId());
                Integer oldActionCount = processVariables.get(csignGrpName + "_" + actionGrp) == null ? 0 : (int) processVariables.get(csignGrpName + "_" + actionGrp);
                Integer completedInstance = processVariables.get(csignGrpName + nrOfCompletedInstances) == null ? 0 : (int) processVariables.get(csignGrpName + nrOfCompletedInstances);
                completeVariables.put(csignGrpName + "_" + actionGrp, ++oldActionCount);
                completeVariables.put(csignGrpName + nrOfCompletedInstances, ++completedInstance);
                //总分支数，从会签策略表中查询即可
                completeVariables.put(csignGrpName + "_nrOfInstances", counterSigns.size());
            }
        }
        //这里需要注意
        synchronized (lock) {
            if (task.getAssignee() == null || !task.getAssignee().equals(currUserId)) {
                taskService.setAssignee(taskId, currUserId);
            }
            // 判断是否是协办完成还是正常流转
            if (permissionService.isTaskPending(task)) {
                taskService.resolveTask(taskId, completeVariables);
                // 如果当前执行人是任务所有人，直接完成任务
                if (currUserId.equals(task.getOwner())) {
                    taskService.complete(taskId, completeVariables);
                }
            } else {
                taskService.complete(taskId, completeVariables);
            }
        }

        //获得流程变量
        try {
            ProcessInstance rpi = runtimeService    //与正在的任务相关的Service
                    .createProcessInstanceQuery()    //创建流程实例查询对象
                    .processInstanceId(task.getProcessInstanceId())     //查询条件 -- 流程的实例id(流程的实例id在流程启动后的整个流程中是不改变的)
                    .singleResult();     //返回唯一结果集
            if (rpi == null) {
                List<HistoricVariableInstance> hisVariablelist = historyService.createHistoricVariableInstanceQuery().processInstanceId(task.getProcessInstanceId()).list();
                for (HistoricVariableInstance hisVariable : hisVariablelist) {
                    processInsVariables.put(hisVariable.getVariableName(), hisVariable.getValue());
                }
            } else {
                processInsVariables = runtimeService.getVariables(task.getProcessInstanceId());
            }
        } catch (Exception exception) {
            log.info("获取流程变量", exception);
        }
        //这里根据流程实例ID，查询下一个待办任务，如果待办任务的skipExpression为true，则自动执行该待办任务：查询待办节点
        List<Task> tasks = taskService.createTaskQuery().processInstanceId(task.getProcessInstanceId()).list();
        if (null != tasks) {
            for (Task skiptask : tasks) {
                if (processInsVariables.get("autoExcuteNext") != null && (boolean) processInsVariables.get("autoExcuteNext")) {
                    completeVariables.put(FlowableConstant.NO_LISTENER, true);
                    completeVariables.put(FlowableConstant.PROCESS_INSTANCE_VCONDITION, "examine");
                    taskService.complete(skiptask.getId(), completeVariables);
                }
                String skipExpression = getTaskDef(skiptask.getTaskDefinitionKey(), skiptask.getProcessDefinitionId());
                String skipBusi = "";
                if (null != taskRequest.getValues() && null != taskRequest.getValues().get(FlowableConstant.SKIP_BUSI)) {
                    skipBusi = taskRequest.getValues().get(FlowableConstant.SKIP_BUSI).toString();
                }
                if (null != skipExpression && skipExpression.contains(taskRequest.getValues().get(FlowableConstant.PROCESS_INSTANCE_VCONDITION).toString())
                        && (!skipExpression.contains(FlowableConstant.SKIP_BUSI) || skipExpression.contains(FlowableConstant.SKIP_BUSI) && skipBusi.equals("1"))) {  // 约定：发起者节点为 __initiator__ ,则自动完成任务
                    //如果存在处理时间
                    String autoReview = i18nUtil.getMessage("MSG.wf.FlowableConstant.autoReview");
                    if (null != taskRequest.getMessageTime()) {
                        addComment(skiptask.getId(), task.getProcessInstanceId(), autoReview, skiptask.getName(),
                                null, taskRequest.getMessageTime());
                    } else {
                        addComment(skiptask.getId(), task.getProcessInstanceId(), autoReview, skiptask.getName(),
                                null);
                    }
                    if (ObjectUtils.isEmpty(skiptask.getAssignee())) {
                        taskService.setAssignee(skiptask.getId(), currUserId);
                    }
                    //自动执行，如果该任务存在按钮，添加按钮信息
                    Map<String, Object> completeSkipVariables = new HashMap<>();
                    List taskButtons = getTaskButtons(skiptask.getId(), processDefKey);
                    if (!taskButtons.isEmpty()) {
                        HashMap buttonInfo = (HashMap) taskButtons.get(0);
                        completeSkipVariables.put(FlowableConstant.PROCESS_INSTANCE_ACTION_INFO, buttonInfo);
                        completeSkipVariables.put(FlowableConstant.PROCESS_INSTANCE_VCONDITION, buttonInfo.get("code"));
                    }
                    completeSkipVariables.put(FlowableConstant.NO_LISTENER, true);
                    taskService.complete(skiptask.getId(), completeSkipVariables);
                }
            }
        }
/*            } else {
                log.info("completeTask获取锁失败ProcessInstanceId：" + lock);
                throw new FlowableTaskException("请勿重复操作，请稍后再试！");
            }
        } catch (Exception ex) {
            redissonLock.unlock(lock);
            log.info("completeTask执行异常，释放锁ProcessInstanceId：" + lock);
            throw ex;
        }*/
        return processInsVariables;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTask(String taskId) {
        HistoricTaskInstance task = getHistoricTaskInstanceNotNull(taskId);
        if (task.getEndTime() == null) {
            throw new FlowableException("Cannot delete task that is running");
        }
        historyService.deleteHistoricTaskInstance(task.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void stopProcessInstance(TaskRequest taskRequest) {
        String taskId = taskRequest.getTaskId();
        String userId = LoginUtil.getOperatorId();
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
        // 如果存在groupId则说明是定时任务过来的执行，需要进行候选判断
        if (!BootAppUtil.isNullOrEmpty(taskRequest.getGroupId())) {
            judgeQuartzexecute(task, taskRequest.getGroupId());
            // 当前用户ID采用候选用户组ID
            userId = taskRequest.getGroupId();
        }

        ProcessInstance processInstance = permissionService.validateStopProcessInstancePermissionOnTask(taskId, userId);
        BpmnModel bpmnModel = repositoryService.getBpmnModel(processInstance.getProcessDefinitionId());
        if (null != bpmnModel) {
            Process process = bpmnModel.getMainProcess();
            List<EndEvent> endNodes = process.findFlowElementsOfType(EndEvent.class, false);
            if (null != endNodes && !endNodes.isEmpty()) {
                String oprType = "";
                if (ObjectUtils.isEmpty(taskRequest.getOprType()))
                    oprType = CommentTypeEnum.TH.getName();
                else
                    oprType = taskRequest.getOprType();
                addComment(taskId, processInstance.getProcessInstanceId(), userId, oprType,
                        taskRequest.getMessage());
                String endId = endNodes.get(0).getId();
                List<Execution> executions = runtimeService.createExecutionQuery()
                        .parentId(processInstance.getProcessInstanceId()).list();
                runtimeService.setVariable(processInstance.getProcessInstanceId(), "StopTaskID", task.getTaskDefinitionKey());
                List<String> executionIds = new ArrayList<>();
                executions.forEach(execution -> executionIds.add(execution.getId()));
                runtimeService.createChangeActivityStateBuilder().moveExecutionsToSingleActivityId(executionIds, endId)
                        .changeState();
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void stopProcessInstance(String processDefId, String processInsId) {
        BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefId);
        if (null != bpmnModel) {
            Process process = bpmnModel.getMainProcess();
            List<EndEvent> endNodes = process.findFlowElementsOfType(EndEvent.class, false);
            if (null != endNodes && !endNodes.isEmpty()) {
                String endId = endNodes.get(0).getId();
                List<Execution> executions = runtimeService.createExecutionQuery()
                        .parentId(processInsId).list();
                List<String> executionIds = new ArrayList<>();
                executions.forEach(execution -> executionIds.add(execution.getId()));
                runtimeService.createChangeActivityStateBuilder().moveExecutionsToSingleActivityId(executionIds, endId)
                        .changeState();
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void stopProcessInstanceByExecID(String processDefId, String executionId) {
        BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefId);
        if (null != bpmnModel) {
            Process process = bpmnModel.getMainProcess();
            List<EndEvent> endNodes = process.findFlowElementsOfType(EndEvent.class, false);
            if (null != endNodes && !endNodes.isEmpty()) {
                String endId = endNodes.get(0).getId();
                List<String> executionIds = new ArrayList<>();
                executionIds.add(executionId);
                runtimeService.createChangeActivityStateBuilder().moveExecutionsToSingleActivityId(executionIds, endId)
                        .changeState();
            }
        }
    }

    @Override
    public List<FlowNodeResponse> getBackNodes(String taskId) {
        TaskEntity taskEntity = (TaskEntity) permissionService.validateExcutePermissionOnTask(taskId,
                LoginUtil.getOperatorId());
        String processInstanceId = taskEntity.getProcessInstanceId();
        String currActId = taskEntity.getTaskDefinitionKey();
        String processDefinitionId = taskEntity.getProcessDefinitionId();
        Process process = repositoryService.getBpmnModel(processDefinitionId).getMainProcess();
        FlowNode currentFlowElement = (FlowNode) process.getFlowElement(currActId, true);
        List<ActivityInstance> activitys = runtimeService.createActivityInstanceQuery()
                .processInstanceId(processInstanceId).finished().orderByActivityInstanceStartTime().asc().list();
        List<String> activityIds = activitys.stream()
                .filter(activity -> activity.getActivityType().equals(BpmnXMLConstants.ELEMENT_TASK_USER))
                .filter(activity -> !activity.getActivityId().equals(currActId)).map(ActivityInstance::getActivityId)
                .distinct().collect(Collectors.toList());
        List<FlowNodeResponse> result = new ArrayList<>();
        for (String activityId : activityIds) {
            FlowNode toBackFlowElement = (FlowNode) process.getFlowElement(activityId, true);
            if (FlowableUtils.isReachable(process, toBackFlowElement, currentFlowElement)) {
                FlowNodeResponse vo = new FlowNodeResponse();
                vo.setNodeId(activityId);
                vo.setNodeName(toBackFlowElement.getName());
                result.add(vo);
            }
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void backTask(TaskRequest taskRequest) {
        String taskId = taskRequest.getTaskId();
        String userId = LoginUtil.getOperatorId();
        // 这个现在其实没有判断，所以用户ID就让他传好了
        Task task = permissionService.validateExcutePermissionOnTask(taskId, userId);
        // 如果存在groupId则说明是定时任务过来的执行，需要进行候选判断
        boolean quartzFlag = true;
        if (!BootAppUtil.isNullOrEmpty(taskRequest.getGroupId())) {
            quartzFlag = judgeQuartzexecute(task, taskRequest.getGroupId());
            // 当前用户ID采用候选用户组ID
            userId = taskRequest.getGroupId();
        }
        if (!quartzFlag) {
            return;
        }
        String backSysMessage = "";
        if (!taskRequest.isNoMessage())
            backSysMessage = i18nUtil.getMessage("MSG.wf.flowableTaskService.rollback", taskRequest.getActivityName());
        String oprType = "";
        if (ObjectUtils.isEmpty(taskRequest.getOprType()))
            oprType = CommentTypeEnum.TH.getName();
        else
            oprType = taskRequest.getOprType();
        if (taskRequest.isHasComment()) {
            addComment(taskId, task.getProcessInstanceId(), userId, oprType,
                    backSysMessage + (taskRequest.getMessage() == null ? "" : taskRequest.getMessage()));
        }
        //根据流程实例查询审核意见，删除最后一条意见
        if (taskRequest.isDeleteLastComment()) {
            List<Comment> datas = taskService.getProcessInstanceComments(task.getProcessInstanceId());
            if (!datas.isEmpty()) {
                List ids = new ArrayList();
                ids.add(datas.get(0).getId());
                hisFlowableActinstMapper.deleteHiCommentByIds(ids);
            }
        }

        String targetRealActivityId = managementService.executeCommand(
                new BackUserTaskCmd(processCounterSignService, runtimeService, taskRequest.getTaskId(), taskRequest.getActivityId()));


        // 退回发起者处理,退回到发起者,默认设置任务执行人为发起者
        if (FlowableConstant.INITIATOR.equals(targetRealActivityId)) {
            String initiator = runtimeService.createProcessInstanceQuery()
                    .processInstanceId(task.getProcessInstanceId()).singleResult().getStartUserId();
            List<Task> newTasks = taskService.createTaskQuery().processInstanceId(task.getProcessInstanceId()).list();
            for (Task newTask : newTasks) {
                // 约定：发起者节点为 __initiator__
                if (FlowableConstant.INITIATOR.equals(newTask.getTaskDefinitionKey()) && ObjectUtils.isEmpty(newTask.getAssignee())) {
                    taskService.setAssignee(newTask.getId(), initiator);
                }
            }
        }
    }

    private void fillPermissionInformation(TaskResponse taskResponse, TaskInfo task, String userId) {
        verifyProcessInstanceStartUser(taskResponse, task);
        List<HistoricIdentityLink> taskIdentityLinks = historyService.getHistoricIdentityLinksForTask(task.getId());
        verifyCandidateGroups(taskResponse, userId, taskIdentityLinks);
        verifyCandidateUsers(taskResponse, userId, taskIdentityLinks);
    }

    private void verifyProcessInstanceStartUser(TaskResponse taskResponse, TaskInfo task) {
        if (null != task.getProcessInstanceId()) {
            HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                    .processInstanceId(task.getProcessInstanceId()).singleResult();
            if (null != historicProcessInstance && StringUtils.isNotEmpty(historicProcessInstance.getStartUserId())) {
                taskResponse.setProcessInstanceStartUserId(historicProcessInstance.getStartUserId());
                BpmnModel bpmnModel = repositoryService.getBpmnModel(task.getProcessDefinitionId());
                FlowElement flowElement = bpmnModel.getFlowElement(task.getTaskDefinitionKey());
                if (flowElement instanceof UserTask) {
                    UserTask userTask = (UserTask) flowElement;
                    List<ExtensionElement> extensionElements = userTask.getExtensionElements()
                            .get("initiator-can-complete");
                    if (CollectionUtils.isNotEmpty(extensionElements)) {
                        String value = extensionElements.get(0).getElementText();
                        if (StringUtils.isNotEmpty(value)) {
                            taskResponse.setInitiatorCanCompleteTask(value);
                        }
                    }
                }
            }
        }
    }

    private void verifyCandidateGroups(TaskResponse taskResponse, String userId,
                                       List<HistoricIdentityLink> taskIdentityLinks) {
        List<Group> userGroups = identityService.createGroupQuery().groupMember(userId).list();
        taskResponse.setMemberOfCandidateGroup(
                String.valueOf(userGroupsMatchTaskCandidateGroups(userGroups, taskIdentityLinks)));
    }

    private boolean userGroupsMatchTaskCandidateGroups(List<Group> userGroups,
                                                       List<HistoricIdentityLink> taskIdentityLinks) {
        for (Group group : userGroups) {
            for (HistoricIdentityLink identityLink : taskIdentityLinks) {
                if (null != identityLink.getGroupId() && identityLink.getType().equals(IdentityLinkType.CANDIDATE)
                        && group.getId().equals(identityLink.getGroupId())) {
                    return true;
                }
            }
        }
        return false;
    }

    private void verifyCandidateUsers(TaskResponse taskResponse, String userId,
                                      List<HistoricIdentityLink> taskIdentityLinks) {
        taskResponse.setMemberOfCandidateUsers(
                String.valueOf(currentUserMatchesTaskCandidateUsers(userId, taskIdentityLinks)));
    }

    private boolean currentUserMatchesTaskCandidateUsers(String userId, List<HistoricIdentityLink> taskIdentityLinks) {
        for (HistoricIdentityLink identityLink : taskIdentityLinks) {
            if (identityLink.getUserId() != null && identityLink.getType().equals(IdentityLinkType.CANDIDATE)
                    && identityLink.getUserId().equals(userId)) {
                return true;
            }
        }
        return false;
    }

    private String getUserName(String userId) {
        if (CommonUtil.isEmptyStr(userId)) {
            return null;
        }
        if ((identityService.createUserQuery().userId(userId)).list() != null) {
            User user = identityService.createUserQuery().userId(userId).singleResult();
            if (user != null) {
                return user.getFirstName();
            }
        } else {
            return userId;
        }
        return null;
    }

    private List<String> getInvolvedUsers(String taskId) {
        List<HistoricIdentityLink> idLinks = historyService.getHistoricIdentityLinksForTask(taskId);
        List<String> result = new ArrayList<>(idLinks.size());

        for (HistoricIdentityLink link : idLinks) {
            // Only include users and non-assignee links
            if (link.getUserId() != null && !IdentityLinkType.ASSIGNEE.equals(link.getType())) {
                result.add(link.getUserId());
            }
        }
        return result;
    }

    private void populateAssignee(TaskInfo task, TaskResponse rep) {
        if (task.getAssignee() != null) {
            rep.setAssignee(task.getAssignee());
        }
    }

    @SuppressWarnings({"squid:S1192"})
    @Override
    public Task getTaskNotNull(String taskId) {
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
        if (task == null) {
            throw new FlowableObjectNotFoundException("Task with id: " + taskId + " does not exist");
        }
        return task;
    }

    @SuppressWarnings({"squid:S1192"})
    @Override
    public HistoricTaskInstance getHistoricTaskInstanceNotNull(String taskId) {
        HistoricTaskInstance task = historyService.createHistoricTaskInstanceQuery().taskId(taskId).singleResult();
        if (task == null) {
            throw new FlowableObjectNotFoundException("Task with id: " + taskId + " does not exist");
        }
        return task;
    }

    @Override
    public Comment addComment(String taskId, String processInstanceId, String userId, CommentTypeEnum type,
                           String message) {
        Authentication.setAuthenticatedUserId(userId);
        type = type == null ? CommentTypeEnum.SP : type;
        message = (message == null || message.length() == 0) ? "" : message;
        Comment repCom = taskService.addComment(taskId, processInstanceId, type.toString(), message);
        return repCom;
    }

    @Override
    public Comment addComment(String taskId, String processInstanceId, String userId, String type,
                           String message) {
        Authentication.setAuthenticatedUserId(userId);
        type = type == null ? CommentTypeEnum.SP.getName() : type;
        message = (message == null || message.length() == 0) ? "" : message;
        Comment repCom = taskService.addComment(taskId, processInstanceId, type, message);
        return repCom;
    }

    public Comment addComment(String taskId, String processInstanceId, String userId, String type,
                           String message, Date messageTime) {
        Authentication.setAuthenticatedUserId(userId);
        type = type == null ? CommentTypeEnum.SP.getName() : type;
        message = (message == null || message.length() == 0) ? "" : message;
        Comment repCom = managementService.executeCommand(new AddCommentCmd(taskId, processInstanceId, type, message, userId, messageTime));
        return repCom;
    }

    @Override
    public List<Comment> getComments(String taskId, String processInstanceId, String type, String userId) {
        List<Comment> comments = null;
        if (type == null || type.length() == 0) {
            // 以taskId为优先
            if (taskId != null && taskId.length() > 0) {
                comments = taskService.getTaskComments(taskId);
            } else if (processInstanceId != null && processInstanceId.length() > 0) {
                comments = taskService.getProcessInstanceComments(processInstanceId);
            } else {
                throw new FlowableIllegalArgumentException("taskId processInstanceId type are all empty");
            }
        } else {
            // 以taskId为优先
            if (taskId != null && taskId.length() > 0) {
                comments = taskService.getTaskComments(taskId, type);
            } else if (processInstanceId != null && processInstanceId.length() > 0) {
                comments = taskService.getProcessInstanceComments(processInstanceId, type);
            } else {
                comments = taskService.getCommentsByType(type);
            }
        }
        if (userId != null && userId.length() > 0 && comments != null && !comments.isEmpty()) {
            comments = comments.stream().filter(comment -> userId.equals(comment.getUserId()))
                    .collect(Collectors.toList());
        }
        return comments;
    }

    private void validateIdentityLinkArguments(String identityId, String identityType) {
        if (identityId == null || identityId.length() == 0) {
            throw new FlowableIllegalArgumentException("identityId is null");
        }
        if (!FlowableConstant.IDENTITY_GROUP.equals(identityType)
                && !FlowableConstant.IDENTITY_USER.equals(identityType)) {
            throw new FlowableIllegalArgumentException("type must be group or user");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveTaskIdentityLink(IdentityRequest taskIdentityRequest) {
        Task task = getTaskNotNull(taskIdentityRequest.getTaskId());
        validateIdentityLinkArguments(taskIdentityRequest.getIdentityId(), taskIdentityRequest.getIdentityType());
        if (FlowableConstant.IDENTITY_GROUP.equals(taskIdentityRequest.getIdentityType())) {
            taskService.addGroupIdentityLink(task.getId(), taskIdentityRequest.getIdentityId(),
                    IdentityLinkType.CANDIDATE);
        } else if (FlowableConstant.IDENTITY_USER.equals(taskIdentityRequest.getIdentityType())) {
            taskService.addUserIdentityLink(task.getId(), taskIdentityRequest.getIdentityId(),
                    IdentityLinkType.CANDIDATE);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTaskIdentityLink(String taskId, String identityId, String identityType) {
        Task task = getTaskNotNull(taskId);
        validateIdentityLinkArguments(identityId, identityType);
        if (FlowableConstant.IDENTITY_GROUP.equals(identityType)) {
            taskService.deleteGroupIdentityLink(task.getId(), identityId, IdentityLinkType.CANDIDATE);
        } else if (FlowableConstant.IDENTITY_USER.equals(identityType)) {
            taskService.deleteUserIdentityLink(task.getId(), identityId, IdentityLinkType.CANDIDATE);
        }
    }

    @Override
    public void completeTaskByQuartz(Map<String, String> map) {
        // 查询当前待办任务节点
        String taskId = getTobeUserTask(map);
        TaskRequest taskRequest = new TaskRequest();
        taskRequest.setTaskId(taskId);
        String message = i18nUtil.getMessage("MSG.wf.flowableTaskService.jobFinish");
        taskRequest.setMessage(message);
        taskRequest.setGroupId("All");
        Map<String, Object> values = new HashMap<>();
        values.put(FlowableConstant.PROCESS_INSTANCE_ACTION_INFO, null);
        values.put(FlowableConstant.PROCESS_INSTANCE_VCONDITION, map.get("code"));
        taskRequest.setValues(values);
        completeTask(taskRequest);
    }

    @Override
    public void stopProcessInstanceByQuartz(Map<String, String> map) {
        // 查询当前待办任务节点
        String taskId = getTobeUserTask(map);
        TaskRequest taskRequest = new TaskRequest();
        taskRequest.setTaskId(taskId);
        String message = i18nUtil.getMessage("MSG.wf.flowableTaskService.jobEndFlow");
        taskRequest.setMessage(message);
        taskRequest.setGroupId("All");
        stopProcessInstance(taskRequest);
    }

    @Override
    public void backTaskByQuartz(Map<String, String> map) {
        // 查询当前待办任务节点
        String taskId = getTobeUserTask(map);
        TaskRequest taskRequest = new TaskRequest();
        taskRequest.setTaskId(taskId);
        String message = i18nUtil.getMessage("MSG.wf.flowableTaskService.jobRollback");
        taskRequest.setMessage(message);
        taskRequest.setGroupId("All");
        taskRequest.setActivityId(map.get("activityId"));
        backTask(taskRequest);
    }

    /**
     * @description 判断任务节点是否存在候选人
     * <AUTHOR>
     * @date 2020/8/29 12:54
     */
    private boolean judgeQuartzexecute(Task task, String groupId) {
        boolean flag = false;
        List<IdentityLink> identityLinks = taskService.getIdentityLinksForTask(task.getId());
        for (IdentityLinkInfo identityLink : identityLinks) {
            if ("candidate".equals(identityLink.getType()) && groupId.equals(identityLink.getGroupId())) {
                flag = true;
                return flag;
            }
        }
        return flag;
    }

    /**
     * @description 根据流程定义KEY和businessKey获取待办任务节点
     * <AUTHOR>
     * @date 2020/8/29 11:09
     */
    private String getTobeUserTask(Map<String, String> map) {
        String taskId = null;
        String businessKey = map.get("businessKey");
        String processDefKey = map.get("processDefinitionKey");
        // 查询任务节点
        TaskQuery taskQuery = taskService.createTaskQuery();
        taskQuery.processDefinitionKey(processDefKey);
        taskQuery.processInstanceBusinessKey(businessKey);
        String userId = LoginUtil.getOperatorId();
        if (!BootAppUtil.isNullOrEmpty(processDefKey)) {
            boolean flag = wfConfService.getProcessAuditAuthCheck(processDefKey);
            if (flag) {
                taskQuery.or().taskCandidateOrAssigned(userId).taskOwner(userId).endOr();
            }
        }
        Class<? extends IListWrapper> taskTodoListWrapper = TaskTodoListWrapper.class;
        List<Task> taskListSearch = taskQuery.list();
        List<TaskResponse> taskList = new ArrayList<>();
        if (taskTodoListWrapper != null) {
            IListWrapper listWrapper = SpringContextUtils.getBean(taskTodoListWrapper);
            taskList = listWrapper.execute(taskListSearch);
        }
        if (!taskList.isEmpty()) {
            taskId = taskList.get(0).getId();
        }
        return taskId;
    }

    @SuppressWarnings("squid:S1612")
    private String getTaskDef(String taskDefKey, String processDefinitionID) {
        //这里可以考虑性能优化：通过流程定义ID获取流程模板
        String skipExpression = "";
        BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinitionID);
        Process process = bpmnModel.getProcesses().get(0);
        Collection<UserTask> flowElements = process.findFlowElementsOfType(UserTask.class);
        List<UserTask> tasks = new ArrayList<>();
        flowElements.stream().filter(p -> p.getId().equals(taskDefKey)).forEach(element -> tasks.add(element));
        if (!tasks.isEmpty()) {
            return tasks.get(0).getSkipExpression();
        }
        return skipExpression;
    }

    @SuppressWarnings({"squid:S1192"})
    @Override
    public List getTaskButtons(String taskId, String processDefinitionKey) {
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
        if (task == null) {
            throw new FlowableObjectNotFoundException("Task with id: " + taskId + " does not exist");
        }
        List buttonDefs = new ArrayList();
        String processDefID = task.getProcessDefinitionId();
        String taskDefKey = task.getTaskDefinitionKey();
        //通过流程定义ID获取流程模板
        BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefID);
        if (bpmnModel != null) {
            List<Process> processList = bpmnModel.getProcesses();
            for (Process process : processList) {
                Map<String, FlowElement> flowElementMap = process.getFlowElementMap();
                Object taskDef = flowElementMap.get(taskDefKey);
                if (taskDef instanceof UserTask && ((UserTask) taskDef).getId().equals(task.getTaskDefinitionKey())) {
                    Map<String, List<ExtensionElement>> extensionElement = ((UserTask) taskDef).getExtensionElements();
                    if (null != extensionElement.get("button")) {
                        for (ExtensionElement button : extensionElement.get("button")) {
                            Map<String, List<ExtensionAttribute>> exAttributes = button.getAttributes();
                            Map buttonInfo = new HashMap();
                            //根据流程定义Key和taskDefKey查询按钮其他信息
                            List<ProcessCommonButtonsDO> buttons = processCommonButtonsService.getButtons(processDefinitionKey, exAttributes.get("code").get(0).getValue());
                            buttonInfo.put("code", exAttributes.get("code").get(0).getValue());
                            buttonInfo.put("name", exAttributes.get("name").get(0).getValue());
                            if (!buttons.isEmpty()) {
                                buttonInfo.put("code", buttons.get(0).getCode());
                                buttonInfo.put("stateCode", buttons.get(0).getStateCode());
                                buttonInfo.put("name", buttons.get(0).getName());
                            }
                            buttonDefs.add(buttonInfo);
                        }
                    }
                }
            }
        }
        return buttonDefs;
    }

    @Override
    public boolean hasListener(String processDefinitionId, String taskDefKey, String processDefKey) {
        boolean hasListener = true;
        //数据存储key
        String wfListenerKey = redisUtils.keyBuilder("common-wf", "listener", processDefKey + taskDefKey);
        //先读取缓存数据
        //先读取缓存数据
        Object objString = redisUtils.get(wfListenerKey);
        if (objString != null) {
            hasListener = Convert.toBool(objString);
        } else {
            BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinitionId);
            if (bpmnModel != null) {
                Process process = bpmnModel.getMainProcess();
                Map<String, FlowElement> flowElementMap = process.getFlowElementMap();
                Object taskDef = flowElementMap.get(taskDefKey);
                if (taskDef instanceof UserTask && ((UserTask) taskDef).getId().equals(taskDefKey)) {
                    hasListener = ((UserTask) taskDef).getTaskListeners().isEmpty() ? false : true;
                    redisUtils.set(wfListenerKey, hasListener, 600);
                    //存修改标记key
                    redisUtils.lPush("wf:listener", wfListenerKey);
                }
            }
        }
        return hasListener;
    }

    @Override
    public List getTaskButtonsCode(String taskDefKey, String processDefID) {
        //通过流程定义ID获取流程模板
        BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefID);
        List buttonDefs = new ArrayList();
        if (bpmnModel != null) {
            List<Process> processList = bpmnModel.getProcesses();
            for (Process process : processList) {
                List<ProcessCommonButtonsDO> commonButtons = processCommonButtonsService.getButtons(process.getId(), "");
                Map<String, FlowElement> flowElementMap = process.getFlowElementMap();
                Object taskDef = flowElementMap.get(taskDefKey);
                if (taskDef instanceof UserTask && ((UserTask) taskDef).getId().equals(taskDefKey)) {
                    Map<String, List<ExtensionElement>> extensionElement = ((UserTask) taskDef).getExtensionElements();
                    if (null != extensionElement.get("button")) {
                        for (ExtensionElement button : extensionElement.get("button")) {
                            Map<String, List<ExtensionAttribute>> exAttributes = button.getAttributes();
                            Map buttonInfo = new HashMap();
                            String buttonCode = exAttributes.get("code").get(0).getValue();
                            List<ProcessCommonButtonsDO> buttons = commonButtons.stream().filter(s -> s.getCode().equals(buttonCode)).collect(Collectors.toList());
                            if (null != buttons && !buttons.isEmpty()) {
                                buttonInfo.put("code", buttonCode);
                                buttonInfo.put("name", exAttributes.get("name").get(0).getValue());
                                buttonInfo.put("direction", buttons.get(0).getDirection());
                                buttonDefs.add(buttonInfo);
                            }
                        }
                    }
                }
            }
        }
        return buttonDefs;
    }

    @SuppressWarnings({"squid:S1854", "squid:S1481"})
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map getTaskAssignment(String processDefKey, String taskID) {
        Map map = new HashMap();
        BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefKey);
        FlowElement flowElement = bpmnModel.getFlowElement(taskID);
        if (flowElement instanceof UserTask) {
            UserTask userTask = (UserTask) flowElement;

        }
        return map;
    }

    public List<Map> hisProcInsQueryByBusinessKeys(String processDefKey, List<String> businessKeys) {
        return hisFlowableActinstMapper.hisProcInsQueryByBusinessKeys(processDefKey, businessKeys);
    }

    public Integer getTaskTodoCount(String userID, List<String> relateRoles) {
        return hisFlowableActinstMapper.getTaskTodoCount(userID, relateRoles);
    }

    /**
     * 获取自定义属性值
     *
     * @param activityId          节点id
     * @param processDefinitionId 流程定义id
     * @param customPropertyName  属性名
     * @return
     */
    public List<ExtensionElement> getCustomProperty(String activityId, String processDefinitionId, String customPropertyName) {
        FlowElement flowElement = getFlowElementByActivityIdAndProcessDefinitionId(activityId, processDefinitionId);
        if (flowElement != null && flowElement instanceof UserTask) {
            UserTask userTask = (UserTask) flowElement;
            Map<String, List<ExtensionElement>> extensionElements = userTask.getExtensionElements();
            if (MapUtils.isNotEmpty(extensionElements)) {
                List<ExtensionElement> values = extensionElements.get(customPropertyName);
                if (CollectionUtils.isNotEmpty(values)) {
                    return values;
                }
            }
        }
        return Lists.newArrayList();
    }

    public FlowElement getFlowElementByActivityIdAndProcessDefinitionId(String activityId, String processDefinitionId) {
        BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinitionId);
        List<Process> processes = bpmnModel.getProcesses();
        if (CollectionUtils.isNotEmpty(processes)) {
            for (Process process : processes) {
                FlowElement flowElement = process.getFlowElement(activityId);
                if (flowElement != null) {
                    return flowElement;
                }
            }
        }
        return null;
    }


    @Override
    public List getTaskButtonsCodeBy(String taskDefKey, String processDefID) {
        //通过流程定义ID获取流程模板
        BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefID);
        List buttonDefs = new ArrayList();
        if (bpmnModel != null) {
            List<Process> processList = bpmnModel.getProcesses();
            for (Process process : processList) {
                List<ProcessCommonButtonsDO> commonButtons = processCommonButtonsService.getButtonsBy(process.getId(), "",1);
                Map<String, FlowElement> flowElementMap = process.getFlowElementMap();
                Object taskDef = flowElementMap.get(taskDefKey);
                if (taskDef instanceof UserTask && ((UserTask) taskDef).getId().equals(taskDefKey)) {
                    Map<String, List<ExtensionElement>> extensionElement = ((UserTask) taskDef).getExtensionElements();
                    if (null != extensionElement.get("button")) {
                        for (ExtensionElement button : extensionElement.get("button")) {
                            Map<String, List<ExtensionAttribute>> exAttributes = button.getAttributes();
                            Map buttonInfo = new HashMap();
                            String buttonCode = exAttributes.get("code").get(0).getValue();
                            List<ProcessCommonButtonsDO> buttons = commonButtons.stream().filter(s -> s.getCode().equals(buttonCode)).collect(Collectors.toList());
                            if (null != buttons && !buttons.isEmpty()) {
                                buttonInfo.put("code", buttonCode);
                                buttonInfo.put("name", exAttributes.get("name").get(0).getValue());
                                buttonInfo.put("direction", buttons.get(0).getDirection());
                                buttonDefs.add(buttonInfo);
                            }
                        }
                    }
                }
            }
        }
        return buttonDefs;
    }
}
