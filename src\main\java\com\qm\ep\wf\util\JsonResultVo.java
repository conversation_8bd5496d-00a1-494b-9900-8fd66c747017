package com.qm.ep.wf.util;

import com.qm.tds.api.exception.QmException;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@ApiModel(description = "结果")
public class JsonResultVo<T> {
    @ApiModelProperty("日志")
    private static final Logger log = LoggerFactory.getLogger(JsonResultVo.class);
    @ApiModelProperty("状态正常")
    public static final String STATUS_OK = "S";
    @ApiModelProperty("状态错误")
    public static final String STATUS_ERR = "E";
    @ApiModelProperty("代码 OK")
    public static final int CODE_OK = 200;
    @ApiModelProperty("代码错误")
    public static final int CODE_ERR = 500;
    @ApiModelProperty("代码错误无提示")
    public static final int CODE_ERR_NO_PROMPT = 1007;
    @ApiModelProperty("状态")
    private String status = "S";
    @ApiModelProperty("状态码")
    private int code = 200;
    @ApiModelProperty("请求调用ID")
    private String traceId;
    @ApiModelProperty("消息")
    private String msg;
    @ApiModelProperty("数据")
    private T data;
    @ApiModelProperty("数据")
    private List<T> dataList;

    public String getStatus() {
        return 200 == this.code ? "S" : "E";
    }

    @ApiModelProperty("当前结果是否为正常")
    public boolean isOk() {
        return 200 == this.code;
    }

    public void setMsgErr(String msg) {
        this.msg = msg;
        this.code = 500;
    }

    public void setMsgErr(String msg, Throwable ex) {
        if (ex == null) {
            this.setMsgErr(msg);
            log.warn(msg);
        } else if (ex instanceof QmException) {
            this.setMsgErr(msg);
            if (ex.getCause() == null) {
                log.warn(msg, ex);
            } else {
                log.info("---error--" + msg, ex);
            }
        } else {
            this.setMsgErr(msg + "[" + ex.getClass().getName() + "]" + ex.getMessage());
            log.info("---error--" + msg, ex);
        }

    }

    public int getCode() {
        return this.code;
    }

    public void setCode(final int code) {
        this.code = code;
    }

    public String getTraceId() {
        return this.traceId;
    }

    public void setTraceId(final String traceId) {
        this.traceId = traceId;
    }

    public String getMsg() {
        return this.msg;
    }

    public void setMsg(final String msg) {
        this.msg = msg;
    }

    public T getData() {
        return this.data;
    }

    public void setData(final T data) {
        this.data = data;
    }

    public List<T> getDataList() {
        return this.dataList;
    }

    public void setDataList(final List<T> dataList) {
        this.dataList = dataList;
    }
}
