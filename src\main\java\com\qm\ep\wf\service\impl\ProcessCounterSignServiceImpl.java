package com.qm.ep.wf.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.qm.ep.wf.domain.bean.ProcessCounterSignDO;
import com.qm.ep.wf.domain.dto.ProcessCounterSignDTO;
import com.qm.ep.wf.domain.dto.UpdateCounterSignTaskKeyDTO;
import com.qm.ep.wf.mapper.ProcessCounterSignMapper;
import com.qm.ep.wf.service.ProcessCounterSignService;
import com.qm.ep.wf.util.CommonUtil;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import com.qm.tds.api.service.impl.QmBaseServiceImpl;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.I18nUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-18
 */
@Service
public class ProcessCounterSignServiceImpl extends QmBaseServiceImpl<ProcessCounterSignMapper, ProcessCounterSignDO> implements ProcessCounterSignService {
    @Autowired
    private I18nUtil i18nUtil;

    @Override
    public List<ProcessCounterSignDO> selectCountersignGroupName(ProcessCounterSignDTO processCounterSignDTO) {
        return baseMapper.selectCountersignGroupName(processCounterSignDTO);
    }

    @Override
    public List<ProcessCounterSignDO> getCountersignInfo(ProcessCounterSignDTO processCounterSignDTO) {
        QueryWrapper<ProcessCounterSignDO> queryWrapper = new QueryWrapper<>();
        if (CommonUtil.isNotEmptyAfterTrim(processCounterSignDTO.getProcessDefKey())) {
            queryWrapper.eq("processDefKey", processCounterSignDTO.getProcessDefKey());
        }
        if (CommonUtil.isNotEmptyAfterTrim(processCounterSignDTO.getTaskKey())) {
            queryWrapper.eq("taskKey", processCounterSignDTO.getTaskKey());
        }
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public JsonResultVo<ProcessCounterSignDO> customeSaveOrUpdate(ProcessCounterSignDO tempDO) {
        JsonResultVo<ProcessCounterSignDO> resultObj = new JsonResultVo<>();
        // 如果是保存，判断是否重复
        if (BootAppUtil.isNullOrEmpty(tempDO.getId())) {
            //定义查询构造器
            QmQueryWrapper<ProcessCounterSignDO> queryWrapper = new QmQueryWrapper<>();
            //会签组名称
            if (!BootAppUtil.isNullOrEmpty(tempDO.getGrpName())) {
                queryWrapper.eq("grpName", tempDO.getGrpName());
            }

            //人工任务名称
            if (!BootAppUtil.isNullOrEmpty(tempDO.getTaskKey())) {
                queryWrapper.eq("taskKey", tempDO.getTaskKey());
            }

            //流程定义Key 对应bpmn中process的ID
            if (!BootAppUtil.isNullOrEmpty(tempDO.getProcessDefKey())) {
                queryWrapper.eq("processDefKey", tempDO.getProcessDefKey());
            }

            List<ProcessCounterSignDO> list = list(queryWrapper);
            if (CollectionUtils.isEmpty(list)) {
                String message = i18nUtil.getMessage("ERR.wf.processCounterSignService.customeSaveOrUpdate");
                resultObj.setMsgErr(message);
                return resultObj;
            }
        }
        boolean flag = saveOrUpdate(tempDO);
        if (flag) {
            resultObj.setData(tempDO);
        } else {
            String message = i18nUtil.getMessage("ERR.wf.common.saveFail");
            resultObj.setMsgErr(message);
        }
        return resultObj;
    }

    @Override
    public JsonResultVo<ProcessCounterSignDO> updateCountersignByKey(ProcessCounterSignDO tempDO) {
        JsonResultVo<ProcessCounterSignDO> resultObj = new JsonResultVo<>();
        Integer flag = baseMapper.updateCountersignByKey(tempDO);
        if (flag > 0) {
            String message = i18nUtil.getMessage("MSG.wf.common.updateSuccess");
            resultObj.setMsg(message);
            resultObj.setData(tempDO);
        } else {
            String message = i18nUtil.getMessage("ERR.wf.common.updateFail");
            resultObj.setMsgErr(message);
        }
        return resultObj;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public JsonResultVo<String> updateTaskKeyById(List<UpdateCounterSignTaskKeyDTO> dtoList) {
        JsonResultVo vo = new JsonResultVo();
        String message = i18nUtil.getMessage("MSG.wf.common.operateSuccess");
        if (CollectionUtils.isEmpty(dtoList)) {
            vo.setMsg(message);
            return vo;
        }
        for (UpdateCounterSignTaskKeyDTO taskKeyDTO : dtoList) {
            ProcessCounterSignDO signDO = baseMapper.selectById(taskKeyDTO.getId());
            signDO.setTaskKey(taskKeyDTO.getTaskKey());
            baseMapper.updateById(signDO);
        }
        vo.setMsg(message);
        return vo;
    }
}
