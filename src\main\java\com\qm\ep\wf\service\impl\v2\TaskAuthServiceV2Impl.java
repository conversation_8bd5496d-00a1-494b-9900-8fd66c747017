package com.qm.ep.wf.service.impl.v2;

import com.qm.ep.wf.domain.bean.TaskAuthDO;
import com.qm.ep.wf.domain.dto.TaskAuthDTO;
import com.qm.ep.wf.mapper.TaskAuthMapper;
import com.qm.ep.wf.service.impl.TaskAuthServiceImpl;
import com.qm.tds.util.BootAppUtil;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/4/7
 */
@Service
public class TaskAuthServiceV2Impl extends TaskAuthServiceImpl<TaskAuthMapper, TaskAuthDO> {


    @Override
    public boolean judgeTaskAuthForUser(String taskName, String actionCode, String processDefKey, String buttonCode, String transCode) {
        // 授权用户组
        List<TaskAuthDTO> userTaskAuth = findTaskAuthDTO(processDefKey, taskName, buttonCode, transCode);

        // 当前按钮角色授权组
        // 
        List<TaskAuthDO> rolesTaskAuth = selectRoleAuth(taskName, processDefKey, buttonCode, transCode);
        if (defaultValidate(userTaskAuth, rolesTaskAuth)) {
            return true;
        }
        if (validateTaskAuthForUser(userTaskAuth)) {
            return true;
        }

        return validateRoleAuth(rolesTaskAuth);
    }

    /**
     * 默认的校验规则，用户授权组和角色授权组都为空的时候默认返回 true
     *
     * @param userTasks
     * @param roleTasks
     * @return
     */
    private boolean defaultValidate(List<TaskAuthDTO> userTasks, List<TaskAuthDO> roleTasks) {
        return CollectionUtils.isEmpty(userTasks) && CollectionUtils.isEmpty(roleTasks);
    }

    /**
     * 校验用户组身份合法性
     *
     * @return
     */
    private boolean validateTaskAuthForUser(List<TaskAuthDTO> list) {

        String userId = BootAppUtil.getLoginKey().getOperatorId();
        for (TaskAuthDTO dto : list) {
            if (dto.getActionCode().equals(userId)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 校验当前用户角色身份合法性
     *
     * @param rolesTaskAuth
     * @return
     */
    private boolean validateRoleAuth(List<TaskAuthDO> rolesTaskAuth) {
        // 查询当前登陆用户的角色
        List<String> roleCodes = selectNowUserRole();
        for (TaskAuthDO item : rolesTaskAuth) {
            if (roleCodes.contains(item.getActionCode())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 查询当前任务按钮的用户授权
     *
     * @param processDefKey
     * @param taskName
     * @param buttonCode
     * @return
     */
    private List<TaskAuthDTO> findTaskAuthDTO(String processDefKey, String taskName, String buttonCode, String transCode) {
        TaskAuthDTO taskAuthDTO = new TaskAuthDTO();
        taskAuthDTO.setProcessDefKey(processDefKey);
        taskAuthDTO.setTaskName(taskName);
        taskAuthDTO.setTransCode(transCode);
        if (!BootAppUtil.isNullOrEmpty(buttonCode)) {
            taskAuthDTO.setButtonCode(buttonCode);
        }
        return baseMapper.judgeTaskAuthForUser(taskAuthDTO);
    }
}
