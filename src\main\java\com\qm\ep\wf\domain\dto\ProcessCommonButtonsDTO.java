package com.qm.ep.wf.domain.dto;

import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-15
 */
@ApiModel(value = "对象ProcessCommonButtonsDTO对象", description = "对象ProcessCommonButtonsDTO对象")
@Data
public class ProcessCommonButtonsDTO extends JsonParamDto {

    @ApiModelProperty("串行版 uid")
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "主键ID")
    private String id;
    @ApiModelProperty(value = "名称")
    private String name;
    @ApiModelProperty(value = "代码")
    private String code;
    @ApiModelProperty(value = "状态码")
    private String stateCode;
    @ApiModelProperty(value = "正向/方向，字典项")
    private String direction;
    @ApiModelProperty(value = "排序")
    private Integer orderNo;
    @ApiModelProperty(value = "动作Url")
    private String actionUrl;
    @ApiModelProperty(value = "流程定义Key")
    private String processDefKey;
    @ApiModelProperty(value = "核心标识")
    private Integer coreFlag;
    @ApiModelProperty(value = "是否隐藏")
    private Integer visable;
    @ApiModelProperty(value = "任务名称")
    private String taskName;
    @ApiModelProperty(value = "备注")
    private String remark;
    @ApiModelProperty(value = "是否显示状态0否1是")
    private Integer isState;
    @ApiModelProperty(value = "用例代码")
    private String transCode;
    @ApiModelProperty(value = "模块代码")
    private String vmodule;
    @ApiModelProperty(value = "用例名称")
    private String transName;
    @ApiModelProperty(value = "按钮状态 reject 拒绝 agree 同意 giveup 弃权")
    private String buttonType;
    @ApiModelProperty(value = "任务ID")
    private String taskId;
    @ApiModelProperty(value = "样式分类")
    private String styleType;

    @ApiModelProperty(value = "样式")
    private String style;

    @ApiModelProperty(value = "互斥显示")
    private Integer mutex;

    @ApiModelProperty(value = "对齐方式")
    private String horizontalAlign;

    @ApiModelProperty(value = "字段transCodes")
    private List<String> transCodes;

    @ApiModelProperty(value = "字段taskNames")
    private List<String> taskNames;

    @ApiModelProperty(value = "字段actionCodes")
    private List<String> actionCodes;

    @ApiModelProperty(value = "主操作标识")
    private Integer operationFlag;

}
