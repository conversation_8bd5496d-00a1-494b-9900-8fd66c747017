<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qm.ep.wf.mapper.WorkFlowLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qm.ep.wf.domain.bean.WorkFlowLogDO">
        <id column="ID" property="id"/>
        <result column="VTRANSCODE" property="vtranscode"/>
        <result column="VMENUNAME" property="vmenuname"/>
        <result column="NCOMPANYID" property="ncompanyid"/>
        <result column="NOPR" property="nopr"/>
        <result column="VOPR" property="vopr"/>
        <result column="VOPRNAME" property="voprname"/>
        <result column="DBEGIN" property="dbegin"/>
        <result column="DEND" property="dend"/>
        <result column="DPULSE" property="dpulse"/>
        <result column="NPROCESS" property="nprocess"/>
        <result column="PROCESSDEFKEY" property="processdefkey"/>
        <result column="BUSINESSKEY" property="businesskey"/>
        <result column="BUTTONNAME" property="buttonname"/>
        <result column="TRACEID" property="traceid"/>
        <result column="NBATCHCOUNT" property="nbatchcount"/>
        <result column="VPARA" property="vpara"/>
        <result column="DTSTAMP" property="dtstamp"/>
        <result column="RESULT" property="result"/>
        <result column="VDELETE" property="vdelete"/>
        <result column="VBATCHID" property="batchId"/>
        <result column="COMPLETENUM" property="completeNum"/>
        <result column="FAILNUM" property="failNum"/>
        <result column="SUCCESSNUM" property="successNum"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID
        , VTRANSCODE, VMENUNAME, NCOMPANYID, NOPR, VOPR, VOPRNAME, DBEGIN, DEND, DPULSE, NPROCESS, PROCESSDEFKEY, BUSINESSKEY, BUTTONNAME, TRACEID, NBATCHCOUNT, VPARA, DTSTAMP, RESULT, VDELETE,
VBATCHID,COMPLETENUM,FAILNUM,SUCCESSNUM
    </sql>

    <!-- 公共查询 -->
    <sql id="QuerySQL">
        select *
        from (
                 select a.VTRANSCODE,
                        a.VMENUNAME,
                        a.NCOMPANYID,
                        a.NOPR,
                        a.VOPR,
                        a.VOPRNAME,
                        a.DBEGIN,
                        a.DEND,
                        a.DPULSE,
                        a.NPROCESS,
                        a.PROCESSDEFKEY,
                        a.BUSINESSKEY,
                        a.BUTTONNAME,
                        a.TRACEID,
                        a.NBATCHCOUNT,
                        a.VPARA,
                        a.DTSTAMP,
                        a.VDELETE,
                        a.RESULT,
                        a.ID,
                        a.VBATCHID,
                        a.COMPLETENUM,
                        a.FAILNUM,
                        a.SUCCESSNUM
                 from act_ex_batchlog a
             ) innerTable
    </sql>
    <select id="selectList" resultType="com.qm.ep.wf.domain.vo.WorkFlowLogVO">
        select *
        from (
                 select a.VTRANSCODE,
                        a.VMENUNAME,
                        a.NCOMPANYID,
                        a.NOPR,
                        a.VOPR,
                        a.VOPRNAME,
                        a.DBEGIN,
                        a.DEND,
                        a.DPULSE,
                        a.NPROCESS,
                        a.PROCESSDEFKEY,
                        a.BUSINESSKEY,
                        a.BUTTONNAME,
                        a.TRACEID,
                        a.NBATCHCOUNT,
                        a.VPARA,
                        a.DTSTAMP,
                        a.VDELETE,
                        a.RESULT,
                        a.ID,
                        a.VBATCHID,
                        a.COMPLETENUM,
                        a.FAILNUM,
                        a.SUCCESSNUM,
                        (a.DEND -a.DBEGIN) as times
                 from act_ex_batchlog a
             ) innerTable
            ${ew.customSqlSegment}
    </select>
    <!-- 复写MP自带函数 -->
    <select id="selectByIdNew" resultType="com.qm.ep.wf.domain.bean.WorkFlowLogDO">
        <include refid="QuerySQL"/>
        where id = #{id}
    </select>
    <select id="selectBatchIdsNew" resultType="com.qm.ep.wf.domain.bean.WorkFlowLogDO">
        <include refid="QuerySQL"/>
        <if test="coll != null and !coll.isEmpty">
            <where>
                id in (<foreach collection="coll" item="item" separator=",">#{item}</foreach>)
            </where>
        </if>
    </select>
    <select id="selectByMapNew" resultType="com.qm.ep.wf.domain.bean.WorkFlowLogDO">
        <include refid="QuerySQL"/>
        <if test="cm != null and !cm.isEmpty">
            <where>
                <foreach collection="cm" index="k" item="v" separator="AND">
                    <choose>
                        <when test="v == null">${k} IS NULL</when>
                        <otherwise>${k} = #{v}</otherwise>
                    </choose>
                </foreach>
            </where>
        </if>
    </select>
    <select id="selectOne" resultType="com.qm.ep.wf.domain.bean.WorkFlowLogDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectCount" resultType="java.lang.Integer">
        select count(1) from (<include refid="QuerySQL"/>${ew.customSqlSegment} ) countTable
    </select>
    <select id="selectList" resultType="com.qm.ep.wf.domain.bean.WorkFlowLogDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectMaps" resultType="com.qm.ep.wf.domain.bean.WorkFlowLogDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectObjs" resultType="com.qm.ep.wf.domain.bean.WorkFlowLogDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectPage" resultType="com.qm.ep.wf.domain.bean.WorkFlowLogDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectMapsPage" resultType="com.qm.ep.wf.domain.bean.WorkFlowLogDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="queryData" resultType="com.qm.ep.wf.domain.bean.WorkFlowLogDO"
            parameterType="java.lang.String">
        ${value}
    </select>
</mapper>
