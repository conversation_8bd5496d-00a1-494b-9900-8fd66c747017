package com.qm.ep.wf.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR> @date
 */
@ApiModel(description = "流程实例请求")
@Data
public class ProcessInstanceRequest {
    @ApiModelProperty("进程定义 ID")
    private String processDefinitionId;
    @ApiModelProperty("流程定义键")
    private String processDefinitionKey;
    @ApiModelProperty("租户 ID")
    private String tenantId;
    @ApiModelProperty("业务密钥")
    private String businessKey;
    @ApiModelProperty("值")
    private Map<String, Object> values;
    @ApiModelProperty("流程实例 ID")
    private String processInstanceId;
    @ApiModelProperty("作者")
    private String oprator;
    @ApiModelProperty("数据OPR 日期")
    private Date oprDate;
}
