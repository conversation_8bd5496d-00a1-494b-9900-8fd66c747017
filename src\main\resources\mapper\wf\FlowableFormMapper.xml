<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.qm.ep.wf.mapper.FlowableFormMapper">

	<!-- 多表查询:根据条件得到多条记录List(查询条件按需修改!) -->
	<select id="list" resultType="com.qm.ep.wf.domain.entity.FlowableForm">
		select
		a.ID as ID,
		a.FORM_KEY as formKey,
		a.FORM_NAME as formName,
		a.CREATE_USER as createBy,
		a.CREATE_TIME as createTime,
		a.UPDATE_USER as updateBy,
		a.UPDATE_TIME as updateTime,
		a.dtstamp as dtstamp

		from T_FLOWABLE_FORM a
		where 1=1
		<if test="entity.formKey != null and entity.formKey !=''">
			<![CDATA[	AND a.FORM_KEY = #{entity.formKey}	]]>
		</if>
		<if test="entity.formName != null and entity.formName !=''">
			<![CDATA[	AND a.FORM_NAME = #{entity.formName}	]]>
		</if>
		order by a.CREATE_TIME desc
	</select>
</mapper>
