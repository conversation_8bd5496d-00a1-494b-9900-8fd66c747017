package com.qm.ep.wf.util;

import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 日期工具类
 *
 * <AUTHOR>
@Slf4j
public class DateUtil {

    /**
     * 这个类不能实例化
     */
    private DateUtil() {
    }

    public static final String DATE_FORMAT_DEFAULT = "yyyy-MM-dd";
    public static final String DATETIME_FORMAT_DEFAULT = "yyyy-MM-dd HH:mm:ss";

    /**
     * 锁对象
     */
    private static final Object LOCK_OBJ = new Object();
    /**
     * 存放不同的日期模板格式的sdf的Map
     */
    private static Map<String, ThreadLocal<SimpleDateFormat>> simpleDateFormatMap = new HashMap<>();

    /**
     * 返回一个ThreadLocal的sdf,每个线程只会new一次sdf
     *
     * @param pattern
     * @return
     */
    @SuppressWarnings({"squid:S3824", "squid:S4065"})
    public static SimpleDateFormat getSimpleDateFormat(final String pattern) {
        ThreadLocal<SimpleDateFormat> threadLocalSimpleDateFormat = simpleDateFormatMap.get(pattern);
        // 此处的双重判断和同步是为了防止simpleDateFormatMap这个单例被多次put重复的sdf
        if (threadLocalSimpleDateFormat == null) {
            synchronized (LOCK_OBJ) {
                threadLocalSimpleDateFormat = simpleDateFormatMap.get(pattern);
                if (threadLocalSimpleDateFormat == null) {
                    // 只有Map中还没有这个pattern的sdf才会生成新的sdf并放入map
                    // 这里是关键,使用ThreadLocal<SimpleDateFormat>替代原来直接new SimpleDateFormat
                    threadLocalSimpleDateFormat = new ThreadLocal<SimpleDateFormat>() {
                        @Override
                        protected SimpleDateFormat initialValue() {
                            return new SimpleDateFormat(pattern);
                        }
                    };
                    simpleDateFormatMap.put(pattern, threadLocalSimpleDateFormat);
                }
            }
        }
        return threadLocalSimpleDateFormat.get();
    }

    /**
     * 字符串转换成Date格式
     *
     * @param dateStr 日期型字符串
     * @param pattern 日期格式
     * @return
     */
    public static Date strToDate(String dateStr, String pattern) {
        try {
            if ((dateStr == null) || (dateStr.length() == 0)) {
                return null;
            }

            if (pattern == null) {
                pattern = DATE_FORMAT_DEFAULT;
            }

            SimpleDateFormat simpleDateFormat = getSimpleDateFormat(pattern);
            return simpleDateFormat.parse(dateStr);
        } catch (Exception e) {
            log.info("---error--"+dateStr + " strToDate转换：", e);
            return null;
        }
    }

    /**
     * 字符串转换的带time的Date格式
     *
     * @param dateStr
     * @return
     */
    public static Date strToDateTime(String dateStr) {
        try {
            return strToDate(dateStr, DATETIME_FORMAT_DEFAULT);
        } catch (Exception e) {
            log.info("---error--"+dateStr + " strToDateTime 转换", e);
            return null;
        }
    }

    /**
     * 给时间加上或减去指定毫秒,秒,分,时,天、月或年等,返回变动后的时间
     *
     * @param date   要加减前的时间,如果不传,则为当前日期
     * @param field  时间域,有Calendar.MILLISECOND,Calendar.SECOND,Calendar.MINUTE,<br>
     *               Calendar.HOUR,Calendar.DATE, Calendar.MONTH,Calendar.YEAR
     * @param amount 按指定时间域加减的时间数量,正数为加,负数为减.
     * @return 变动后的时间
     */
    public static Date add(Date date, int field, int amount) {
        if (date == null) {
            date = new Date();
        }

        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(field, amount);

        return cal.getTime();
    }

    /**
     * 计算两个时间差
     */
    public static String getDatePoor(long milliseconds) {
        final long day = TimeUnit.MILLISECONDS.toDays(milliseconds);

        final long hours = TimeUnit.MILLISECONDS.toHours(milliseconds)
                - TimeUnit.DAYS.toHours(TimeUnit.MILLISECONDS.toDays(milliseconds));

        final long minutes = TimeUnit.MILLISECONDS.toMinutes(milliseconds)
                - TimeUnit.HOURS.toMinutes(TimeUnit.MILLISECONDS.toHours(milliseconds));

        final long seconds = TimeUnit.MILLISECONDS.toSeconds(milliseconds)
                - TimeUnit.MINUTES.toSeconds(TimeUnit.MILLISECONDS.toMinutes(milliseconds));
        String dataStr = (day > 0 ? (day + "天") : "") + (hours > 0 ? (hours + "小时") : "") +
                (minutes > 0 ? (minutes + "分钟") : "") + (seconds > 0 ? (seconds + "秒") : "");
        if (ObjectUtils.isEmpty(dataStr)) {
            dataStr = "0秒";
        }
        return dataStr;
    }
}
