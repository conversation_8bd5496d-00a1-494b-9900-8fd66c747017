package com.qm.ep.wf.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 角色信息
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-17
 */
@ApiModel(value = "RoleDTO对象", description = "角色信息")
@Data
public class RoleDTO extends JsonParamDto {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "id")
    private String id;
    @ApiModelProperty(value = "角色代码")
    private String vrolename;
    @ApiModelProperty(value = "角色名称")
    private String vrolecode;
    @ApiModelProperty(value = "管理员 是  否")
    private String vmanagerflag;
    @ApiModelProperty(value = "公司Id")
    private String ncompanyid;
    @ApiModelProperty(value = "停用标识")
    private String vstop;
    @ApiModelProperty(value = "停用时间")
    private Date dstop;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Timestamp dtstamp;
    @ApiModelProperty(value = "预置数据标识-山推统一风格增加 ，1 预置，0 手工维护")
    private String vdefaultflag;
    @ApiModelProperty(value = "部门ID")
    private String ndeptid;
    @ApiModelProperty(value = "本公司标识")
    private String vlocalcompanyflag;
    @ApiModelProperty(value = "APP标识")
    private String vappflag;
    @ApiModelProperty(value = "用户ID")
    private String npersonid;
    @ApiModelProperty(value = "角色代码集合")
    private List<String> vrolecodes;

    @ApiModelProperty(value = "组织类型")
    private String vorgtype;

    @ApiModelProperty(value = "人员所属单位ID")
    private String norgid;

    @ApiModelProperty(value = "单位代码")
    private String vorgcode;

    @ApiModelProperty(value = "单位名称")
    private String vorgname;

    @ApiModelProperty(value = "人员代码")
    private String vpersoncode;

    @ApiModelProperty(value = "人员姓名")
    private String vpersonname;

    @ApiModelProperty(value = "模块")
    private String vmodule;

    @ApiModelProperty(value = "菜单名称")
    private String vtext;

    @ApiModelProperty(value = "人员姓名")
    private String vrealname;

    private List<String> ids;

    @ApiModelProperty(value = "语言代码")
    private String vlanguagecode;

}