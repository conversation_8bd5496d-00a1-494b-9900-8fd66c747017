package com.qm.ep.wf.service;

import com.qm.ep.wf.domain.bean.WorkFlowLogDO;
import com.qm.ep.wf.domain.dto.WorkFlowLogDTO;
import com.qm.ep.wf.domain.vo.WorkFlowLogVO;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.service.IQmBaseService;

import java.util.Map;

/**
 * <p>
 * 导出Excel日志 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-25
 */
public interface WorkFlowLogService extends IQmBaseService<WorkFlowLogDO> {
    QmPage<WorkFlowLogVO> query(WorkFlowLogDTO workFlowLogDTO);

    WorkFlowLogDO queryByBatchId(String batchId);

    void saveBatchCompleteInit(Map<String, Object> batchCompleteInfo);

    void saveBatchProcess(Map<String, Object> batchCompleteInfo, int failNum, int successNum, String msg);

}
