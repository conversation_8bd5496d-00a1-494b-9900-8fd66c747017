package com.qm.ep.wf.common;

import com.qm.ep.wf.util.SpringContextUtils;
import com.qm.tds.util.I18nUtil;

/**
 * <AUTHOR> @date
 */
public enum CommentTypeEnum {
    /**
     * 过程意见类型
     */
    CS("MSG.wf.CommentTypeEnum.init"),
    <PERSON><PERSON>("MSG.wf.CommentTypeEnum.commit"),
    <PERSON><PERSON><PERSON>("MSG.wf.CommentTypeEnum.recommit"),
    R<PERSON>("MSG.wf.CommentTypeEnum.claim"),
    QXRL("MSG.wf.CommentTypeEnum.disclaim"),
    <PERSON>("MSG.wf.CommentTypeEnum.approve"),
    <PERSON>("MSG.wf.CommentTypeEnum.finish"),
    T<PERSON>("MSG.wf.CommentTypeEnum.return"),
    <PERSON>("MSG.wf.CommentTypeEnum.withdraw"),
    <PERSON><PERSON>("MSG.wf.CommentTypeEnum.tempStorage"),
    Z<PERSON>("MSG.wf.CommentTypeEnum.turnTo"),
    W<PERSON>("MSG.wf.CommentTypeEnum.delegate"),
    <PERSON><PERSON>("MSG.wf.CommentTypeEnum.stop");

    /**
     * 名称
     */
    private String name;

    private static I18nUtil i18nUtil;

    private static I18nUtil getI18nUtil() {
        if (i18nUtil == null) {
            i18nUtil = SpringContextUtils.getBean(I18nUtil.class);
        }
        return i18nUtil;
    }


    public static String getEnumMsgByType(String type) {
        for (CommentTypeEnum e : CommentTypeEnum.values()) {
            if (e.toString().equals(type)) {
                return getI18nUtil().getMessage(e.name);
            }
        }
        return "";
    }

    private CommentTypeEnum(String name) {
        this.name = name;
    }

    public String getName() {
        return getI18nUtil().getMessage(name);
    }

}
