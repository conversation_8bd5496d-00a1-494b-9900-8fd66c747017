package com.qm.ep.wf.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("act_ex_listener")
@ApiModel(value = "对象ProcessListenerDO对象", description = "对象ProcessListenerDO对象")
public class ProcessListenerDO implements Serializable {

    @ApiModelProperty("串行版 uid")
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty(value = "名称")
    @TableField("NAME")
    private String name;

    @ApiModelProperty(value = "监听器类型  字典项:执行监听器executionListener；任务监听器taskListener")
    @TableField("LISTENER_TYPE")
    private String listenerType;

    @ApiModelProperty(value = "事件类型 执行监听器对应（start；take；end）任务监听器对应（assignment；create；complete；delete）")
    @TableField("EVENT_TYPE")
    private String eventType;

    @ApiModelProperty(value = "值类型 字典项（类；表达式；委托表达式）")
    @TableField("VALUE_TYPE")
    private String valueType;

    @ApiModelProperty(value = "类/表达式/委托表达式")
    @TableField("EXPRESSION")
    private String expression;

    @ApiModelProperty(value = "流程定义Key 对应bpmn中process的ID")
    @TableField("PROCESS_DEF_KEY")
    private String processDefKey;

    @ApiModelProperty(value = "时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    @Version
    private Timestamp dtstamp;
}
