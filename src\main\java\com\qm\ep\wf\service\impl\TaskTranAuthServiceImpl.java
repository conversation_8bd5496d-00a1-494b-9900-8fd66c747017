package com.qm.ep.wf.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qm.ep.wf.domain.bean.ProcessCommonButtonsDO;
import com.qm.ep.wf.domain.bean.TaskAuthDO;
import com.qm.ep.wf.domain.bean.TaskTranAuthDO;
import com.qm.ep.wf.domain.bean.TransCodeDO;
import com.qm.ep.wf.domain.dto.ProcessCommonButtonsDTO;
import com.qm.ep.wf.domain.dto.TaskTranAuthDTO;
import com.qm.ep.wf.domain.vo.ProcessDefKeyNameVo;
import com.qm.ep.wf.domain.vo.ProcessDefinitionResponse;
import com.qm.ep.wf.domain.vo.RoleVO;
import com.qm.ep.wf.exception.FlowableTaskException;
import com.qm.ep.wf.mapper.TaskTranAuthMapper;
import com.qm.ep.wf.remote.SysFeignRemote;
import com.qm.ep.wf.service.ProcessCommonButtonsService;
import com.qm.ep.wf.service.ProcessInstanceService;
import com.qm.ep.wf.service.TaskTranAuthService;
import com.qm.ep.wf.service.WfConfService;
import com.qm.ep.wf.util.JacksonUtil;
import com.qm.ep.wf.util.ObjectUtils;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import com.qm.tds.api.service.impl.QmBaseServiceImpl;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.RedisUtils;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.bpmn.model.ExtensionElement;
import org.flowable.bpmn.model.Process;
import org.flowable.bpmn.model.UserTask;
import org.flowable.engine.RepositoryService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-19
 */
@Service
public class TaskTranAuthServiceImpl extends QmBaseServiceImpl<TaskTranAuthMapper, TaskTranAuthDO> implements TaskTranAuthService {

    @Autowired
    protected RepositoryService repositoryService;
    @Autowired
    SysFeignRemote sysFeignRemote;
    @Autowired
    private ProcessCommonButtonsService processCommonButtonsService;
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private WfConfService wfConfService;
    @Autowired
    private TaskAuthServiceImpl taskAuthServiceImpl;

    @Autowired
    private ProcessInstanceService processInstanceService;

    @Override
    public JsonResultVo<QmPage<TaskTranAuthDO>> searchTransAuth(TaskTranAuthDTO tempDTO) {
        //定义查询构造器
        QmQueryWrapper<TaskTranAuthDO> queryWrapper = new QmQueryWrapper<>();
        //拼装实体属性查询条件
        LambdaQueryWrapper<TaskTranAuthDO> lambdaWrapper = queryWrapper.lambda();
        //主键ID
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getId())) lambdaWrapper.eq(TaskTranAuthDO::getId, tempDTO.getId());
        //任务名称
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getTaskName()))
            lambdaWrapper.eq(TaskTranAuthDO::getTaskName, tempDTO.getTaskName());
        //事务码
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getTransCode()))
            lambdaWrapper.eq(TaskTranAuthDO::getTransCode, tempDTO.getTransCode());
        //工作流模版Key
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getProcessDefKey()))
            lambdaWrapper.eq(TaskTranAuthDO::getProcessDefKey, tempDTO.getProcessDefKey());
        //时间戳
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getDtstamp()))
            lambdaWrapper.eq(TaskTranAuthDO::getDtstamp, tempDTO.getDtstamp());
        //按钮code
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getButtonCode()))
            lambdaWrapper.eq(TaskTranAuthDO::getButtonCode, tempDTO.getButtonCode());


        if (tempDTO.getAuthorization() != null) {
            lambdaWrapper.eq(TaskTranAuthDO::getAuthorization, tempDTO.getAuthorization());
        }


        lambdaWrapper.groupBy(TaskTranAuthDO::getTransCode);
        //查询数据，使用table函数。
        QmPage<TaskTranAuthDO> list = table(queryWrapper, tempDTO);
        // 处理查询，组件新的参数查询事务码数据
        JsonResultVo<QmPage<TaskTranAuthDO>> ret = new JsonResultVo<>();
        ret.setData(list);
        return ret;
    }

    @Override
    public boolean saveOrUpdateBatchAndRedis(List<TaskTranAuthDO> list) {
        for (TaskTranAuthDO taskTranAuthDO : list) {
            LambdaQueryWrapper<TaskTranAuthDO> lambdaQueryWrapper = new LambdaQueryWrapper();
            lambdaQueryWrapper.eq(TaskTranAuthDO::getProcessDefKey, taskTranAuthDO.getProcessDefKey());
            lambdaQueryWrapper.eq(TaskTranAuthDO::getTaskName, taskTranAuthDO.getTaskName());
            lambdaQueryWrapper.eq(TaskTranAuthDO::getButtonCode, taskTranAuthDO.getButtonCode());
            lambdaQueryWrapper.eq(TaskTranAuthDO::getTransCode, taskTranAuthDO.getTransCode());
            List<TaskTranAuthDO> taskTranAuthDOS = getQmBaseMapper().selectList(lambdaQueryWrapper);
            if (!CollectionUtils.isEmpty(taskTranAuthDOS)) {
                getQmBaseMapper().delete(lambdaQueryWrapper);
            }
        }

        boolean flag = saveOrUpdateBatch(list);
        // 同步REDIS数据
        if (flag && !CollectionUtils.isEmpty(list)) {
            List<String> pdf = list.stream().map(TaskTranAuthDO::getProcessDefKey).distinct().collect(Collectors.toList());
            Map<String, List<RoleVO>> roleMaps = processInstanceService.cacheCurrentAllUserRole();
            pdf.forEach(key -> createRedisData(key, roleMaps));
        }
        return flag;
    }

    @Override
    public boolean removeByMapAndRedis(Map map) {
        boolean flag = removeByMap(map);
        Map<String, List<RoleVO>> roleMaps = processInstanceService.cacheCurrentAllUserRole();
        if (flag) createRedisData(map.get("PROCESS_DEF_KEY").toString(), roleMaps);
        return flag;
    }

    /**
     * 构建保存到redis的流程节点按钮数据，方法为通用方法，应实现所有场景，包括数据不存在的删除情况，所有对权限的操作都应调用本方法
     *
     * @param processDefKey
     */
    @Override
    public void createRedisData(String processDefKey) {
        // 1、首先使用流程定义和节点查询已授权的事务码
        QmQueryWrapper<TaskTranAuthDO> queryWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<TaskTranAuthDO> lambdaWrapper = queryWrapper.lambda();
        lambdaWrapper.eq(TaskTranAuthDO::getProcessDefKey, processDefKey);
        List<TaskTranAuthDO> list = list(queryWrapper);

        // 2、删除事务码为key相关的所有数据，重新构建存储
        String delKey = redisUtils.keyBuilder("wf", processDefKey);
        redisUtils.del(redisUtils.getKeys(delKey));

        // 查询所有操作授权，并按照taskName为key转为map
        QmQueryWrapper<TaskAuthDO> taskAuthQueryWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<TaskAuthDO> taskAuthLambdaWrapper = taskAuthQueryWrapper.lambda();
        taskAuthLambdaWrapper.eq(TaskAuthDO::getProcessDefKey, processDefKey);
        List<TaskAuthDO> allauthList = taskAuthServiceImpl.list(taskAuthQueryWrapper);
        Map<String, List<TaskAuthDO>> authMap = allauthList.stream().collect(Collectors.groupingBy(TaskAuthDO::getTaskName));

        String roleKey = redisUtils.keyBuilder("wf", "role", "all", "v2");
        Object obj = redisUtils.get(roleKey);
        Map<String, List<RoleVO>> roleMap = null;
        if (obj != null) {
            roleMap = (Map<String, List<RoleVO>>) obj;
        }
        if (ObjectUtils.isEmpty(roleMap)) {
            // 查询所有角色对应code
            JsonResultVo<List<RoleVO>> roleResult = sysFeignRemote.getAllRoles();
            if (roleResult.getCode() == 500) throw new FlowableTaskException(roleResult.getMsg());

            Map<String, List<RoleVO>> collect = roleResult.getData().stream().collect(Collectors.groupingBy(item -> item.getId()));
            roleMap = collect;
        }

        // 查询所有的按钮，然后循环取按钮对应的事务和操作授权
        List<ProcessCommonButtonsDTO> processButtonDtos = new ArrayList<>();
        selectButtonsAndAuth(processButtonDtos, list, allauthList, processDefKey);

        if (!list.isEmpty()) for (TaskTranAuthDO taskTranAuthDO : list) {
            List<TaskAuthDO> authList = authMap.get(taskTranAuthDO.getTaskName());
            // 5、存在操作授权，则用户或角色code也作为key的一部分，查询对应的按钮数据
            if (BootAppUtil.isNullOrEmpty(authList)) {
                // 4、不存在操作授权，则直接查询按钮数据，只以事务码为key存储按钮数据
                List<ProcessCommonButtonsDTO> buttons = processButtonDtos.stream().filter(item ->
                        item.getProcessDefKey().equals(taskTranAuthDO.getProcessDefKey()) && item.getTaskNames().contains(taskTranAuthDO.getTaskName())
                                && item.getTransCodes().contains(taskTranAuthDO.getTransCode())).collect(Collectors.toList());
                if (!buttons.isEmpty())
                    buttons.forEach(button -> button.setTransCode(taskTranAuthDO.getTransCode()));
                String key = redisUtils.keyBuilder("wf", taskTranAuthDO.getProcessDefKey(), taskTranAuthDO.getTaskName(), taskTranAuthDO.getTransCode());
                redisUtils.set(key, JacksonUtil.objToStr(buttons));
            } else for (TaskAuthDO taskAuthDO : authList) {
                List<ProcessCommonButtonsDTO> buttons = processButtonDtos.stream().filter(item ->
                        item.getProcessDefKey().equals(processDefKey) && item.getTaskNames().contains(taskAuthDO.getTaskName())
                                && item.getTransCodes().contains(taskTranAuthDO.getTransCode())
                                && item.getActionCodes().contains(taskAuthDO.getActionCode())).collect(Collectors.toList());
                if (!buttons.isEmpty())
                    buttons.forEach(button -> button.setTransCode(taskTranAuthDO.getTransCode()));
                if ("0".equals(taskAuthDO.getActionType())) {
                    String key = redisUtils.keyBuilder("wf", taskTranAuthDO.getProcessDefKey(), taskTranAuthDO.getTaskName(), taskTranAuthDO.getTransCode(), taskAuthDO.getActionCode().toUpperCase());
                    redisUtils.set(key, JacksonUtil.objToStr(buttons));
                } else if (!BootAppUtil.isNullOrEmpty(roleMap)) {
                    List<RoleVO> roleCodeList = roleMap.get(taskAuthDO.getActionCode());
                    if (null != roleCodeList) for (RoleVO code : roleCodeList) {
                        String key = redisUtils.keyBuilder("wf", taskTranAuthDO.getProcessDefKey(), taskTranAuthDO.getTaskName(), taskTranAuthDO.getTransCode(), code.getId().toUpperCase());
                        redisUtils.set(key, JacksonUtil.objToStr(buttons));
                    }
                }
            }
        }
    }

    @SuppressWarnings("squid:S135")
    @Override
    public void createRedisData(String processDefKey, Map<String, List<RoleVO>> roleMap) {
        // 1、首先使用流程定义和节点查询已授权的事务码
        QmQueryWrapper<TaskTranAuthDO> queryWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<TaskTranAuthDO> lambdaWrapper = queryWrapper.lambda();
        lambdaWrapper.eq(TaskTranAuthDO::getProcessDefKey, processDefKey);
        List<TaskTranAuthDO> list = list(queryWrapper);

        // 2、删除事务码为key相关的所有数据，重新构建存储
        String delKey = redisUtils.keyBuilder("wf", processDefKey);
        redisUtils.del(redisUtils.getKeys(delKey));


        // 查询所有操作授权，并按照taskName为key转为map
        QmQueryWrapper<TaskAuthDO> taskAuthQueryWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<TaskAuthDO> taskAuthLambdaWrapper = taskAuthQueryWrapper.lambda();
        taskAuthLambdaWrapper.eq(TaskAuthDO::getProcessDefKey, processDefKey);
        List<TaskAuthDO> allauthList = taskAuthServiceImpl.list(taskAuthQueryWrapper);
        Map<String, List<TaskAuthDO>> authMap = allauthList.stream().collect(Collectors.groupingBy(TaskAuthDO::getTaskName));

        // 查询所有的按钮，然后循环取按钮对应的事务和操作授权
        List<ProcessCommonButtonsDTO> processButtonDtos = new ArrayList<>();
        selectButtonsAndAuth(processButtonDtos, list, allauthList, processDefKey);

        for (TaskTranAuthDO taskTranAuthDO : list) {
            List<TaskAuthDO> authList = authMap.get(taskTranAuthDO.getTaskName());
            // 4、不存在操作授权，则直接查询按钮数据，只以事务码为key存储按钮数据
            if (BootAppUtil.isNullOrEmpty(authList)) {
                List<ProcessCommonButtonsDTO> buttons = processButtonDtos.stream().filter(item ->
                        item.getProcessDefKey().equals(taskTranAuthDO.getProcessDefKey()) && item.getTaskNames().contains(taskTranAuthDO.getTaskName())
                                && item.getTransCodes().contains(taskTranAuthDO.getTransCode())).collect(Collectors.toList());
                if (!buttons.isEmpty())
                    buttons.forEach(button -> button.setTransCode(taskTranAuthDO.getTransCode()));
                String key = redisUtils.keyBuilder("wf", taskTranAuthDO.getProcessDefKey(), taskTranAuthDO.getTaskName(), taskTranAuthDO.getTransCode());
                redisUtils.set(key, JacksonUtil.objToStr(buttons));
                continue;
            }

            for (TaskAuthDO taskAuthDO : authList) {
                List<ProcessCommonButtonsDTO> buttons = processButtonDtos.stream().filter(item ->
                        item.getProcessDefKey().equals(processDefKey) && item.getTaskNames().contains(taskAuthDO.getTaskName())
                                && item.getTransCodes().contains(taskTranAuthDO.getTransCode())
                                && item.getActionCodes().contains(taskAuthDO.getActionCode())).collect(Collectors.toList());

                if ("0".equals(taskAuthDO.getActionType())) {
                    String key = redisUtils.keyBuilder("wf", taskTranAuthDO.getProcessDefKey(), taskTranAuthDO.getTaskName(), taskTranAuthDO.getTransCode(), taskAuthDO.getActionCode().toUpperCase());
                    redisUtils.set(key, JacksonUtil.objToStr(buttons));
                    continue;
                }
                if (!BootAppUtil.isNullOrEmpty(roleMap)) {
                    List<RoleVO> personCodeList = roleMap.get(taskAuthDO.getActionCode());
                    if (null == personCodeList) {
                        continue;
                    }
                    for (RoleVO code : personCodeList) {
                        String key = redisUtils.keyBuilder("wf", taskTranAuthDO.getProcessDefKey(), taskTranAuthDO.getTaskName(), taskTranAuthDO.getTransCode(), code.getId().toUpperCase());
                        redisUtils.set(key, JacksonUtil.objToStr(buttons));
                    }
                }
            }
        }
    }

    @SuppressWarnings("squid:S1192")
    private void selectButtonsAndAuth(List<ProcessCommonButtonsDTO> processButtonDtos, List<TaskTranAuthDO> list, List<TaskAuthDO> allauthList, String processDefKey) {
        QmQueryWrapper<ProcessCommonButtonsDO> buttonQueryWrapper = new QmQueryWrapper<>();
        buttonQueryWrapper.eq("processDefKey", processDefKey);
        List<ProcessCommonButtonsDO> processButtons = processCommonButtonsService.list(buttonQueryWrapper);
        for (ProcessCommonButtonsDO processButton : processButtons) {
            List<TaskTranAuthDO> tranAuthList = list.stream().filter(item -> item.getButtonCode().equals(processButton.getCode()) && item.getProcessDefKey().equals(processButton.getProcessDefKey())).collect(Collectors.toList());
            List<TaskAuthDO> actorAuthList = allauthList.stream().filter(item -> item.getButtonCode().equals(processButton.getCode()) && item.getProcessDefKey().equals(processButton.getProcessDefKey())).collect(Collectors.toList());
            List<String> taskNames = tranAuthList.stream().map(TaskTranAuthDO::getTaskName).collect(Collectors.toList());
            List<String> transCodes = tranAuthList.stream().map(TaskTranAuthDO::getTransCode).collect(Collectors.toList());
            List<String> actionCodes = actorAuthList.stream().map(TaskAuthDO::getActionCode).collect(Collectors.toList());
            ProcessCommonButtonsDTO dto = new ProcessCommonButtonsDTO();
            BeanUtils.copyProperties(processButton, dto);
            dto.setTaskNames(taskNames);
            dto.setTransCodes(transCodes);
            dto.setActionCodes(actionCodes);
            processButtonDtos.add(dto);
        }
    }

    @Override
    public JsonResultVo<QmPage<TransCodeDO>> searchTable(TaskTranAuthDTO tempDTO) {
        //定义查询构造器
        QmQueryWrapper<TaskTranAuthDO> queryWrapper = new QmQueryWrapper<>();
        //拼装实体属性查询条件
        LambdaQueryWrapper<TaskTranAuthDO> lambdaWrapper = queryWrapper.lambda();
        //主键ID
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getId())) lambdaWrapper.eq(TaskTranAuthDO::getId, tempDTO.getId());
        //任务名称
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getTaskName()))
            lambdaWrapper.eq(TaskTranAuthDO::getTaskName, tempDTO.getTaskName());
        //事务码
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getTransCode()))
            lambdaWrapper.eq(TaskTranAuthDO::getTransCode, tempDTO.getTransCode());
        //工作流模版Key
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getProcessDefKey()))
            lambdaWrapper.eq(TaskTranAuthDO::getProcessDefKey, tempDTO.getProcessDefKey());
        //时间戳
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getDtstamp()))
            lambdaWrapper.eq(TaskTranAuthDO::getDtstamp, tempDTO.getDtstamp());
        //按钮code
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getButtonCode()))
            lambdaWrapper.eq(TaskTranAuthDO::getButtonCode, tempDTO.getButtonCode());
        //模块
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getVmodule())) {
            Set<String> modules = wfConfService.getWfConfByModule(tempDTO.getVmodule(), 2);
            if (modules.isEmpty()) modules.add("non");
            lambdaWrapper.in(TaskTranAuthDO::getProcessDefKey, modules);
        }
        //查询数据，使用table函数。
        QmPage<TaskTranAuthDO> list = table(queryWrapper, tempDTO);
        // 处理查询，组件新的参数查询事务码数据
        JsonResultVo<QmPage<TransCodeDO>> ret = new JsonResultVo<>();
        if (!list.getItems().isEmpty()) {
            List<String> codes = new ArrayList<>();
            list.getItems().forEach(item -> codes.add(item.getTransCode()));
            ret = sysFeignRemote.getTransCode(BootAppUtil.getLoginKey().getTenantId(), codes);
            if (ret.getCode() == 500) throw new FlowableTaskException(ret.getMsg());
        } else ret.setData(new QmPage<>());

        //数据库存在相同数据，采用覆盖策略
        Map<String, Integer> collect = list.getItems().stream().collect(Collectors.toMap(TaskTranAuthDO::getTransCode, TaskTranAuthDO::getAuthorization, (val, val2) -> val2));
        if (ret.getData() == null || ret.getData().getItems() == null) {
            return ret;
        }
        for (TransCodeDO item : ret.getData().getItems()) {
            if (!collect.containsKey(item.getVcode())) {
                item.setAuthorization(0);
            }
            item.setAuthorization(collect.get(item.getVcode()));
        }
        return ret;
    }

    /**
     * @description 根据任务id、事务码、流程定义key判断是否有权限
     * <AUTHOR>
     * @date 2020/8/21 16:16
     */
    @Override
    public boolean judgeTaskAuth(String taskName, String transCode, String processDefKey, String buttonCode) {
        TaskTranAuthDTO taskTranAuthDTO = new TaskTranAuthDTO();
        taskTranAuthDTO.setProcessDefKey(processDefKey);
        taskTranAuthDTO.setTransCode(transCode);
        taskTranAuthDTO.setTaskName(taskName);
        taskTranAuthDTO.setButtonCode(buttonCode);
        Integer num = baseMapper.judgeTaskAuth(taskTranAuthDTO);
        return num > 0;
    }

    @Override
    public List<TaskTranAuthDO> selectTaskAuthByParams(String taskName, String transCode, String processDefKey, String buttonCode) {
        TaskTranAuthDTO taskTranAuthDTO = new TaskTranAuthDTO();
        taskTranAuthDTO.setProcessDefKey(processDefKey);
        taskTranAuthDTO.setTransCode(transCode);
        taskTranAuthDTO.setTaskName(taskName);
        taskTranAuthDTO.setButtonCode(buttonCode);
        return baseMapper.selectTaskAuthByParams(taskTranAuthDTO);
    }

    @Override
    public List procDefList(List list) {
        // 查询流程下任务节点，并重新构造数据
        List<ProcessDefKeyNameVo> lists = new ArrayList<>();
        if (!list.isEmpty()) for (int i = 0, len = list.size(); i < len; i++) {
            ProcessDefinitionResponse p = (ProcessDefinitionResponse) list.get(i);
            List<Map<String, Object>> userButtons = getTask(p.getId(), p.getKey());
            ProcessDefKeyNameVo processDefKeyNameVo = new ProcessDefKeyNameVo();
            processDefKeyNameVo.setId(p.getKey());
            processDefKeyNameVo.setName(p.getName());
            processDefKeyNameVo.setVersion(p.getVersion());
            processDefKeyNameVo.setChildren(userButtons);
            lists.add(processDefKeyNameVo);
        }
        return lists;
    }

    /**
     * @description 获取人工任务节点
     * <AUTHOR>
     * @date 2020/8/18 13:18
     */
    private List<Map<String, Object>> getTask(String processDefID, String processDefKey) {
        //通过流程定义ID获取流程模板
        BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefID);
        List customButtons = new ArrayList();
        if (null != bpmnModel) {
            List<Process> processList = bpmnModel.getProcesses();
            for (Process process : processList) {
                Collection<UserTask> flowElements = process.findFlowElementsOfType(UserTask.class);
                for (UserTask userTask : flowElements) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("id", userTask.getId());
                    map.put("name", userTask.getName());
                    map.put("key", processDefKey);
                    // 查询任务节点下的按钮
                    List buttons = getUserTaskButtons(processDefKey, userTask);
                    map.put("children", buttons);
                    // 这里加一个返回值，目的是给前端进行判断，如果任务存在按钮，则可以被多选框选中
                    if (!buttons.isEmpty()) map.put("isChildren", true);
                    customButtons.add(map);
                }
            }
        }
        return customButtons;
    }

    /**
     * @description 查询人工任务节点按钮
     * <AUTHOR>
     * @date 2020/8/21 15:57
     */
    @SuppressWarnings("squid:S1192")
    private List getUserTaskButtons(Object processDefinitionKey, UserTask userTask) {
        List list = new ArrayList();
        List<ExtensionElement> elements = userTask.getExtensionElements().get("button");
        if (!BootAppUtil.isNullOrEmpty(elements)) for (ExtensionElement button : elements) {
            String code = button.getAttributes().get("code").get(0).getValue();
            //定义查询构造器
            QmQueryWrapper<ProcessCommonButtonsDO> queryWrapper = new QmQueryWrapper<>();
            //拼装实体属性查询条件
            //流程定义Key
            queryWrapper.eq("processDefKey", processDefinitionKey);
            //	代码
            queryWrapper.eq("code", code);
            QmPage<ProcessCommonButtonsDO> commonButtons = processCommonButtonsService.table(queryWrapper, new ProcessCommonButtonsDTO());
            if (!commonButtons.getItems().isEmpty()) {
                ProcessCommonButtonsDO processCommonButtonsDO = commonButtons.getItems().get(0);
                Map<String, Object> map = new HashMap();
                // 生成一个随机得id作为key，避免重复
                map.put("id", UUID.randomUUID());
                map.put("name", processCommonButtonsDO.getName());
                map.put("code", processCommonButtonsDO.getCode());
                map.put("remark", processCommonButtonsDO.getRemark());
                map.put("stateCode", processCommonButtonsDO.getStateCode());
                map.put("processDefKey", processDefinitionKey);
                map.put("taskName", userTask.getId());
                // 按钮永远可以被选中，配合任务节点判断使用统一变量，方便前端控制，
                map.put("isChildren", true);
                list.add(map);
            }
        }
        return list;
    }
}
