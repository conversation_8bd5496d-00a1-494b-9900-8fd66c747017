package com.qm.ep.wf.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.qm.ep.wf.common.CommentTypeEnum;
import com.qm.ep.wf.common.ResponseFactory;
import com.qm.ep.wf.common.cmd.GetProcessDefinitionInfoCmd;
import com.qm.ep.wf.constant.Constants;
import com.qm.ep.wf.constant.FlowableConstant;
import com.qm.ep.wf.domain.bean.ActHiCommentCodeDO;
import com.qm.ep.wf.domain.bean.ActHistoryCommentDO;
import com.qm.ep.wf.domain.bean.ProcessCommonButtonsDO;
import com.qm.ep.wf.domain.dto.ProcessCommonButtonsDTO;
import com.qm.ep.wf.domain.entity.WfConf;
import com.qm.ep.wf.domain.vo.ProcessDefinitionResponse;
import com.qm.ep.wf.domain.vo.ProcessInstanceRequest;
import com.qm.ep.wf.domain.vo.RoleVO;
import com.qm.ep.wf.domain.vo.TaskResponse;
import com.qm.ep.wf.exception.FlowableTaskException;
import com.qm.ep.wf.mapper.*;
import com.qm.ep.wf.service.*;
import com.qm.ep.wf.service.impl.v2.ProcessInstanceServiceV2Impl;
import com.qm.ep.wf.util.*;
import com.qm.ep.wf.wapper.IListWrapper;
import com.qm.ep.wf.wapper.ProcDefListWrapper;
import com.qm.ep.wf.wapper.TaskTodoListWrapper;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import com.qm.tds.api.service.impl.QmBaseServiceImpl;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.I18nUtil;
import com.qm.tds.util.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.flowable.bpmn.model.Process;
import org.flowable.bpmn.model.*;
import org.flowable.common.engine.api.FlowableException;
import org.flowable.common.engine.api.FlowableObjectNotFoundException;
import org.flowable.common.engine.api.query.Query;
import org.flowable.common.engine.impl.identity.Authentication;
import org.flowable.editor.language.json.converter.util.CollectionUtils;
import org.flowable.engine.*;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.impl.persistence.entity.ActivityInstanceEntity;
import org.flowable.engine.impl.persistence.entity.ExecutionEntity;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.repository.ProcessDefinitionQuery;
import org.flowable.engine.runtime.ActivityInstance;
import org.flowable.engine.runtime.Execution;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.engine.runtime.ProcessInstanceBuilder;
import org.flowable.engine.task.Comment;
import org.flowable.task.api.Task;
import org.flowable.task.api.TaskQuery;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> @date
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public abstract class ProcessInstanceServiceImpl  extends QmBaseServiceImpl<ProcessCommonButtonsMapper, ProcessCommonButtonsDO> implements ProcessInstanceService {
    @Autowired
    protected ResponseFactory responseFactory;
    @Autowired
    protected ManagementService managementService;
    @Autowired
    protected RuntimeService runtimeService;
    @Autowired
    protected HistoryService historyService;
    @Autowired
    protected PermissionServiceImpl permissionService;
    @Autowired
    protected FlowableTaskServiceImpl flowableTaskService;
    @Autowired
    protected TaskService taskService;
    @Autowired
    protected ProcessDefinitionService processDefinitionService;
    @Autowired
    protected RepositoryService repositoryService;

    @Autowired
    private RunFlowableActinstMapper runFlowableActinstDao;
    @Autowired
    private HisFlowableActinstMapper hisFlowableActinstDao;

    @Autowired
    protected WfConfService wfConfService;
    @Autowired
    protected ProcessCommonButtonsService processCommonButtonsService;
    @Autowired
    protected TaskAuthService taskAuthService;
    @Autowired
    protected TaskTranAuthService taskTranAuthService;
    @Autowired
    protected RedisUtils redisUtils;
    @Autowired
    private ActHistoryCommentMapper actHistoryCommentMapper;

    @Autowired
    private ProcessInstanceServiceV2Impl processInstanceService;
    @Autowired
    private I18nUtil i18nUtil;

    @Autowired
    private ActHiCommentCodeMapper actHiCommentCodeMapper;

    @Value("${ep.lang.multiFlag:false}")
    private Boolean multiFlag;

    @Override
    public List<ActHistoryCommentDO> getHistoryComments(String processDefKey, String businessKey) {
        QueryWrapper<ActHistoryCommentDO> queryCommentWrapper = new QueryWrapper<>();
        queryCommentWrapper.eq("PROCESS_DEF_KEY", processDefKey);
        queryCommentWrapper.eq("BUSINESS_KEY", businessKey);
        queryCommentWrapper.orderByAsc("TIME_");
        return actHistoryCommentMapper.selectList(queryCommentWrapper);
    }

    @Override
    public ProcessInstance getProcessInstanceById(String processInstanceId) {
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                .processInstanceId(processInstanceId).singleResult();
        if (processInstance == null) {
            throw new FlowableObjectNotFoundException("No process instance found with id " + processInstanceId);
        }
        return processInstance;
    }

    @Override
    public HistoricProcessInstance getHistoricProcessInstanceById(String processInstanceId) {
        HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                .processInstanceId(processInstanceId).singleResult();
        if (historicProcessInstance == null) {
            throw new FlowableObjectNotFoundException("No process instance found with id " + processInstanceId);
        }
        return historicProcessInstance;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String start(ProcessInstanceRequest processInstanceRequest) {
        //默认不开始跳过操作
        if (null != processInstanceRequest.getValues()) {
            processInstanceRequest.getValues().put(FlowableConstant.FLOWABLE_SKIP_EXPRESSION_ENABLED, false);
            processInstanceRequest.getValues().put("skip", false);
        }
        String processDefinitionId = CommonUtil.trimToEmptyStr(processInstanceRequest.getProcessDefinitionId());
        String processDefinitionKey = CommonUtil.trimToEmptyStr(processInstanceRequest.getProcessDefinitionKey());

        if (processDefinitionId.length() == 0 && processDefinitionKey.length() == 0) {
            throw new FlowableException("request param both processDefinitionId and processDefinitionKey is not found");
        } else if (processDefinitionId.length() != 0 && processDefinitionKey.length() != 0) {
            throw new FlowableException("request param both processDefinitionId and processDefinitionKey is found");
        }
        ProcessDefinition definition = null;
        if (processDefinitionKey.length() != 0) {
            definition = processDefinitionService.getProcessDefinitionByPKey(processDefinitionKey);
            processDefinitionId = definition.getId();
        }
        String userId = LoginUtil.getOperatorId();
        if (CommonUtil.isNotEmptyAfterTrim(processInstanceRequest.getOprator())) {
            userId = processInstanceRequest.getOprator();
        }
        //获取流程定义，不考虑权限
        if (null == definition)
            definition = managementService
                    .executeCommand(new GetProcessDefinitionInfoCmd(processDefinitionId, processDefinitionKey, processInstanceRequest.getTenantId()));
        //流程启动变量
        Map<String, Object> startVariables = null;
        if (null != processInstanceRequest.getValues() && !processInstanceRequest.getValues().isEmpty()) {
            startVariables = processInstanceRequest.getValues();
            startVariables.put(FlowableConstant.SKIP_SIGN, "0");
        }
        if (!ObjectUtils.isEmpty(processInstanceRequest.getValues())
                && ObjectUtils.isEmpty(processInstanceRequest.getValues().get(FlowableConstant.PROCESS_BUSSINESS_TABLE))) {
            //这里查询是否存在工作流配置，如存在，将扩展信息写入启动的流程变量里
            WfConf wfConf = wfConfService.getWfConfByWF(processDefinitionKey);
            if (null != wfConf && null != startVariables) {
                startVariables.put(FlowableConstant.PROCESS_BUSSINESS_TABLE, wfConf.getBusiTableName());
                startVariables.put(FlowableConstant.PROCESS_STATE_FIELDNAME, wfConf.getStateFieldName());
                startVariables.put(FlowableConstant.PROCESS_KEY_FIELDNAME, wfConf.getBusiKeyFieldName());
            }
        }
        Authentication.setAuthenticatedUserId(userId);

        ProcessInstanceBuilder processInstanceBuilder = runtimeService.createProcessInstanceBuilder();
        processInstanceBuilder.processDefinitionId(definition.getId());
        // 流程实例标题
        processInstanceBuilder.name(definition.getName());
        // 业务key
        processInstanceBuilder.businessKey(processInstanceRequest.getBusinessKey());
        processInstanceBuilder.variables(startVariables);
        //启动流程
        ProcessInstance instance = processInstanceBuilder.start();
        String processInstanceId = instance.getProcessInstanceId();
        List<Task> tasks = taskService.createTaskQuery().processInstanceId(processInstanceId).list();
        for (Task task : tasks) {
            // 约定：发起者节点为 __initiator__ ,则自动完成任务
            if (FlowableConstant.INITIATOR.equals(task.getTaskDefinitionKey())) {
                //获取执行按钮信息，这里可以考虑redis缓存，根据流程定义Key,获取默认执行按钮信息(这里考虑一个流程定义一个自动执行按钮)
                String defaultButtonKey = redisUtils.keyBuilder(Constants.MODULE, Constants.AUTO_TASK_BUTTON, definition.getKey());
                //先读取缓存数据
                Object obj = redisUtils.get(defaultButtonKey);
                String comment = CommentTypeEnum.CS.getName();
                Map buttonInfo;
                List taskButtons;
                if (null == obj) {
                    taskButtons = flowableTaskService.getTaskButtons(task.getId(), definition.getKey());
                    //存入缓存
                    redisUtils.set(defaultButtonKey, taskButtons, 600);
                } else {
                    taskButtons = (List) obj;
                }
                //自动执行，如果该任务存在按钮，添加按钮信息
                Map<String, Object> completeVariables = new HashMap<>();
                String buttonCode ="";
                if (!taskButtons.isEmpty()) {
                    buttonInfo = (HashMap) taskButtons.get(0);
                    completeVariables.put(FlowableConstant.PROCESS_INSTANCE_ACTION_INFO, buttonInfo);
                    completeVariables.put(FlowableConstant.PROCESS_INSTANCE_VCONDITION, buttonInfo.get("code"));
                    comment = buttonInfo.get("name") != null ? buttonInfo.get("name").toString() : comment;

                    buttonCode = buttonInfo.get("code").toString();
                }
                if (null != startVariables.get(FlowableConstant.NO_LISTENER)) {
                    if (Convert.toBool(startVariables.get(FlowableConstant.NO_LISTENER))) {
                        completeVariables.put(FlowableConstant.NO_LISTENER, flowableTaskService.hasListener(processDefinitionId, task.getTaskDefinitionKey(), processDefinitionKey));
                    }
                }
                Comment repcom ;
                //这里需要考虑自动执行作业执行时间问题
                if (null != processInstanceRequest.getOprDate()) {
                    repcom = flowableTaskService.addComment(task.getId(), processInstanceId, userId, comment, null, processInstanceRequest.getOprDate());
                } else {
                    repcom = flowableTaskService.addComment(task.getId(), processInstanceId, userId, comment, null);
                }

                if(multiFlag) {
                    //向工作流标记拓展表中插入数据--------202203190--strat--
                    //QueryWrapper<ActHiCommentCodeDO> queryWrapper = new QueryWrapper<>();
                    log.info("--向工作流标记拓展表中插入数据------------");
                    log.info("--buttonCode--" + buttonCode + "-----processDefinitionKey-----" + processDefinitionKey);
                    ActHiCommentCodeDO codeDo = new ActHiCommentCodeDO();
                    codeDo.setId(IdWorker.getIdStr());//id
                    codeDo.setCommentId(repcom.getId());//com表id
                    codeDo.setProcInstId(task.getProcessInstanceId());//流程ID
                    codeDo.setButtonCode("initial");//工作流按钮编码
                    codeDo.setButtonName(comment);
                    codeDo.setProcessDefKey("Maintenance1");//"Maintenance1"
                    actHiCommentCodeMapper.insert(codeDo);
                    //向工作流标记拓展表中插入数据--------202203190--strat--
                }
                String taskId = task.getId();
                SynchronizedLockObj<String> lockObj = SynchronizedLockObj.getInstance();
                lockObj.setObj(processInstanceId);

                synchronized (lockObj) {
                    if (ObjectUtils.isEmpty(task.getAssignee())) {
                        taskService.setAssignee(taskId, userId);
                    }
                    taskService.complete(taskId, completeVariables);
                }
                break;
            }
        }
        return processInstanceId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String processInstanceId, boolean cascade, String deleteReason) {
        HistoricProcessInstance historicProcessInstance = getHistoricProcessInstanceById(processInstanceId);
        //这里判断流程是否结束
        if (null != historicProcessInstance.getEndTime()) {
            historyService.deleteHistoricProcessInstance(historicProcessInstance.getId());
            return;
        }
        ExecutionEntity executionEntity = (ExecutionEntity) getProcessInstanceById(processInstanceId);
        if (CommonUtil.isNotEmptyAfterTrim(executionEntity.getSuperExecutionId())) {
            throw new FlowableException(FlowableConstant.SUB_PROCESS_MSG);
        }
        runtimeService.deleteProcessInstance(processInstanceId, deleteReason);
        if (cascade) {
            historyService.deleteHistoricProcessInstance(processInstanceId);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String deleteInit(String processInstanceId, boolean cascade, String deleteReason) {
        HistoricProcessInstance historicProcessInstance = getHistoricProcessInstanceById(processInstanceId);
        //这里判断流程是否结束
        if (null != historicProcessInstance.getEndTime()) {
            return i18nUtil.getMessage("ERR.wf.processInstanceService.processEndDelete");
        }

        ExecutionEntity executionEntity = (ExecutionEntity) getProcessInstanceById(processInstanceId);
        //这里目前只考虑顺序流
        List<String> activeActivityIds = runtimeService.getActiveActivityIds(processInstanceId);
        if (null != activeActivityIds && !activeActivityIds.isEmpty()) {
            //这里需要判断流程是否位于初始节点
            BpmnModel bpmnModel = repositoryService.getBpmnModel(executionEntity.getProcessDefinitionId());
            FlowNode flowNode = (FlowNode) bpmnModel.getFlowElement(activeActivityIds.get(0));
            SequenceFlow sequenceFlow = flowNode.getIncomingFlows().get(0);
            // 获取上一个节点的activityId
            String sourceRef = sequenceFlow.getSourceRef();
            if (CommonUtil.isNotEmptyAfterTrim(executionEntity.getSuperExecutionId())) {
                throw new FlowableException("This is a subprocess");
            }
            if (sourceRef.equals("__initiator__")) {
                runtimeService.deleteProcessInstance(processInstanceId, deleteReason);
                if (cascade) {
                    historyService.deleteHistoricProcessInstance(processInstanceId);
                }
                return i18nUtil.getMessage("MSG.wf.common.delSuccess");
            } else {
                return i18nUtil.getMessage("ERR.wf.processInstanceService.checkedProcessDelete");
            }
        }
        return "";
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String stopProcessInstance(String processInstanceId) {
        HistoricProcessInstance historicProcessInstance = getHistoricProcessInstanceById(processInstanceId);
        //这里判断流程是否结束
        if (null != historicProcessInstance.getEndTime()) {
            return i18nUtil.getMessage("MSG.wf.common.processInstanceService.endProcessStop");
        }

        ExecutionEntity executionEntity = (ExecutionEntity) getProcessInstanceById(processInstanceId);
        //这里目前只考虑顺序流
        List<String> activeActivityIds = runtimeService.getActiveActivityIds(processInstanceId);
        if (null != activeActivityIds && !activeActivityIds.isEmpty()) {
            //这里需要判断流程是否位于初始节点
            BpmnModel bpmnModel = repositoryService.getBpmnModel(executionEntity.getProcessDefinitionId());
            FlowNode flowNode = (FlowNode) bpmnModel.getFlowElement(activeActivityIds.get(0));
            SequenceFlow sequenceFlow = flowNode.getIncomingFlows().get(0);
            // 获取上一个节点的activityId
            String sourceRef = sequenceFlow.getSourceRef();
            if (CommonUtil.isNotEmptyAfterTrim(executionEntity.getSuperExecutionId())) {
                throw new FlowableException("This is a subprocess");
            }
            if (sourceRef.equals(Constants.INITNODENAME)) {
                stopProcessInstance(executionEntity.getProcessDefinitionId(), processInstanceId, sourceRef);
                return "";
            } else {
                return i18nUtil.getMessage("ERR.wf.processInstanceService.checkedProcessStop");
            }
        }
        return "";
    }

    /**
     * 删除跳转的历史节点信息
     *
     * @param disActivityId     跳转的节点id
     * @param processInstanceId 流程实例id
     */
    protected void deleteActivity(String disActivityId, String processInstanceId) {
        String tableName = managementService.getTableName(ActivityInstanceEntity.class);
        String sql = "select t.* from " + tableName + " t where t.PROC_INST_ID_=#{processInstanceId} and t.ACT_ID_ = #{disActivityId} " +
                " order by t.END_TIME_ ASC";
        List<ActivityInstance> disActivities = runtimeService.createNativeActivityInstanceQuery().sql(sql)
                .parameter("processInstanceId", processInstanceId)
                .parameter("disActivityId", disActivityId).list();
        //删除运行时和历史节点信息
        if (CollectionUtils.isNotEmpty(disActivities)) {
            ActivityInstance activityInstance = disActivities.get(0);
            sql = "select t.* from " + tableName + " t where t.PROC_INST_ID_=#{processInstanceId} and (t.END_TIME_ >= #{endTime} or t.END_TIME_ is null)";
            List<ActivityInstance> datas = runtimeService.createNativeActivityInstanceQuery().sql(sql).parameter("processInstanceId", processInstanceId)
                    .parameter("endTime", activityInstance.getEndTime()).list();
            List<String> runActivityIds = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(datas)) {
                datas.forEach(ai -> runActivityIds.add(ai.getId()));
                runFlowableActinstDao.deleteRunActinstsByIds(runActivityIds);
                hisFlowableActinstDao.deleteHisActinstsByIds(runActivityIds);
            }
        }
    }


    /**
     * 删除迁移数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteMigrateData(List<String> processInstanceIds) {
        List<String> byteIds = new ArrayList<>();
        //删除运行时数据
        if (!processInstanceIds.isEmpty()) {
            runFlowableActinstDao.deleteRunActByInst(processInstanceIds);
            runFlowableActinstDao.deleteRunTimerJob(processInstanceIds);
            runFlowableActinstDao.deleteRunRunJob(processInstanceIds);
            runFlowableActinstDao.deleteRunIdentityLink(processInstanceIds);
            List<Map> runBytes = runFlowableActinstDao.selectRunGeByte(processInstanceIds);
            runFlowableActinstDao.deleteRunVariable(processInstanceIds);
            List<String> taskLinkIds = runFlowableActinstDao.selectRunTaskLink(processInstanceIds);
            if (!taskLinkIds.isEmpty()) {
                runFlowableActinstDao.deleteRunTaskLink(taskLinkIds);
            }
            runFlowableActinstDao.deleteRunTask(processInstanceIds);
            runFlowableActinstDao.deleteRunExecution(processInstanceIds);

            for (Map runByte : runBytes) {
                byteIds.add(runByte.get("ID_").toString());
            }
            //删除历史数据
            List<Map> hiBytes = hisFlowableActinstDao.selectHiGeByte(processInstanceIds);
            for (Map hiByte : hiBytes) {
                byteIds.add(hiByte.get("ID_").toString());
            }
            hisFlowableActinstDao.deleteHiIdLink(processInstanceIds);
            hisFlowableActinstDao.deleteHiActinst(processInstanceIds);
            hisFlowableActinstDao.deleteHiProcInst(processInstanceIds);
            hisFlowableActinstDao.deleteHiTaskInst(processInstanceIds);
            hisFlowableActinstDao.deleteHiComment(processInstanceIds);
            hisFlowableActinstDao.deleteHiVariable(processInstanceIds);

            if (!byteIds.isEmpty()) {
                runFlowableActinstDao.deleteGeByte(byteIds);
            }
        }
    }

    /**
     * 终止流程实例
     *
     * @param processDefinitionId
     * @param processInstanceId
     * @param stopTaskID
     */
    public void stopProcessInstance(String processDefinitionId, String processInstanceId, String stopTaskID) {
        BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinitionId);
        if (null != bpmnModel) {
            Process process = bpmnModel.getMainProcess();
            List<EndEvent> endNodes = process.findFlowElementsOfType(EndEvent.class, false);
            if (null != endNodes && !endNodes.isEmpty()) {
                String endId = endNodes.get(0).getId();
                runtimeService.setVariable(processInstanceId, "StopTaskID", stopTaskID);
                List<Execution> executions = runtimeService.createExecutionQuery()
                        .parentId(processInstanceId).list();
                List<String> executionIds = new ArrayList<>();
                executions.forEach(execution -> executionIds.add(execution.getId()));
                deleteActivity(stopTaskID, processInstanceId);
                List<Execution> executions1 = runtimeService.createExecutionQuery().parentId(processInstanceId).list();
                List<String> executionIds1 = new ArrayList<>();
                executions1.forEach(execution -> executionIds1.add(execution.getId()));

                runtimeService.createChangeActivityStateBuilder()
                        .moveExecutionsToSingleActivityId(executionIds1, endId)
                        .changeState();
            }
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void activate(String processInstanceId) {
        ProcessInstance processInstance = getProcessInstanceById(processInstanceId);
        if (!processInstance.isSuspended()) {
            throw new FlowableException("Process instance is not suspended with id " + processInstanceId);
        }
        runtimeService.activateProcessInstanceById(processInstance.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void suspend(String processInstanceId) {
        ProcessInstance processInstance = getProcessInstanceById(processInstanceId);
        if (processInstance.isSuspended()) {
            throw new FlowableException("Process instance is already suspended with id {0}" + processInstanceId);
        }
        runtimeService.suspendProcessInstanceById(processInstance.getId());
    }

    @SuppressWarnings("squid:RedundantThrowsDeclarationCheck")
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String startBizWf(String bizKey, String wfCode, Map<String, Object> params, boolean isProceesDefKey, String oprator, Date oprDate) {
        //这里添加工作流启动
        String processInstanceID = "";
        String wfModelKey = "";
        String busiTableName = "";
        String stateFieldName = "";
        WfConf wfConf = null;
        ProcessInstanceRequest processInstanceRequest = new ProcessInstanceRequest();
        processInstanceRequest.setOprator(oprator);
        processInstanceRequest.setOprDate(oprDate);
        if (!isProceesDefKey) {
            wfConf = wfConfService.getConditionWfConf(wfCode);
        } else {
            wfConf = wfConfService.getWfConfByVmodel(wfCode);
            wfModelKey = wfCode;
        }
        if (null != wfConf) {
            wfModelKey = wfConf.getVmodel(); //工作流模板key
            busiTableName = wfConf.getBusiTableName();
            stateFieldName = wfConf.getStateFieldName();
            if (null != params) {
                params.put(FlowableConstant.PROCESS_BUSSINESS_TABLE, busiTableName);
                params.put(FlowableConstant.PROCESS_STATE_FIELDNAME, stateFieldName);
                params.put(FlowableConstant.PROCESS_KEY_FIELDNAME, wfConf.getBusiKeyFieldName());
            }
        }
        //这里添加工作流启动参数
        //业务主键,唯一且有业务含义：可以是任意拼接的提交单据的描述
        processInstanceRequest.setBusinessKey(bizKey);
        //流程定义ID
        processInstanceRequest.setProcessDefinitionKey(wfModelKey);//工作流ID
        //如果有启动参数，在这里设置
        processInstanceRequest.setValues(params);
        if (null != params && params.containsKey("skip")) {
            processInstanceRequest.getValues().put("skip", params.get("skip"));
            processInstanceRequest.getValues().put(FlowableConstant.FLOWABLE_SKIP_EXPRESSION_ENABLED, false);
        }
        try {
            processInstanceID = start(processInstanceRequest);
        } catch (Exception e) {
            String msg = e.getMessage();
            String businessMsg = e.getMessage();
            if (msg.contains(Constants.NO_MODEL_DEF)) {
                String param = msg.replace(Constants.NO_MODEL_DEF, "");
                String message = i18nUtil.getMessage("MSG.wf.processInstanceService.definitionTemplateNotFound");
                businessMsg = message + param;
            }
            log.info("---error--"+"工作流启动失败:bizKey=" + bizKey + " wfCode:" + wfCode, e);
            throw new FlowableTaskException(businessMsg);
        }
        return processInstanceID;
    }

    @SuppressWarnings("squid:RedundantThrowsDeclarationCheck")
    @Transactional(rollbackFor = Exception.class)
    public List<String> startBatchBizWf(List<Map<String, Object>> requestParams) {
        List failBusinessKeys = new ArrayList();
        for (Map businMap : requestParams) {
            String businessKey = businMap.get("businessKey").toString();
            try {
                String wfCode = businMap.get("wfCode").toString();
                Map<String, Object> params = (Map<String, Object>) businMap.get("params");
                params.put("skip", false);
                startBizWf(businessKey
                        , wfCode, params, false, LoginUtil.getOperatorId(), null);
            } catch (Exception e) {
                failBusinessKeys.add(businessKey);
                String message = i18nUtil.getMessage("ERR.wf.ProcessInstanceController.processStartupFailInfo");
                log.info("---error--"+businessKey + message, e);
            }
        }
        return failBusinessKeys;
    }

    @Override
    public List handleProcessInstanceTaskButtons(Object processInstance, String transCode) {
        Map map = JSONObject.parseObject(JSONObject.toJSONString(processInstance), Map.class);
        Object processDefinitionKey = map.get("processDefinitionKey");
        List returnList = new ArrayList();
        // 查询待办节点
        TaskQuery query = taskService.createTaskQuery();
        query.processInstanceId(map.get("id").toString());
        String userId = LoginUtil.getOperatorId();
        if (!BootAppUtil.isNullOrEmpty(processDefinitionKey)) {
            boolean flag = wfConfService.getProcessAuditAuthCheck(processDefinitionKey.toString());
            if (flag) {
                query.or().taskCandidateOrAssigned(userId).taskOwner(userId).endOr();
            }
        }
        List<TaskResponse> list = getTobeDoneList(query, TaskTodoListWrapper.class);
        // 判断任务节点是否存在
        if (!list.isEmpty()) {
            for (TaskResponse taskEntity : list) {
                List buttonList = getButtonsByTask(taskEntity.getProcessDefinitionId(), taskEntity.getTaskDefinitionKey(), processDefinitionKey.toString(), transCode);
                if (CollectionUtils.isNotEmpty(buttonList)) {
                    returnList.addAll(buttonList);
                }
            }
            //对un逆向操作过滤
            List<ProcessCommonButtonsDO> buttons = ((List<ProcessCommonButtonsDO>) returnList).stream().filter(s -> s.getCode().trim().startsWith("un_"))
                    .collect(Collectors.toList());
            if (!buttons.isEmpty()) {
                for (TaskResponse taskEntity : list) {
                    List<ProcessCommonButtonsDO> btns = buttons.stream().filter(s -> ("un_" + taskEntity.getTaskDefinitionKey()).equals(s.getCode()))
                            .collect(Collectors.toList());
                    for (ProcessCommonButtonsDO btn : btns) {
                        btn.setVisable(0);
                    }
                }
            }
            //对互斥显示过滤
            List btns = ((List<ProcessCommonButtonsDO>) returnList).stream().filter(s -> s.getMutex() != null && s.getMutex().equals(1)).collect(Collectors.toList());
            if (!btns.isEmpty()) {
                Set<ProcessCommonButtonsDO> playerSet = new TreeSet<>(Comparator.comparing(o -> (o.getMutex() + "" + o.getCode() + "" + o.getVisable())));
                playerSet.addAll(returnList);
                return new ArrayList<>(playerSet);
            }
        }
        return returnList;
    }

    private List getButtonsByTask(String processDefinitionId, String taskDefinitionKey, String processDefinitionKey, String transCode) {
        List<ProcessCommonButtonsDO> returnList = new ArrayList();
        List<UserTask> flowElements = null;
        // 先从redis获取任务节点
        String key = redisUtils.keyBuilder("wf", "processDefinition", processDefinitionId.split(":")[0]);
        Object tasks = redisUtils.get(key);
        if (!BootAppUtil.isNullOrEmpty(tasks)) {
            flowElements = JSON.parseObject(tasks.toString(), new TypeReference<List<UserTask>>() {
            });
        } else {
            //通过流程定义ID获取流程模板
            BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinitionId);
            if (null != bpmnModel) {
                List<Process> processList = bpmnModel.getProcesses();
                for (Process process : processList) {
                    flowElements = process.findFlowElementsOfType(UserTask.class);
                }
            }
            processDefinitionService.saveRedisProcessDefinitionTask(processDefinitionId);
        }
        if (null != flowElements && !flowElements.isEmpty()) {
            for (UserTask userTask : flowElements) {
                // 获取当前待办任务节点的元素
                if (userTask.getId().equals(taskDefinitionKey)) {
                    // 获取用户节点按钮
                    List<ProcessCommonButtonsDO> buttonsList = getUserTaskButtons(processDefinitionKey, userTask, transCode, 1, 0);
                    if (!buttonsList.isEmpty()) {
                        //这里添加任务与按钮的互斥规则
                        returnList.addAll(buttonsList);
                    }
                } else {
                    // 获取用户节点按钮
                    List<ProcessCommonButtonsDO> buttonsList = getUserTaskButtons(processDefinitionKey, userTask, transCode, 0, 0);
                    if (!buttonsList.isEmpty()) {
                        //这里添加任务与按钮的互斥规则
                        returnList.addAll(buttonsList);
                    }
                }
            }
        }
        return returnList;
    }

    @Override
    public JsonResultVo getProcessTaskOutgoing(String processDefKey, String transCode) {
        JsonResultVo resultObj = new JsonResultVo();
        Map buttonMap = new HashMap();
        List<ProcessDefinitionResponse> processList = getProcessList(processDefKey);
        if (!processList.isEmpty()) {
            //通过流程定义ID获取流程模板
            BpmnModel bpmnModel = repositoryService.getBpmnModel(processList.get(0).getId());
            if (null != bpmnModel) {
                // 获取模板对应的流程
                List<Process> processs = bpmnModel.getProcesses();
                for (Process process : processs) {
                    // 获取人工任务节点
                    Collection<UserTask> flowElements = process.findFlowElementsOfType(UserTask.class);
                    // 循环人工任务
                    for (UserTask userTask : flowElements) {
                        // 获取用户节点按钮
                        List buttonsList = getUserTaskButtons(processDefKey, userTask, transCode, 1, 1);
                        if (!buttonsList.isEmpty()) {
                            buttonMap.put(userTask.getId(), buttonsList.stream().sorted(Comparator.comparing(ProcessCommonButtonsDO::getCode)).collect(Collectors.toList()));
                        }
                    }
                }
            }
        }
        resultObj.setData(buttonMap);
        return resultObj;
    }

    /**
     * @param auth 是否判断节点权限，1 按照 0 不按照
     * @description 查询入线状态
     * <AUTHOR>
     * @date 2020/11/10 13:07
     */
    @Override
    public JsonResultVo getProcessTaskIncoming(String processDefKey, String transCode, Integer auth) {
        JsonResultVo resultObj = new JsonResultVo();
        Map userIncomingMap = new HashMap();
        List<ProcessDefinitionResponse> processList = getProcessList(processDefKey);
        if (!processList.isEmpty()) {
            //通过流程定义ID获取流程模板
            BpmnModel bpmnModel = repositoryService.getBpmnModel(processList.get(0).getId());
            if (null != bpmnModel) {
                // 获取模板对应的流程
                List<Process> processs = bpmnModel.getProcesses();
                for (Process process : processs) {
                    // 获取人工任务节点
                    Collection<UserTask> flowElements = process.findFlowElementsOfType(UserTask.class);
                    // 获取网关节点
                    Collection<ExclusiveGateway> gateWayflowElements = process.findFlowElementsOfType(ExclusiveGateway.class);
                    // 取网关和人工任务所有的出线
                    // 取所有人工任务节点和网关节点的出线，供有权限的任务节点入线去对应
                    List<UserTask> userTasks = new ArrayList<>();
                    List<Map> outList = new ArrayList<>();
                    for (UserTask userTask : flowElements) {
                        if (!userTask.getOutgoingFlows().isEmpty()) {
                            for (SequenceFlow outgoingFlow : userTask.getOutgoingFlows()) {
                                Map userTaskOutgoingMap = new HashMap();
                                // 来源于哪个任务节点
                                userTaskOutgoingMap.put("sourceRef", userTask.getId());
                                // outgoing的ID，用于和有权限节点做对比
                                userTaskOutgoingMap.put(FlowableConstant.OUT_GOING, outgoingFlow.getId());
                                // 类型，人工任务出线是1
                                userTaskOutgoingMap.put("type", 1);
                                outList.add(userTaskOutgoingMap);
                            }
                        }
                        userTasks.add(userTask);
                    }
                    for (ExclusiveGateway exclusiveGateway : gateWayflowElements) {
                        if (!exclusiveGateway.getOutgoingFlows().isEmpty()) {
                            for (SequenceFlow outgoingFlow : exclusiveGateway.getOutgoingFlows()) {
                                Map gatewayOutgoingMap = new HashMap();
                                // 来源于哪个任务节点
                                gatewayOutgoingMap.put("sourceRef", exclusiveGateway.getIncomingFlows().get(0).getSourceRef());
                                // outgoing的ID，用于和有权限节点做对比
                                gatewayOutgoingMap.put(FlowableConstant.OUT_GOING, outgoingFlow.getId());
                                // 类型，网关出线是2
                                gatewayOutgoingMap.put("type", 2);
                                outList.add(gatewayOutgoingMap);
                            }
                        }
                    }
                    // 循环上面赋值过的有权限的人工任务节点，去incoming，对应的outgoing集合，去对应的按钮
                    for (UserTask userTask : userTasks) {
                        // 获取用户节点按钮,如果有数据说明存在权限，继续查入线，根据auth，判断是否要走权限判断
                        if (auth == 1) {
                            List buttonsList = getUserTaskButtons(processDefKey, userTask, transCode, 1, 1);
                            if (buttonsList.isEmpty()) {
                                continue;
                            }
                        }
                        if (!userTask.getIncomingFlows().isEmpty()) {
                            List incomings = new ArrayList();
                            for (SequenceFlow incomingFlow : userTask.getIncomingFlows()) {
                                // 查询上面封装的出线map和节点进线做对比，取出对应的按钮
                                for (Map map : outList) {
                                    String code = map.get(FlowableConstant.OUT_GOING).toString();
                                    String[] codes = code.split("_");
                                    if (incomingFlow.getId().equals(code) && codes.length > 2) {
                                        //定义查询构造器
                                        QmQueryWrapper<ProcessCommonButtonsDO> queryWrapper = new QmQueryWrapper<>();
                                        queryWrapper.eq("processDefKey", processDefKey);
                                        //	代码
                                        StringBuilder buttonCode = new StringBuilder();
                                        for (int i = 0; i < codes.length; i++) {
                                            if (i > 1) {
                                                if (i != (codes.length - 1)) {
                                                    buttonCode.append(codes[i] + "_");
                                                } else {
                                                    buttonCode.append(codes[i]);
                                                }
                                            }
                                        }
                                        queryWrapper.eq("code", buttonCode.toString());
                                        queryWrapper.eq("isState", 1);

                                        if(multiFlag) {
                                            log.info("-----getProcessTaskIncoming---开启多语言查询-------");
                                            ProcessCommonButtonsDO param = new ProcessCommonButtonsDO();
                                            param.setCode(buttonCode.toString());
                                            param.setProcessDefKey(processDefKey);
                                            param.setIsState(1);
                                            //增加语言标识----20220414--
                                            String LanguageCode = BootAppUtil.getLoginKey().getLanguageCode();
                                            if (!BootAppUtil.isNullOrEmpty(LanguageCode)) {
                                                param.setVlanguagecode(LanguageCode);
                                            }
                                            List<ProcessCommonButtonsDO> processCommonButtons = baseMapper.selectButtonLangList(param);
                                            if(!processCommonButtons.isEmpty()){
                                                ProcessCommonButtonsDO buttonDO = processCommonButtons.get(0);
                                                incomings.add(buttonDO);
                                            }

                                        }else{
                                            log.info("----getProcessTaskIncoming----关闭多语言查询-------");
                                            QmPage<ProcessCommonButtonsDO> commonButtons = processCommonButtonsService.table(queryWrapper, new ProcessCommonButtonsDTO());
                                            if (!commonButtons.getItems().isEmpty()) {
                                                ProcessCommonButtonsDO buttonDO = commonButtons.getItems().get(0);
                                                incomings.add(buttonDO);
                                            }
                                        }
                                    }
                                }
                            }
                            if (!incomings.isEmpty()) {
                                userIncomingMap.put(userTask.getId(), incomings.stream().sorted(Comparator.comparing(ProcessCommonButtonsDO::getCode)).collect(Collectors.toList()));
                            }
                        }
                    }
                }
            }
        }
        resultObj.setData(userIncomingMap);
        return resultObj;
    }

    @Override
    public Map getTaskButtonsByTaskId(String taskId) {
        Task task = flowableTaskService.getTaskNotNull(taskId);
        HistoricProcessInstance historicProcessInstance = getHistoricProcessInstanceById(task.getProcessInstanceId());
        ProcessDefinition processDefinition = processDefinitionService.getProcessDefinitionById(task.getProcessDefinitionId());
        List list = getButtonsByTask(task.getProcessDefinitionId(), task.getTaskDefinitionKey(), processDefinition.getKey(), null);
        Map map = new HashMap();
        if (null != historicProcessInstance.getBusinessKey()) {
            map.put(historicProcessInstance.getBusinessKey(), list);
        }
        return map;
    }

    @Override
    public List getProcessButtons(String processDefKey, String transCode) {
        List list = new ArrayList();
        List<ProcessDefinitionResponse> processList = getProcessList(processDefKey);
        if (!processList.isEmpty()) {
            //通过流程定义ID获取流程模板
            BpmnModel bpmnModel = repositoryService.getBpmnModel(processList.get(0).getId());
            if (null != bpmnModel) {
                // 获取模板对应的流程
                List<Process> processs = bpmnModel.getProcesses();
                for (Process process : processs) {
                    // 获取人工任务节点
                    Collection<UserTask> flowElements = process.findFlowElementsOfType(UserTask.class);
                    // 循环人工任务
                    for (UserTask userTask : flowElements) {
                        // 获取用户节点按钮
                        List buttonsList = getUserTaskButtons(processDefKey, userTask, transCode, 1, 0);
                        if (!buttonsList.isEmpty()) {
                            list.addAll(buttonsList);
                        }
                    }
                }
            }
        }
        return list;
    }

    /**
     * @param isState 是否作为状态展示1是0否
     * @param type    控制这个任务节点的按钮是否可见 1可见 0不可见
     * @description 查询人工任务节点按钮
     * <AUTHOR>
     * @date 2020/8/21 15:57
     */
    private List getUserTaskButtons(Object processDefinitionKey, UserTask userTask, String transCode, Integer type, Integer isState) {
        List list = new ArrayList();
        // 查询状态时，不使用redis
        if (isState == 0 && !BootAppUtil.isNullOrEmpty(transCode)) {
            getButtonsByRedis(list, processDefinitionKey.toString(), userTask, transCode, type, isState);
            if (!list.isEmpty()) {
                return list;
            }
        }
        List<ExtensionElement> elements = userTask.getExtensionElements().get("button");
        if (!BootAppUtil.isNullOrEmpty(elements)) {
            for (ExtensionElement button : elements) {
                String code = button.getAttributes().get("code").get(0).getValue();
                //定义查询构造器
                QmQueryWrapper<ProcessCommonButtonsDO> queryWrapper = new QmQueryWrapper<>();
                //拼装实体属性查询条件
                //流程定义Key
                queryWrapper.eq("processDefKey", processDefinitionKey);
                //	代码
                queryWrapper.eq("code", code);

                //	是否作为状态展示1是0否
                if (isState == 1) {
                    queryWrapper.eq("isState", isState);
                }
                List<ProcessCommonButtonsDO> processCommonButtons = new ArrayList<>();
                if(multiFlag) {
                    log.info("----getUserTaskButtons----开启多语言查询-------");
                    ProcessCommonButtonsDO param = new ProcessCommonButtonsDO();
                    param.setCode(code);
                    param.setProcessDefKey(processDefinitionKey.toString());
                    if (isState == 1) {
                        param.setIsState(isState);
                    }
                    //增加语言标识----20220311--
                    String LanguageCode = BootAppUtil.getLoginKey().getLanguageCode();
                    if (!BootAppUtil.isNullOrEmpty(LanguageCode)) {
                        param.setVlanguagecode(LanguageCode);
                    }
                    processCommonButtons = baseMapper.selectButtonLangList(param);
                }else{
                    log.info("---getUserTaskButtons-----关闭多语言查询-------");
                    processCommonButtons = processCommonButtonsService.list(queryWrapper);
                }

                if (!processCommonButtons.isEmpty()) {
                    ProcessCommonButtonsDO buttonObj = processCommonButtons.get(0);
                    buttonObj.setVisable(type);
                    buttonObj.setTaskId(userTask.getId());
                    ProcessCommonButtonsDO buttonDO = new ProcessCommonButtonsDO();
                    BeanUtils.copyProperties(buttonObj, buttonDO);
                    if (BootAppUtil.isNullOrEmpty(transCode)) {
                        list.add(buttonDO);
                    } else {
                        // 判断权限
                        boolean tranFlag = taskTranAuthService.judgeTaskAuth(userTask.getId(), transCode, processDefinitionKey.toString(), buttonDO.getCode());
                        if (tranFlag) {
                            boolean userFlag = taskAuthService.judgeTaskAuthForUser(userTask.getId(), LoginUtil.getOperatorId(), processDefinitionKey.toString(), buttonDO.getCode(), transCode);
                            if (userFlag) {
                                list.add(buttonDO);
                            }
                        }
                    }
                }
            }
        }
        // 不是作为状态查询，正常查按钮，如果没有才走redis
        if (CollectionUtils.isNotEmpty(list) && isState == 0) {
            Map<String, List<RoleVO>> roleMaps = processInstanceService.cacheCurrentAllUserRole();
            taskTranAuthService.createRedisData(processDefinitionKey.toString(), roleMaps);
        }
        return list;
    }

    private void getButtonsByRedis(List list, String processDefKey, UserTask userTask, String transCode, Integer type, Integer isState) {
        Object buttons = null;
        String key = redisUtils.keyBuilder("wf", processDefKey, userTask.getId(), transCode, LoginUtil.getOperatorId().toUpperCase());
        buttons = redisUtils.get(key);
        if (BootAppUtil.isNullOrEmpty(buttons)) {
            String newKey = redisUtils.keyBuilder("wf", processDefKey, userTask.getId(), transCode);
            buttons = redisUtils.get(newKey);
        }
        if (!BootAppUtil.isNullOrEmpty(buttons)) {
            List<ProcessCommonButtonsDO> buttonLists = JSON.parseObject(buttons.toString(), new TypeReference<List<ProcessCommonButtonsDO>>() {
            });
            if (!buttonLists.isEmpty()) {
                buttonLists.forEach(b -> {
                    ProcessCommonButtonsDO button = b;
                    button.setTaskId(userTask.getId());
                    button.setVisable(type);
                    if (isState == 1 && button.getIsState().equals(isState)) {
                        list.add(button);
                    }
                    if (isState == 0) {
                        list.add(button);
                    }
                });
            }
        }
    }

    /**
     * @description 查询待办任务节点
     * <AUTHOR>
     * @date 2020/8/22 10:21
     */
    protected List<TaskResponse> getTobeDoneList(Query query, Class<? extends IListWrapper> listWrapperClass) {
        List<TaskResponse> list = query.list();
        if (null != listWrapperClass) {
            IListWrapper listWrapper = SpringContextUtils.getBean(listWrapperClass);
            list = listWrapper.execute(list);
        }
        return list;
    }

    /**
     * @description 根据流程定义KEY获取流程定义
     * <AUTHOR>
     * @date 2020/8/22 10:38
     */
    private List getProcessList(String processDefKey) {
        // 查询对象
        ProcessDefinitionQuery query = repositoryService.createProcessDefinitionQuery();
        // 最后版本
        query.latestVersion();
        // 流程定义KEY
        query.processDefinitionKey(processDefKey);
        Class<? extends IListWrapper> listWrapperClass = ProcDefListWrapper.class;
        List list = query.list();
        if (null != listWrapperClass) {
            IListWrapper listWrapper = SpringContextUtils.getBean(listWrapperClass);
            list = listWrapper.execute(list);
        }
        return list;
    }



    @Override
    public String getButtonLangText(String commentId) {
        //1、通过comid 查询 工作流id 与code
        QueryWrapper<ActHiCommentCodeDO> queryWrapper = new QueryWrapper<>();
        //commentId
        queryWrapper.eq("commentId", commentId);
        /*//流程定义Key
        queryWrapper.eq("processDefKey", processDefinitionKey);
        //	代码
        queryWrapper.eq("code", code);*/
        ActHiCommentCodeDO codeDo = actHiCommentCodeMapper.selectOne(queryWrapper);

        if(BootAppUtil.isNullOrEmpty(codeDo)){
            log.info("--没有在拓展表，act_hi_comment_code，查询到数据！！--");
            return "";
        }else{
            //2、从数据库中获取button信息
            String Vtext =processCommonButtonsService.findButtonLangInfo(codeDo.getButtonCode(),codeDo.getProcessDefKey());

            return Vtext;
        }


    }
}
