package com.qm.ep.wf.util;

import com.qm.tds.api.util.SpringContextHolder;
import com.qm.tds.util.I18nUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> @date
 */
@Data
@ApiModel(value = "返回说明", description = "返回说明")
public class Result implements Serializable {
    @ApiModelProperty("串行版 uid")
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("服务器错误")
    private static Integer serverError = 500;
    @ApiModelProperty("数据s ok")
    private static Integer sOK = 200;

    @ApiModelProperty(value = "返回状态码；200:成功")
    private Integer code = 0;
    @ApiModelProperty(value = "返回信息")
    private String msg;

    @ApiModelProperty(value = "返回数据")
    private transient Object data;

    @ApiModelProperty("数据i18n 实用程序")
    private static I18nUtil i18nUtil;

    private static I18nUtil getI18nUtil() {
        if (i18nUtil == null) {
            i18nUtil = SpringContextUtils.getBean(I18nUtil.class);
        }
        return i18nUtil;
    }

    public Result() {
        this.msg = getI18nUtil().getMessage("MSG.wf.common.operateSuccess");
    }

    public Result(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Result(String msg) {
        this.code = sOK;
        this.msg = msg;
    }

    public Result(Integer code, String msg, Object data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    public static Result error(String msg) {
        return error(serverError, msg);
    }

    public static Result error(int code, String msg) {
        if (msg == null || msg.length() == 0) {
            msg = getI18nUtil().getMessage("MSG.wf.common.tradeFail");
        }
        return new Result(code, msg);
    }

    public static Result error(int code, String msg, Object data) {
        if (msg == null || msg.length() == 0) {
            msg = getI18nUtil().getMessage("MSG.wf.common.tradeFail");
        }
        return new Result(code, msg, data);
    }

    public static Result ok() {
        Result r = new Result();
        r.setCode(sOK);
        return r;
    }

    public static Result ok(String msg) {
        return new Result(msg);
    }

    public static Result ok(Object data) {
        Result r = new Result();
        r.setCode(sOK);
        r.setData(data);
        return r;
    }

    public static Result ok(String msg, Object data) {
        Result r = new Result();
        r.setCode(sOK);
        if (msg != null && msg.length() != 0) {
            r.setMsg(msg);
        }
        r.setData(data);
        return r;
    }
}
