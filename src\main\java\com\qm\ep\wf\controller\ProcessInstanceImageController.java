package com.qm.ep.wf.controller;

import com.qm.ep.wf.common.BaseFlowableController;
import com.qm.ep.wf.config.CustomProcessDiagramGenerator;
import com.qm.ep.wf.domain.vo.CustomProcessDiagramGeneratorVO;
import com.qm.ep.wf.util.LoginUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.io.IOUtils;
import org.flowable.bpmn.constants.BpmnXMLConstants;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.common.engine.api.FlowableException;
import org.flowable.engine.ProcessEngineConfiguration;
import org.flowable.engine.history.HistoricActivityInstance;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.repository.ProcessDefinition;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR> @date
 */
@Api(tags = {"流程实例映像控制器"})
@RestController
public class ProcessInstanceImageController extends BaseFlowableController {
    @Autowired
    private ProcessEngineConfiguration processEngineConfiguration;

    @ApiOperation(value = "实例镜像", notes = "[author:10027705]", httpMethod = "GET")
    @GetMapping(value = "/flowable/processInstanceImage")
    public ResponseEntity<byte[]> image(@RequestParam String processInstanceId) {
        HistoricProcessInstance processInstance = permissionService
                .validateReadPermissionOnProcessInstance(LoginUtil.getOperatorId(), processInstanceId);
        ProcessDefinition pde = repositoryService.getProcessDefinition(processInstance.getProcessDefinitionId());
        if (pde == null || !pde.hasGraphicalNotation()) {
            throw new FlowableException(
                    messageFormat("Process instance image is not found with id {0}", processInstanceId));
        }
        List<String> highLightedFlows = new ArrayList<>();
        List<String> highLightedActivities = new ArrayList<>();
        List<HistoricActivityInstance> allHistoricActivityIntances = historyService
                .createHistoricActivityInstanceQuery().processInstanceId(processInstanceId).list();
        allHistoricActivityIntances.forEach(historicActivityInstance -> {
            if (BpmnXMLConstants.ELEMENT_SEQUENCE_FLOW.equals(historicActivityInstance.getActivityType())) {
                highLightedFlows.add(historicActivityInstance.getActivityId());
            } else {
                highLightedActivities.add(historicActivityInstance.getActivityId());
            }
        });

        List<String> runningActivitiIdList = null;
        // 流程已结束
        if (processInstance.getEndTime() != null) {
            runningActivitiIdList = Arrays.asList();
        } else {
            runningActivitiIdList = runtimeService.getActiveActivityIds(processInstanceId);
        }

        BpmnModel bpmnModel = repositoryService.getBpmnModel(pde.getId());
        CustomProcessDiagramGenerator diagramGenerator = (CustomProcessDiagramGenerator) processEngineConfiguration
                .getProcessDiagramGenerator();

        CustomProcessDiagramGeneratorVO vo = new CustomProcessDiagramGeneratorVO();
        vo.setBpmnModel(bpmnModel);
        vo.setImageType("png");
        vo.setHighLightedActivities(highLightedActivities);
        /*vo.setHighLightedFlows(runningActivitiIdList);
        vo.setRunningActivitiIdList(highLightedFlows);*/
        /*由于赋值时两个参数互相赋对方的值，导致流程模块中显示框颜色都为高亮。
         * 现改为正确赋值，流程图中当前步骤为蓝色
         * 2021-12-02
         */
        vo.setHighLightedFlows(highLightedFlows);
        vo.setRunningActivitiIdList(runningActivitiIdList);
        vo.setActivityFontName(processEngineConfiguration.getAnnotationFontName());
        vo.setLabelFontName(processEngineConfiguration.getLabelFontName());
        vo.setAnnotationFontName(processEngineConfiguration.getAnnotationFontName());
        vo.setCustomClassLoader(processEngineConfiguration.getClassLoader());
        vo.setScaleFactor(1.0);
        vo.setDrawSequenceFlowNameWithNoLabelDi(true);

        InputStream resource = diagramGenerator.generateCustomDiagram(vo);
        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.setContentType(MediaType.IMAGE_PNG);
        try {
            return new ResponseEntity<>(IOUtils.toByteArray(resource), responseHeaders, HttpStatus.OK);
        } catch (Exception e) {
            throw new FlowableException("Process instance image read error", e);
        }
    }
}
