package com.qm.ep.wf.util;


import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR> @date 2020年3月22日
 */
public class CommonUtil {

    /**
     * 这个类不能实例化
     */
    private CommonUtil() {
    }


    public static String trimToEmptyStr(String str) {
        return StringUtils.trimToEmpty(str);
    }

    public static boolean isEmptyObject(Object object) {
        return ObjectUtils.isEmpty(object);
    }

    public static boolean isNotEmptyObject(Object object) {
        return !ObjectUtils.isEmpty(object);
    }

    public static boolean isEmptyStr(String str) {
        return StringUtils.isEmpty(str);
    }


    public static boolean isNotEmptyStr(String str) {
        return !StringUtils.isEmpty(str);
    }

    public static boolean isEmptyAfterTrim(String str) {
        return StringUtils.isEmpty(StringUtils.trimToEmpty(str));
    }


    public static boolean isNotEmptyAfterTrim(String str) {
        return !isEmptyAfterTrim(str);
    }

    public static <T> T isEmptyDefault(T source, T df) {
        return isNotEmptyObject(source) ? source : df;
    }

    /**
     * 验证[某字符串]是否存在于逗号分隔的字符串中
     *
     * @param str       【abc,123,www】
     * @param substr    【123】
     * @param sepatator 【,】
     * @return
     */
    public static boolean isExist(String str, String substr, String sepatator) {
        if (str == null || str.trim().length() == 0) {
            return false;
        }
        if (substr == null || substr.trim().length() == 0) {
            return false;
        }
        String[] strArr = str.split(sepatator);
        int size = strArr.length;
        for (int i = 0; i < size; i++) {
            if (strArr[i].equals(substr)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 对String型的id号串,拆分成数组 【abc,123,www】将拆分成str数组
     *
     * @param srcIds
     * @param splitChar
     * @return
     */
    public static String[] getIds(String srcIds, String splitChar) {
        String[] ids = null;
        // 第一个字符是splitchar
        boolean spBegin = false;

        if (srcIds == null || srcIds.length() == 0) {
            return new String[]{};
        }
        if (srcIds.indexOf(splitChar) == 0) {
            spBegin = true;
        }

        String[] strSplitIds = srcIds.trim().split(splitChar);
        int len = strSplitIds.length;
        if (spBegin) {
            if (len > 1) {
                ids = new String[len - 1];
                for (int i = 1; i < len; i++) {
                    ids[i - 1] = strSplitIds[i];
                }
            } else {
                return new String[]{};
            }
        } else {
            ids = new String[len];
            for (int i = 0; i < len; i++) {
                ids[i] = strSplitIds[i];
            }
        }
        return ids;
    }
}
