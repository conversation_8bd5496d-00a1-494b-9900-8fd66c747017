package com.qm.ep.wf.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-19
 */
@ApiModel(value = "对象TaskTranAuthDTO对象", description = "对象TaskTranAuthDTO对象")
@Data
public class TaskTranAuthDTO extends JsonParamDto {

    @ApiModelProperty("串行版 uid")
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "主键ID")
    private String id;
    @ApiModelProperty(value = "任务名称")
    private String taskName;
    @ApiModelProperty(value = "事务码")
    private String transCode;
    @ApiModelProperty(value = "工作流模版Key")
    private String processDefKey;
    @ApiModelProperty(value = "时间戳")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Timestamp dtstamp;
    @ApiModelProperty(value = "按钮CODE")
    private String buttonCode;
    @ApiModelProperty(value = "是否显示状态0否1是")
    private Integer isState;
    @ApiModelProperty(value = "模块代码")
    private String vmodule;
    @ApiModelProperty(value = "用户或角色代码")
    private String actionCode;

    @ApiModelProperty("授权")
    private Integer authorization;
}
