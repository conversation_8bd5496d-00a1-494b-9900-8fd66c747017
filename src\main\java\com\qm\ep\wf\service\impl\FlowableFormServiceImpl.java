package com.qm.ep.wf.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qm.ep.wf.domain.entity.FlowableForm;
import com.qm.ep.wf.mapper.FlowableFormMapper;
import com.qm.ep.wf.service.FlowableFormService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 流程Service
 *
 * <AUTHOR>
@Service
public class FlowableFormServiceImpl extends ServiceImpl<FlowableFormMapper, FlowableForm>
        implements FlowableFormService {

    @Resource
    protected FlowableFormMapper flowableFormMapper;

    @Override
    public IPage<FlowableForm> list(IPage<FlowableForm> page, FlowableForm flowableForm) {
        return page.setRecords(flowableFormMapper.list(page, flowableForm));
    }

    @Override
    public FlowableForm getById(String formKeyDefinition) {
        QueryWrapper<FlowableForm> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("form_key", formKeyDefinition);
        return flowableFormMapper.selectOne(queryWrapper);
    }
}
