package com.qm.ep.wf.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.qm.ep.wf.domain.entity.FlowableForm;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * @description: 运行时的节点Dao
 */
@Mapper
public interface RunFlowableActinstMapper extends BaseMapper<FlowableForm> {

    /**
     * 删除节点信息
     *
     * @param ids ids
     */
    public void deleteRunActinstsByIds(List<String> ids);

    public void deleteRunActByInst(List<String> ids);

    public void deleteRunTimerJob(List<String> ids);

    public void deleteRunRunJob(List<String> ids);

    public void deleteRunExecution(List<String> ids);

    public void deleteRunVariable(List<String> ids);

    public List<Map> selectRunGeByte(List<String> ids);

    public void deleteRunTask(List<String> ids);

    public void deleteRunIdentityLink(List<String> ids);

    public void deleteGeByte(List<String> ids);

    public void deleteRunTaskLink(List<String> ids);

    public List<String> selectRunTaskLink(List<String> ids);
}
