package com.qm.ep.wf.service.impl.v2;

import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.qm.ep.wf.domain.bean.ProcessCommonButtonsDO;
import com.qm.ep.wf.domain.bean.TaskTranAuthDO;
import com.qm.ep.wf.domain.dto.UserDTO;
import com.qm.ep.wf.domain.vo.HistoricProcessInstanceResponse;
import com.qm.ep.wf.domain.vo.RoleVO;
import com.qm.ep.wf.domain.vo.TaskResponse;
import com.qm.ep.wf.exception.FlowableTaskException;
import com.qm.ep.wf.remote.SysFeignRemote;
import com.qm.ep.wf.service.impl.ProcessInstanceServiceImpl;
import com.qm.ep.wf.util.LoginUtil;
import com.qm.ep.wf.wapper.TaskTodoListWrapper;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.exception.QmException;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import com.qm.tds.base.domain.MultiLanguageTextDO;
import com.qm.tds.base.domain.dto.MultiLanguageTextDTO;
import com.qm.tds.base.service.IMultiLanguageTextService;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.I18nUtil;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.bpmn.model.ExtensionElement;
import org.flowable.bpmn.model.Process;
import org.flowable.bpmn.model.UserTask;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.task.api.Task;
import org.flowable.task.api.TaskQuery;
import org.flowable.task.service.impl.persistence.entity.TaskEntityImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 由于 ProcessInstanceServiceImpl 实现中存在一些性能问题
 * 在不影响其他业务的情况下，将原有 ProcessInstanceServiceImpl 实例 提升为 abstract 抽象类
 * 后续将在v2版本中对其中的方法进行优化重写
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/3/30
 */
@Service
public class ProcessInstanceServiceV2Impl extends ProcessInstanceServiceImpl {

    /**
     * 远程调用Feign接口
     */
    @Autowired
    private SysFeignRemote sysFeignRemote;
    @Autowired
    private I18nUtil i18nUtil;
    @Autowired
    private IMultiLanguageTextService multiTextService;
    @Value("${ep.lang.multiFlag:false}")
    private Boolean multiFlag;

    /**
     * 优化根据任务ID获取任务表单button方法
     *
     * @param taskId
     * @return
     */
    @Override
    public Map getTaskButtonsByTaskId(String taskId) {


        Task task = flowableTaskService.getTaskNotNull(taskId);
        List<ProcessCommonButtonsDO> buttons = getElementsButtons(task, null);
        HistoricProcessInstance historicProcessInstance = getHistoricProcessInstanceById(task.getProcessInstanceId());
        Map map = Maps.newHashMap();
        if (null != historicProcessInstance.getBusinessKey()) {
            map.put(historicProcessInstance.getBusinessKey(), buttons);
        }
        // 不是作为状态查询，正常查按钮，如果没有才走redis
        Map<String, List<RoleVO>> stringListMap = cacheCurrentAllUserRole();
        if (!CollectionUtils.isEmpty(buttons)) {
            taskTranAuthService.createRedisData(task.getProcessInstanceId(), stringListMap);
        }

        return map;
    }

    private List<ProcessCommonButtonsDO> getElementsButtons(String id, String processDefinitionId, String taskDefinitionKey, String transCode) {
        TaskEntityImpl task = new TaskEntityImpl();
        task.setId(id);
        task.setProcessDefinitionId(processDefinitionId);
        task.setTaskDefinitionKey(taskDefinitionKey);
        return getElementsButtons(task, transCode);
    }

    /**
     * 从用户任务中获取button元素。
     * @param task
     * @return
     */
    @SuppressWarnings("squid:S135")
    private List<ProcessCommonButtonsDO> getElementsButtons(Task task, String transCode) {
        ProcessDefinition processDefinition = processDefinitionService.getProcessDefinitionById(task.getProcessDefinitionId());
        List<UserTask> userTasks = getUserTasksByProcessDefinitionId(task.getProcessDefinitionId());

        cacheCurrentUserRole();
        Set<ProcessCommonButtonsDO> buttons = new HashSet<>();

        Map<String,String> langMap = new HashMap();
        System.out.println("-------multiFlag-------"+multiFlag);
        if(multiFlag){
            //查询语言全部信息--20211231--
            MultiLanguageTextDTO sysDTO = new MultiLanguageTextDTO();
            sysDTO.setVtablename("act_ex_button");//物理表名称 - SYSC009D 为数据字段 字典项
            sysDTO.setVlanguagecode(BootAppUtil.getLoginKey().getLanguageCode());//语言标识
            JsonResultVo<QmPage<MultiLanguageTextDO>> result = multiTextService.getListByPage(sysDTO);
            List<MultiLanguageTextDO> repList = result.getData().getItems();
            //list转成map  ； key是NMAINID  ，value 是  vtext
            for(MultiLanguageTextDO to : repList){
                langMap.put(to.getNmainid(),to.getVtext());
            }
        }


        Map<String, String> mutexs = Maps.newHashMap();
        for (UserTask userTask : userTasks) {
            List<ExtensionElement> elements = userTask.getExtensionElements().get("button");
            if (CollectionUtils.isEmpty(elements)) {
                continue;
            }
            for (ExtensionElement element : elements) {
                List<ProcessCommonButtonsDO> dbButtonsByExtensionElement = findDBButtonsByExtensionElement(element, processDefinition.getKey());
                for (ProcessCommonButtonsDO buttonsDO : dbButtonsByExtensionElement) {
                    ProcessCommonButtonsDO target = new ProcessCommonButtonsDO();
                    BeanUtils.copyProperties(buttonsDO, target);
                    target.setVisable(userTask.getId().equals(task.getTaskDefinitionKey()) ? 1 : 0);
                    target.setTaskId(userTask.getId());

                    if(multiFlag) {
                        //获取多语言信息
                        //如果sysc000_m有翻译值取翻译值，如果没有就显示原来的值
                        target.setName(langMap.get(target.getId()) == null ? target.getName() : langMap.get(target.getId()));
                    }
                    System.out.println("------target-------"+target.getName());

                    if (BootAppUtil.isNullOrEmpty(transCode)) {
                        buttons.add(target);
                        continue;
                    }
                    List<TaskTranAuthDO> authDOList = taskTranAuthService.selectTaskAuthByParams(userTask.getId(), transCode, processDefinition.getKey(), buttonsDO.getCode());
                    if (CollectionUtils.isEmpty(authDOList)) {
                        continue;
                    }
                    // 非操作授权，默认具有权限
                    if (authDOList.get(0).getAuthorization().equals(1)) {
                        buttons.add(target);
                        continue;
                    }
                    if (judgeTaskAuthForUser(userTask.getId(), processDefinition.getKey(), buttonsDO.getCode(), transCode)) {
                        buttons.add(target);
                    }

                }
            }
        }
        return new ArrayList<>(buttons);
    }

    private boolean isMutexs(Map<String, String> mutexs, ProcessCommonButtonsDO buttonsDO) {
        if (ObjectUtil.isNull(buttonsDO.getMutex()) || buttonsDO.getMutex() == 0) {
            return false;
        }
        if (mutexs.containsKey(buttonsDO.getId())) {
            return true;
        }
        mutexs.put(buttonsDO.getId(), buttonsDO.getId());
        return false;
    }

    /**
     * 根据流程定义ID查询用户任务
     *
     * @param taskProcessDefinitionId ---》流程定义ID
     * @return
     */

    private List<UserTask> getUserTasksByProcessDefinitionId(String taskProcessDefinitionId) {
        BpmnModel bpmnModel = repositoryService.getBpmnModel(taskProcessDefinitionId);
        if (ObjectUtils.isEmpty(bpmnModel)) {
            return Lists.newArrayList();
        }
        List<UserTask> userTasks = Lists.newArrayList();
        for (Process process : bpmnModel.getProcesses()) {
            userTasks.addAll(process.findFlowElementsOfType(UserTask.class));
        }
        return userTasks;
    }

    /**
     * 从数据库中获取button信息
     *
     * @param element
     * @return
     */
    private List<ProcessCommonButtonsDO> findDBButtonsByExtensionElement(ExtensionElement element, String processDefinitionKey) {
        String code = element.getAttributes().get("code").get(0).getValue();
        //定义查询构造器
        QmQueryWrapper<ProcessCommonButtonsDO> queryWrapper = new QmQueryWrapper<>();
        //拼装实体属性查询条件
        //流程定义Key
        queryWrapper.eq("processDefKey", processDefinitionKey);
        //	代码
        queryWrapper.eq("code", code);


        return processCommonButtonsService.list(queryWrapper);
    }

    /**
     * 判断权限
     *
     * @param userTaskId
     * @param processDefinitionKey
     * @param buttonCode
     * @return
     */
    private boolean judgeTaskAuthForUser(String userTaskId, String processDefinitionKey, String buttonCode, String transCode) {
        return taskAuthService.judgeTaskAuthForUser(userTaskId, LoginUtil.getOperatorId(), processDefinitionKey, buttonCode, transCode);
    }

    /**
     * 缓存当前用户角色信息 过期时间60秒
     */

    @Override
    public void cacheCurrentUserRole() {
        String currentUserRoleKey = redisUtils.keyBuilder("wf", "current_user", "role", BootAppUtil.getLoginKey().getOperatorId());
        if (redisUtils.get(currentUserRoleKey) != null) {
            return;
        }

        UserDTO userDTO = new UserDTO();
        userDTO.setId(BootAppUtil.getLoginKey().getOperatorId());
        JsonResultVo roleList = sysFeignRemote.getRoleListByUserId(BootAppUtil.getLoginKey().getTenantId(), userDTO);
        if (roleList.getCode() == 500) {
            throw new FlowableTaskException(roleList.getMsg());
        }
        List<String> roleIds = new ArrayList<>();
        if (roleList.getData() != null) {
            for (LinkedHashMap roleDO : (ArrayList<LinkedHashMap>) roleList.getData()) {
                roleIds.add(roleDO.get("id").toString());
            }
        }


        redisUtils.set(currentUserRoleKey, roleIds, 600);
    }

    /**
     * 缓存当前用户角色信息 过期时间600秒
     */
    @Override
    public Map<String, List<RoleVO>> cacheCurrentAllUserRole() {
        String roleKey = redisUtils.keyBuilder("wf", "role", "all", "v2");

        Object obj = redisUtils.get(roleKey);
        if (null != obj) {
            return (Map<String, List<RoleVO>>) obj;
        }
        JsonResultVo<List<RoleVO>> roleList = sysFeignRemote.getAllRoles();
        if (roleList.getCode() == 500) {
            throw new FlowableTaskException(roleList.getMsg());
        }
        if (CollectionUtils.isEmpty(roleList.getData())) {
            String message = i18nUtil.getMessage("ERR.wf.processInstanceService.roleListEmpty");
            throw new QmException(message);
        }
        Map<String, List<RoleVO>> collect = roleList.getData().stream().collect(Collectors.groupingBy(item -> item.getId()));

        redisUtils.set(roleKey, collect, 7200);
        return collect;
    }


    @Override
    public Map<String, Collection<ProcessCommonButtonsDO>> handleProcessInstanceTaskButtons(List<HistoricProcessInstanceResponse> processInstance, String transCode) {
        cacheCurrentAllUserRole();
        cacheCurrentUserRole();
        // 查询待办节点
        TaskQuery query = taskService.createTaskQuery();
        String userId = LoginUtil.getOperatorId();
        Map<String, Collection<ProcessCommonButtonsDO>> buttonMap = new HashMap<>();
        Map<String, String> mutexs = Maps.newHashMap();


        for (HistoricProcessInstanceResponse response : processInstance) {
            query.processInstanceId(response.getId());
            boolean flag = wfConfService.getProcessAuditAuthCheck(response.getProcessDefinitionKey());
            if (flag) {
                query.or().taskCandidateOrAssigned(userId).taskOwner(userId).endOr();
            }

            List<TaskResponse> list = getTobeDoneList(query, TaskTodoListWrapper.class);

            Set<ProcessCommonButtonsDO> buttonsDOList = Sets.newLinkedHashSet();
            for (TaskResponse taskResponse : list) {
                List<ProcessCommonButtonsDO> elementsButtons = getElementsButtons(taskResponse.getId(), taskResponse.getProcessDefinitionId(), taskResponse.getTaskDefinitionKey(), transCode);
                if (CollectionUtils.isEmpty(elementsButtons)) {
                    continue;
                }
                buttonsDOList.addAll(elementsButtons);
            }

            List<ProcessCommonButtonsDO> unButtons = buttonsDOList.stream().filter(t -> t.getCode().contains("un_")).collect(Collectors.toList());
            for (TaskResponse taskResponse : list) {
                unButtons.stream().filter(s -> ("un_" + taskResponse.getTaskDefinitionKey()).equals(s.getCode())).forEach(t -> t.setVisable(0));
            }

            Set<ProcessCommonButtonsDO> playerSet = new TreeSet<>(Comparator.comparing(o -> (o.getMutex() + "" + o.getCode() + "" + o.getVisable())));
            playerSet.addAll(buttonsDOList);

            putButtonMaps(buttonMap, response.getBusinessKey(), playerSet);
        }
        return buttonMap;
    }

    public void putButtonMaps(Map<String, Collection<ProcessCommonButtonsDO>> buttonMap, String key, Set<ProcessCommonButtonsDO> values) {
        if (!buttonMap.containsKey(key)) {
            buttonMap.put(key, values);
            return;
        }
        Collection<ProcessCommonButtonsDO> buttonsDOS = buttonMap.get(key);
        buttonsDOS.addAll(values);
        buttonMap.put(key, buttonsDOS);
    }
}
