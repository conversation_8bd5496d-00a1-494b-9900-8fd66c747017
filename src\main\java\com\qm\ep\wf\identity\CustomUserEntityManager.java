package com.qm.ep.wf.identity;

import com.google.common.collect.Lists;
import org.flowable.idm.api.User;
import org.flowable.idm.engine.IdmEngineConfiguration;
import org.flowable.idm.engine.impl.UserQueryImpl;
import org.flowable.idm.engine.impl.persistence.entity.UserEntity;
import org.flowable.idm.engine.impl.persistence.entity.UserEntityImpl;
import org.flowable.idm.engine.impl.persistence.entity.UserEntityManagerImpl;
import org.flowable.idm.engine.impl.persistence.entity.data.UserDataManager;

import java.util.List;

/**
 * <AUTHOR> @date
 */
public class CustomUserEntityManager extends UserEntityManagerImpl {

    public CustomUserEntityManager(IdmEngineConfiguration idmEngineConfiguration, UserDataManager userDataManager) {
        super(idmEngineConfiguration, userDataManager);
    }

    @Override
    public UserEntity findById(String entityId) {
        return new UserEntityImpl();
    }

    @Override
    public List<User> findUserByQueryCriteria(UserQueryImpl query) {
        return Lists.newArrayList();
    }

    @Override
    public long findUserCountByQueryCriteria(UserQueryImpl query) {
        return 1;
    }
}
