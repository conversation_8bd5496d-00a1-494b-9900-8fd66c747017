package com.qm.ep.wf.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-18
 */
@ApiModel(value = "对象ProcessCounterSignDTO对象", description = "对象ProcessCounterSignDTO对象")
@Data
public class ProcessCounterSignDTO extends JsonParamDto {

    @ApiModelProperty("串行版 uid")
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "主键ID")
    private String id;
    @ApiModelProperty(value = "会签组名称")
    private String grpName;
    @ApiModelProperty(value = "人工任务名称")
    private String taskKey;
    @ApiModelProperty(value = "流程定义Key 对应bpmn中process的ID")
    private String processDefKey;
    @ApiModelProperty(value = "会签类型，（比例通过制、一票否决制、自定义）")
    private String type;
    @ApiModelProperty(value = "自定义属性")
    private String customValue;
    @ApiModelProperty(value = "比例")
    private String rate;
    @ApiModelProperty(value = "时间戳")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Timestamp dtstamp;
    @ApiModelProperty(value = "流程定义Key数组")
    private List<String> processDefKeys;
}
