package com.qm.ep.wf.domain.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/5/26
 */
@Data
public class ProcessDefinitionQueryDTO implements Serializable {
    /**
     * 当前页码
     */
    private Integer currentPage;
    /**
     * 每页大小
     */
    private Integer pageSize;
    /**
     * 流程定义名称
     */
    private String name;
    /**
     * 流程定义Key
     */
    private String key;

    /**
     * 所属模块
     */
    private String vmodule;

    /**
     * WF_CONF 表字段，是否为操作授权，0是false，1是 true
     */
    private Integer authorization;

}
