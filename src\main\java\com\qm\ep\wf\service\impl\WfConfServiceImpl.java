package com.qm.ep.wf.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.qm.ep.wf.domain.dto.WfConfDTO;
import com.qm.ep.wf.domain.entity.WfConf;
import com.qm.ep.wf.mapper.WfConfMapper;
import com.qm.ep.wf.service.ProcessDefinitionService;
import com.qm.ep.wf.service.WfConfService;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.service.impl.QmBaseServiceImpl;
import com.qm.tds.util.BootAppUtil;
import org.flowable.engine.repository.ProcessDefinition;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 *
 */
@Service
public class WfConfServiceImpl extends QmBaseServiceImpl<WfConfMapper, WfConf> implements WfConfService {
    @Resource
    private WfConfMapper wfConfMapper;
    @Autowired
    protected ProcessDefinitionService processDefinitionService;

    @Override
    public QmPage<WfConf> getWfConf(WfConfDTO confDTO) {
        QueryWrapper<WfConf> queryWrapper = new QueryWrapper<>();
        if (confDTO != null) {
            String code = confDTO.getVwfCode();
            String name = confDTO.getVwfName();
            if (!BootAppUtil.isNullOrEmpty(code)) {
                queryWrapper.lambda().like(WfConf::getVwfCode, code);
            }
            if (!BootAppUtil.isNullOrEmpty(name)) {
                queryWrapper.lambda().like(WfConf::getVwfName, name);
            }
            if (!BootAppUtil.isNullOrEmpty(confDTO.getVmodule())) {
                queryWrapper.lambda().like(WfConf::getVmodule, confDTO.getVmodule());
            }
            if (!BootAppUtil.isNullOrEmpty(confDTO.getVmodel())) {
                queryWrapper.lambda().eq(WfConf::getVmodel, confDTO.getVmodel());
            }
        }
        return table(queryWrapper, confDTO);
    }

    /**
     * @description type为1查询流程ID，2为流程定义key
     * <AUTHOR>
     * @date 2020/9/30 16:08
     */
    @Override
    public Set<String> getWfConfByModule(String module, Integer type) {
        QueryWrapper<WfConf> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().like(WfConf::getVmodule, module);
        QmPage<WfConf> page = table(queryWrapper, new WfConfDTO());
        Set<String> set = new HashSet<>();
        if (!page.getItems().isEmpty()) {
            for (WfConf item : page.getItems()) {
                String str = item.getVmodel();
                if (type == 1) {
                    ProcessDefinition p = processDefinitionService.getProcessDefinitionByPKey(item.getVmodel());
                    if (!BootAppUtil.isNullOrEmpty(p)) {
                        str = p.getId();
                    }
                }
                set.add(str);
            }
        }
        return set;
    }

    @Override
    public WfConf getWfConfByWF(String wfName) {
        QueryWrapper<WfConf> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().like(WfConf::getVmodel, wfName).or().like(WfConf::getVmodel, wfName);
        List<WfConf> wfConfs = wfConfMapper.selectList(queryWrapper);
        if (!wfConfs.isEmpty()) {
            return wfConfs.get(0);
        } else {
            return null;
        }
    }

    @Override
    public WfConf getWfConfByVmodel(String wfName) {
        QueryWrapper<WfConf> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(WfConf::getVmodel, wfName);
        List<WfConf> wfConfs = wfConfMapper.selectList(queryWrapper);
        if (!wfConfs.isEmpty()) {
            return wfConfs.get(0);
        } else {
            return null;
        }
    }


    @Override
    public WfConf getConditionWfConf(String vwfCode) {
        QueryWrapper<WfConf> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(WfConf::getVwfCode, vwfCode);
        return wfConfMapper.selectOne(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateWfConf(WfConf wfConf) {
        wfConfMapper.updateById(wfConf);
    }

    @Override
    public boolean getProcessAuditAuthCheck(String procDefKey) {
        boolean flag = false;
        //考虑性能，临时去掉
/*        QueryWrapper<WfConf> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(WfConf::getVmodel, procDefKey);
        List<WfConf> wfConfs = wfConfMapper.selectList(queryWrapper);
        if (!wfConfs.isEmpty() && wfConfs.get(0).getAuditAuthCheck() == 1) {
            flag = true;
        }*/
        return flag;
    }
}
