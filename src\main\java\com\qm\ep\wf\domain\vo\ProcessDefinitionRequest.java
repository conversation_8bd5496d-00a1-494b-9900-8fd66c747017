package com.qm.ep.wf.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> @date
 */
@ApiModel(description = "流程定义请求")
@Data
public class ProcessDefinitionRequest {
    @ApiModelProperty("进程定义 ID")
    private String processDefinitionId;
    @ApiModelProperty("包括流程实例")
    private boolean includeProcessInstances = false;
    @ApiModelProperty("日期")
    private Date date;
}
