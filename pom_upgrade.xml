<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.qm.ep</groupId>
        <artifactId>tds-service-parent-nacos-bastnew</artifactId>
        <version>4.0.0-SNAPSHOT</version>
    </parent>
    <groupId>com.qm.ep</groupId>
    <artifactId>common-wf</artifactId>
    <version>0.0.9-SNAPSHOT</version>
    <name>common-wf</name>
    <description>工作流</description>
    <packaging>jar</packaging>

    <dependencies>
        <!--flowable -->
        <dependency>
            <groupId>org.flowable</groupId>
            <artifactId>flowable-spring-boot-starter</artifactId>
            <version>6.6.0</version>
            <exclusions><!-- 需要排除flowable的mybatis依赖，不然会跟mybatis-plus冲突 -->
                <exclusion>
                    <groupId>org.mybatis</groupId>
                    <artifactId>mybatis</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baomidou</groupId>
                    <artifactId>mybatis-plus-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.liquibase</groupId>
                    <artifactId>liquibase-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.flowable</groupId>
            <artifactId>flowable-idm-spring</artifactId>
            <version>6.6.0</version>
            <exclusions><!-- 需要排除flowable的mybatis依赖，不然会跟mybatis-plus冲突 -->
                <exclusion>
                    <groupId>org.mybatis</groupId>
                    <artifactId>mybatis</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baomidou</groupId>
                    <artifactId>mybatis-plus-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.liquibase</groupId>
                    <artifactId>liquibase-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.flowable</groupId>
            <artifactId>flowable-idm-spring-configurator</artifactId>
            <version>6.6.0</version>
            <exclusions><!-- 需要排除flowable的mybatis依赖，不然会跟mybatis-plus冲突 -->
                <exclusion>
                    <groupId>org.mybatis</groupId>
                    <artifactId>mybatis</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baomidou</groupId>
                    <artifactId>mybatis-plus-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.liquibase</groupId>
                    <artifactId>liquibase-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.flowable</groupId>
            <version>6.6.0</version>
            <artifactId>flowable-json-converter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.liquibase</groupId>
                    <artifactId>liquibase-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- MQ -->
        <dependency>
            <groupId>com.qm.tds</groupId>
            <artifactId>tds-base-mq</artifactId>
            <version>4.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-bus</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-function-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.qm.tds</groupId>
            <artifactId>tds-base-lock</artifactId>
            <version>4.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-bus</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-function-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

    </dependencies>

    <!-- Maven仓库地址 -->
    <repositories>
        <repository>
            <id>qm_maven_center</id>
            <name>qm dms maven center</name>
            <url>http://yldevrepo.faw.com:8080/repository/maven-snapshots/</url>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
                <checksumPolicy>warn</checksumPolicy>
            </snapshots>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>


    <!-- 上传jar包至启明私有maven仓库-->
    <distributionManagement>
        <!-- 开发环境 -->
        <repository>
            <id>qm-dms-nexus-releases</id>
            <name>Nexus Release Repository</name>
            <url>http://jfapp.qm.cn:8003/repository/qm-dms-releases/</url>
        </repository>
        <snapshotRepository>
            <id>qm-dms-nexus-snapshots</id>
            <name>Nexus Snapshot Repository</name>
            <url>http://jfapp.qm.cn:8003/repository/qm-dms-snapshots/</url>
        </snapshotRepository>

    </distributionManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>

