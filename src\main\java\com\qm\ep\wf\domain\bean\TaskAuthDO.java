package com.qm.ep.wf.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("act_ex_taskactorauth")
@ApiModel(value = "对象TaskAuthDO对象", description = "对象TaskAuthDO对象")
public class TaskAuthDO implements Serializable {

    @ApiModelProperty("串行版 uid")
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty(value = "人工任务名称")
    @TableField("TASK_NAME")
    private String taskName;

    @ApiModelProperty(value = "参与者类型 字典项（用户、角色）")
    @TableField("ACTION_TYPE")
    private String actionType;

    @ApiModelProperty(value = "参与者代码")
    @TableField("ACTION_CODE")
    private String actionCode;

    @ApiModelProperty(value = "流程定义Key 对应bpmn中process的ID")
    @TableField("PROCESS_DEF_KEY")
    private String processDefKey;

    @ApiModelProperty(value = "时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    @Version
    private Timestamp dtstamp;

    @ApiModelProperty(value = "按钮CODE")
    @TableField("BUTTON_CODE")
    private String buttonCode;

    @ApiModelProperty(value = "按钮名称")
    @TableField(exist = false)
    private String buttonName;

    @ApiModelProperty(value = "按钮备注")
    @TableField(exist = false)
    private String buttonRemark;

    @ApiModelProperty(value = "按钮状态")
    @TableField(exist = false)
    private String buttonState;

    @ApiModelProperty(value = "事务码")
    @TableField("TRANS_CODE")
    private String transCode;
}
