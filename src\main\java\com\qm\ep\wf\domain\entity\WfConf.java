package com.qm.ep.wf.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/6/28 9:14
 */
@ApiModel(description = "数据WF 会议")
@Data
@Accessors(chain = true)
@TableName("wf_conf")
public class WfConf implements Serializable {
    @ApiModelProperty("串行版 uid")
    private static final long serialVersionUID = -3974756660021726422L;
    @ApiModelProperty(value = "主键")
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty("创建用户")
    private String createUser;

    @ApiModelProperty("创造时间")
    private Date createTime;

    @ApiModelProperty("更新用户")
    private String updateUser;

    @ApiModelProperty("更新时间")
    private Date updateTime;

    @ApiModelProperty("版本")
    private Integer version;

    @ApiModelProperty("数据VWF代码")
    private String vwfCode;

    @ApiModelProperty("数据VWF 名称")
    private String vwfName;

    @ApiModelProperty("数据vmodel")
    private String vmodel;

    @ApiModelProperty("数据vbill 路由")
    private String vbillRoute;

    @ApiModelProperty("数据VSF")
    private String vsF;

    @ApiModelProperty("数据vstarter")
    private String vstarter;

    @ApiModelProperty("数据busi 表名称")
    @TableField("BUSI_TABLENAME")
    private String busiTableName;

    @ApiModelProperty("状态字段名称")
    @TableField("STATE_FIELDNAME")
    private String stateFieldName;

    @ApiModelProperty("数据busi 键字段名称")
    @TableField("BUSIKEY_FIELDNAME")
    private String busiKeyFieldName;

    @ApiModelProperty(value = "启动日期")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField(value = "dstart", updateStrategy = FieldStrategy.IGNORED)
    private Date dstart;

    @ApiModelProperty(value = "时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    @Version
    private Timestamp dtstamp;

    @ApiModelProperty("审核身份验证检查")
    @TableField("AUDIT_AUTH_CHECK")
    private Integer auditAuthCheck;

    @ApiModelProperty("数据vmodule 模块")
    @TableField("VMODULE")
    private String vmodule;
    /**
     * 非操作授权，默认为 0
     */
    @ApiModelProperty("非操作授权，默认为 0")
    @TableField("AUTHORIZATION")
    private Integer authorization;
}
