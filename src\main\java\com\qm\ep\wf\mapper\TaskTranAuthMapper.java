package com.qm.ep.wf.mapper;

import com.qm.ep.wf.domain.bean.TaskTranAuthDO;
import com.qm.ep.wf.domain.dto.TaskTranAuthDTO;
import com.qm.tds.api.mp.mapper.QmBaseMapper;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-19
 */
public interface TaskTranAuthMapper extends QmBaseMapper<TaskTranAuthDO> {

    /**
     * @description 根据任务id、事务码、流程定义key判断是否有权限
     * <AUTHOR>
     * @date 2020/8/21 16:16
     */
    public Integer judgeTaskAuth(TaskTranAuthDTO taskTranAuthDTO);

    /**
     * 查询操作授权列表
     *
     * @param taskTranAuthDTO
     * @return
     */
    List<TaskTranAuthDO> selectTaskAuthByParams(TaskTranAuthDTO taskTranAuthDTO);
}
