#这里填写英文翻译
MSG.wf.common.saveSuccess=Save succeeded!                                                               
ERR.wf.common.saveFail=Save failed!                                                                     
MSG.wf.common.delSuccess=Deletion succeeded!                                                            
ERR.wf.common.delFail=Deletion failed!                                                                  
MSG.wf.common.updateSuccess=Edit succeeded!                                                             
ERR.wf.common.updateFail=Edit failed!                                                                   
ERR.wf.common.isEmpty=%s should not be null                                                             
ERR.wf.processCommonButtons.rptItemDO2=There is a repetitive process code, please re-enter              
MSG.wf.flow.defination=Process definition:                                                              
MSG.wf.task.name=Task:                                                                                  
MSG.wf.transaction.code=Transaction code:   
MSG.wf.common.terminator=.                                                           
MSG.wf.taskAuth.checkIsLast=After removing this permission, the button does not have any operator author
ERR.wf.deleteProcesss.exception=Process: %s deletion failed %s                                          
ERR.wf.deleteProcessInstance.exception=The deletion of the business primary key %s failed! The reason is
MSG.wf.common.claimSuccess=Claim succeeded!                                                             
MSG.wf.common.cancelClaimSuccess=Claim cancellation succeeded!                                          
ERR.wf.common.failReason=Execution failed due to: %s                                                    
MSG.wf.common.success=Succeeded!                                                                        
ERR.wf.flowableForm.save=Form: %s already exists!                                                       
ERR.wf.task.errorMessage=Failed due to: unable to find to-do task!                                      
ERR.wf.task.failMessage=Failed due to:                                                                  
ERR.wf.processInstance.startErrMsg=Process startup failed due to                                        
ERR.wf.processInstanceService.roleListEmpty=The role list is empty.                                     
MSG.wf.copyButton.copySuccess=Copy succeeded                                                            
ERR.wf.copyButton.copyFail=Copy failed                                                                  
ERR.wf.copyButton.nonExist=Source process does not have button                                          
ERR.wf.processCounterSignService.customeSaveOrUpdate=There is a repetitive countersignature group policy
MSG.wf.common.operateSuccess=Operation succeeded                                                        
ERR.wf.processDefinitionService.definitionExist=Process definition: %s has a running instance and cannot
ERR.wf.processInstanceService.processEndDelete=Process instance has ended, deletion is not allowed!     
ERR.wf.processInstanceService.checkedProcessDelete=Only unreviewed processes can be deleted!            
MSG.wf.common.processInstanceService.endProcessStop=Process instance has ended, termination is not allow
ERR.wf.processInstanceService.checkedProcessStop=Only unreviewed process instances can be terminated!   
MSG.wf.processInstanceService.definitionTemplateNotFound=Workflow template definition not found         
MSG.wf.flowableTaskService.rollback=Back to %s.                                                         
MSG.wf.flowableTaskService.jobFinish=Job completion task                                                
MSG.wf.flowableTaskService.jobEndFlow=Job end process                                                   
MSG.wf.flowableTaskService.jobRollback=Job rollback task                                                
MSG.wf.taskAuthService.cleanSuccess=Permission deletion succeeded                                       
MSG.wf.common.tradeFail=Transaction execution failed  
##########################################
MSG.wf.CommentTypeEnum.init=Initial                                                                     
MSG.wf.CommentTypeEnum.commit=Submit                                                                    
MSG.wf.CommentTypeEnum.recommit=Resubmit                                                                
MSG.wf.CommentTypeEnum.claim=Claim                                                                      
MSG.wf.CommentTypeEnum.disclaim=Claim cancellation                                                      
MSG.wf.CommentTypeEnum.approve=Approval                                                                 
MSG.wf.CommentTypeEnum.finish=Finish                                                                    
MSG.wf.CommentTypeEnum.return=Return                                                                    
MSG.wf.CommentTypeEnum.withdraw=Withdrawal                                                              
MSG.wf.CommentTypeEnum.tempStorage=Temporary storage                                                    
MSG.wf.CommentTypeEnum.turnTo=Turn to do                                                                
MSG.wf.CommentTypeEnum.delegate=Assignment                                                              
MSG.wf.CommentTypeEnum.stop=Termination                                                                 
MSG.wf.FlowableConstant.autoReview=Automatic review                                                     
ERR.wf.FlowableConstant.busiSourceUsing=Business resources are being occupied, please refresh and try ag
ERR.wf.FlowableConstant.sourceUsing=Resources are being occupied, please refresh and try again later!   
ERR.wf.ProcessCommonButtonsController.operateSuccess=Operation succeeded!                               
MSG.wf.ProcessCommonButtonsController.operateFail=Operation failed!                                     
ERR.wf.ProcessController.submitProcessId=Submission succeeded, the process ID is:                       
ERR.wf.ProcessController.processNonexist=Process does not exist                                         
MSG.wf.ProcessController.approvalOrNot=Approval passed:                                                 
ERR.wf.ProcessController.nodeNonexist=Node does not exist                                               
ERR.wf.ProcessController.rejectSuccess=Rejection succeeded...                                           
ERR.wf.ProcessController.stopProcessInstanceSuccess=Process instance termination succeeded              
MSG.wf.ProcessController.pendingProcessSuccess=Process suspension succeeded...                          
MSG.wf.ProcessController.recoveryProcessSuccess=Process recovery succeeded...                           
ERR.wf.ProcessDefinitionController.processIdNull=Process ID should not be null                          
ERR.wf.ProcessDefinitionController.activateSuccess=Activation succeeded!                                
ERR.wf.ProcessDefinitionController.pendingSuccess=Suspension succeeded!                                 
ERR.wf.ProcessInstanceController.processStartupError=There is a problem with the start of the %s process
ERR.wf.ProcessInstanceController.processStartupFail=process startup failed, see the failure information 
ERR.wf.ProcessInstanceController.processStopSuccess=Process termination succeeded!                      
ERR.wf.ProcessInstanceController.wfinstanceNonexist=Workflow instance does not exist                    
ERR.wf.ProcessInstanceController.processChangeFail=Business primary key: %s process variable update fail
ERR.wf.ProcessInstanceController.processStartupFailInfo=Process startup failed:                         
ERR.wf.TaskAuthController.processDefine=Process definition:                                             
ERR.wf.TaskAuthController.task=Task:                                                                    
ERR.wf.TaskAuthController.trancCode=Transaction code:                                                   
ERR.wf.TaskAuthController.button=Button:                                                                
ERR.wf.TaskAuthController.java=These buttons perform operator authorization for the first time. After sa
ERR.wf.TaskController.wfcodeError=Workflow code: %s Business primary key： %s workflow instance does not 
ERR.wf.TaskController.failReasonIs=Failed due to:                                                       
ERR.wf.TaskController.batchFail=Batch %s failed, please select a document with the same state to process
ERR.wf.TaskController.buttonTaskDiff=Failed due to: the button is inconsistent with the current task, pl
ERR.wf.TaskController.taskFailPrompt=%s task %s failed!                                                 
ERR.wf.TaskController.processing=Processing, please try again later!                                    
ERR.wf.TaskController.rejectedBack=You have rejected, and the task has been backed to the previous node 
