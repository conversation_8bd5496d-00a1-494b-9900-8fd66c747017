<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qm.ep.wf.mapper.ProcessListenerMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qm.ep.wf.domain.bean.ProcessListenerDO">
        <id column="ID" property="id"/>
        <result column="NAME" property="name"/>
        <result column="LISTENER_TYPE" property="listenerType"/>
        <result column="EVENT_TYPE" property="eventType"/>
        <result column="VALUE_TYPE" property="valueType"/>
        <result column="EXPRESSION" property="expression"/>
        <result column="PROCESS_DEF_KEY" property="processDefKey"/>
        <result column="DTSTAMP" property="dtstamp"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
    ID, NAME, LISTENER_TYPE, EVENT_TYPE, VALUE_TYPE, EXPRESSION, PROCESS_DEF_KEY, DTSTAMP
    </sql>

    <!-- 公共查询 -->
    <sql id="QuerySQL">
        select * from (
            select
                a.NAME,
                a.LISTENER_TYPE,
                a.EVENT_TYPE,
                a.VALUE_TYPE,
                a.EXPRESSION,
                a.PROCESS_DEF_KEY,
                a.ID,
                a.DTSTAMP
            from act_ex_listener a
        ) innerTable
    </sql>

    <!-- 复写MP自带函数 -->
    <select id="selectByIdNew" resultType="com.qm.ep.wf.domain.bean.ProcessListenerDO">
        <include refid="QuerySQL"/>
        where id = #{id}
    </select>
    <select id="selectBatchIdsNew" resultType="com.qm.ep.wf.domain.bean.ProcessListenerDO">
        <include refid="QuerySQL"/>
        <if test="coll != null and !coll.isEmpty">
            <where>
                id in (<foreach collection="coll" item="item" separator=",">#{item}</foreach>)
            </where>
        </if>
    </select>
    <select id="selectByMapNew" resultType="com.qm.ep.wf.domain.bean.ProcessListenerDO">
        <include refid="QuerySQL"/>
        <if test="cm != null and !cm.isEmpty">
            <where>
                <foreach collection="cm" index="k" item="v" separator="AND">
                    <choose>
                        <when test="v == null">${k} IS NULL</when>
                        <otherwise>${k} = #{v}</otherwise>
                    </choose>
                </foreach>
            </where>
        </if>
    </select>
    <select id="selectOne" resultType="com.qm.ep.wf.domain.bean.ProcessListenerDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectCount" resultType="java.lang.Integer">
        select count(1) from (<include refid="QuerySQL"/>${ew.customSqlSegment} ) countTable
    </select>
    <select id="selectList" resultType="com.qm.ep.wf.domain.bean.ProcessListenerDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectMaps" resultType="com.qm.ep.wf.domain.bean.ProcessListenerDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectObjs" resultType="com.qm.ep.wf.domain.bean.ProcessListenerDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectPage" resultType="com.qm.ep.wf.domain.bean.ProcessListenerDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectMapsPage" resultType="com.qm.ep.wf.domain.bean.ProcessListenerDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
</mapper>
