package com.qm.ep.wf.controller;

import com.alibaba.fastjson.JSONObject;
import com.qm.ep.wf.common.BaseFlowableController;
import com.qm.ep.wf.common.FlowablePage;
import com.qm.ep.wf.constant.Constants;
import com.qm.ep.wf.constant.FlowableConstant;
import com.qm.ep.wf.domain.bean.ActHistoryCommentDO;
import com.qm.ep.wf.domain.entity.WfConf;
import com.qm.ep.wf.domain.vo.HistoricProcessInstanceResponse;
import com.qm.ep.wf.domain.vo.ProcessInstanceDetailResponse;
import com.qm.ep.wf.domain.vo.ProcessInstanceRequest;
import com.qm.ep.wf.service.ProcessDefinitionService;
import com.qm.ep.wf.service.ProcessInstanceService;
import com.qm.ep.wf.service.WfConfService;
import com.qm.ep.wf.util.*;
import com.qm.ep.wf.wapper.CommentListWrapper;
import com.qm.ep.wf.wapper.ProcInsListWrapper;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.I18nUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.flowable.common.engine.api.FlowableException;
import org.flowable.common.engine.api.query.QueryProperty;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.history.HistoricProcessInstanceQuery;
import org.flowable.engine.impl.HistoricProcessInstanceQueryProperty;
import org.flowable.engine.impl.persistence.entity.CommentEntityImpl;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.engine.task.Attachment;
import org.flowable.engine.task.Comment;
import org.flowable.task.api.Task;
import org.flowable.variable.api.history.HistoricVariableInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> @date
 */
@SuppressWarnings({"squid:S3776", "squid:S3740", "rawtypes", "unchecked"})
@RestController
@Slf4j
@RequestMapping("/flowable/processInstance")
@Api(value = "流程实例", tags = {"processInstance"})
public abstract class ProcessInstanceController extends BaseFlowableController {

    @Resource
    private WfConfService wfConfService;
    @Autowired
    private I18nUtil i18nUtil;
    private static final Map<String, QueryProperty> allowedSortProperties = new HashMap<>();

    private static final String START_TIME = "startTime";

    private static final String PROCESS_INSTANCE_FORM_DATA = "processInstanceFormData";

    private static final String TRANS_CODE = "transCode";

    private static final String PROCESS_DEF_KEY = "processDefKey";

    @Autowired
    protected ProcessInstanceService processInstanceService;
    @Autowired
    private ProcessDefinitionService processDefinitionService;

    @Value("${ep.lang.multiFlag:false}")
    private Boolean multiFlag;

    static {
        allowedSortProperties.put(FlowableConstant.ID, HistoricProcessInstanceQueryProperty.PROCESS_INSTANCE_ID_);
        allowedSortProperties.put(FlowableConstant.PROCESS_DEFINITION_ID,
                HistoricProcessInstanceQueryProperty.PROCESS_DEFINITION_ID);
        allowedSortProperties.put(FlowableConstant.PROCESS_DEFINITION_KEY,
                HistoricProcessInstanceQueryProperty.PROCESS_DEFINITION_KEY);
        allowedSortProperties.put(FlowableConstant.BUSINESS_KEY, HistoricProcessInstanceQueryProperty.BUSINESS_KEY);
        allowedSortProperties.put(START_TIME, HistoricProcessInstanceQueryProperty.START_TIME);
        allowedSortProperties.put("endTime", HistoricProcessInstanceQueryProperty.END_TIME);
        allowedSortProperties.put("duration", HistoricProcessInstanceQueryProperty.DURATION);
        allowedSortProperties.put(FlowableConstant.TENANT_ID, HistoricProcessInstanceQueryProperty.TENANT_ID);
    }

    @ApiOperation(value = "列表", notes = "[author:10027705]", httpMethod = "POST")
    @PostMapping(value = "/list")
    public JsonResultVo list1(@RequestBody Map<String, String> requestParams) {
        JsonResultVo resultObj = new JsonResultVo();
        HistoricProcessInstanceQuery query = historyService.createHistoricProcessInstanceQuery();
        query.orderByProcessInstanceEndTime();
        if (CommonUtil.isNotEmptyAfterTrim(requestParams.get(START_TIME))) {
            Date startTime = DateUtil.strToDateTime(requestParams.get(START_TIME));
            query.startedAfter(startTime);
        }
        if (CommonUtil.isNotEmptyAfterTrim(requestParams.get(FlowableConstant.PROCESS_INSTANCE_ID))) {
            query.processInstanceId(requestParams.get(FlowableConstant.PROCESS_INSTANCE_ID));
        }
        if (CommonUtil.isNotEmptyAfterTrim(requestParams.get(FlowableConstant.PROCESS_INSTANCE_NAME))) {
            query.processInstanceNameLike(ObjectUtils.convertToLike(requestParams.get(FlowableConstant.PROCESS_INSTANCE_NAME)));
        }
        if (CommonUtil.isNotEmptyAfterTrim(requestParams.get(FlowableConstant.PROCESS_DEFINITION_NAME))) {
            query.processDefinitionName(requestParams.get(FlowableConstant.PROCESS_DEFINITION_NAME));
        }
        if (CommonUtil.isNotEmptyAfterTrim(requestParams.get(FlowableConstant.PROCESS_DEFINITION_KEY))) {
            query.processDefinitionKey(requestParams.get(FlowableConstant.PROCESS_DEFINITION_KEY));
        }
        if (CommonUtil.isNotEmptyAfterTrim(requestParams.get(FlowableConstant.PROCESS_DEFINITION_ID))) {
            query.processDefinitionId(requestParams.get(FlowableConstant.PROCESS_DEFINITION_ID));
        }
        if (CommonUtil.isNotEmptyAfterTrim(requestParams.get(FlowableConstant.BUSINESS_KEY))) {
            query.processInstanceBusinessKey(requestParams.get(FlowableConstant.BUSINESS_KEY));
        }
        if (CommonUtil.isNotEmptyAfterTrim(requestParams.get(FlowableConstant.START_TIME))) {
            String startTimeStr = requestParams.get(FlowableConstant.START_TIME);
            Date startTime = null;
            if (!BootAppUtil.isNullOrEmpty(startTimeStr)) {
                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                ParsePosition pos = new ParsePosition(0);
                startTime = formatter.parse(startTimeStr, pos);
            }
            query.startedAfter(startTime);
        }
        if (CommonUtil.isNotEmptyAfterTrim(requestParams.get(FlowableConstant.INVOLVED_USER))) {
            query.involvedUser(requestParams.get(FlowableConstant.INVOLVED_USER));
        }
        if (CommonUtil.isNotEmptyAfterTrim(requestParams.get(FlowableConstant.FINISHED))) {
            boolean isFinished = ObjectUtils.convertToBoolean(requestParams.get(FlowableConstant.FINISHED));
            if (isFinished) {
                query.finished();
            } else {
                query.unfinished();
            }
        }
        if (CommonUtil.isNotEmptyAfterTrim(requestParams.get(FlowableConstant.SUPER_PROCESS_INSTANCE_ID))) {
            query.superProcessInstanceId(requestParams.get(FlowableConstant.SUPER_PROCESS_INSTANCE_ID));
        }
        if (CommonUtil.isNotEmptyAfterTrim(requestParams.get(FlowableConstant.EXCLUDE_SUBPROCESSES))) {
            query.excludeSubprocesses(
                    ObjectUtils.convertToBoolean(requestParams.get(FlowableConstant.EXCLUDE_SUBPROCESSES)));
        }
        if (CommonUtil.isNotEmptyAfterTrim(requestParams.get(FlowableConstant.FINISHED_AFTER))) {
            query.finishedAfter(ObjectUtils.convertToDatetime(requestParams.get(FlowableConstant.FINISHED_AFTER)));
        }
        if (CommonUtil.isNotEmptyAfterTrim(requestParams.get(FlowableConstant.FINISHED_BEFORE))) {
            query.finishedBefore(ObjectUtils.convertToDatetime(requestParams.get(FlowableConstant.FINISHED_BEFORE)));
        }
        if (CommonUtil.isNotEmptyAfterTrim(requestParams.get(FlowableConstant.STARTED_AFTER))) {
            query.startedAfter(ObjectUtils.convertToDatetime(requestParams.get(FlowableConstant.STARTED_AFTER)));
        }
        if (CommonUtil.isNotEmptyAfterTrim(requestParams.get(FlowableConstant.STARTED_BEFORE))) {
            query.startedBefore(ObjectUtils.convertToDatetime(requestParams.get(FlowableConstant.STARTED_BEFORE)));
        }
        if (CommonUtil.isNotEmptyAfterTrim(requestParams.get(FlowableConstant.STARTED_BY))) {
            query.startedBy(requestParams.get(FlowableConstant.STARTED_BY));
        }
        //TODO 牛昌注释掉下列代码: 此处代码存在问题，starteBy对应的应该是 ACT_HI_PROCINST.START_USER_ID_ 字段，但是这里获取的事开始时间因此提出疑问
//        if (CommonUtil.isNotEmptyAfterTrim(requestParams.get(FlowableConstant.START_TIME))) {
//            query.startedBy(requestParams.get(FlowableConstant.START_TIME));
//        }
        // startByMe 覆盖 startedBy
        if (CommonUtil.isNotEmptyAfterTrim(requestParams.get(FlowableConstant.START_BY_ME))) {
            boolean isStartByMe = ObjectUtils.convertToBoolean(requestParams.get(FlowableConstant.START_BY_ME));
            if (isStartByMe) {
                query.startedBy(LoginUtil.getOperatorId());
            }
        }
        if (CommonUtil.isNotEmptyAfterTrim(requestParams.get(FlowableConstant.TENANT_ID))) {
            query.processInstanceTenantIdLike(requestParams.get(FlowableConstant.TENANT_ID));
        }
        requestParams.put(Constants.ORDER_RULE, "endTime|ASC,startTime|DESC");
        FlowablePage page = pageList(requestParams, query, ProcInsListWrapper.class, allowedSortProperties,
                HistoricProcessInstanceQueryProperty.START_TIME);
        resultObj.setData(page);
        return resultObj;
    }

    @ApiOperation(value = "列表", notes = "[author:10027705]", httpMethod = "GET")
    @GetMapping(value = "/list")
    public JsonResultVo list(@RequestParam Map<String, String> requestParams) {
        JsonResultVo resultObj = new JsonResultVo();
        HistoricProcessInstanceQuery query = historyService.createHistoricProcessInstanceQuery();
        query.orderByProcessInstanceEndTime();
        if (CommonUtil.isNotEmptyAfterTrim(requestParams.get(START_TIME))) {
            Date startTime = DateUtil.strToDateTime(requestParams.get(START_TIME));
            query.startedAfter(startTime);
        }
        if (CommonUtil.isNotEmptyAfterTrim(requestParams.get(FlowableConstant.PROCESS_INSTANCE_ID))) {
            query.processInstanceId(requestParams.get(FlowableConstant.PROCESS_INSTANCE_ID));
        }
        if (CommonUtil.isNotEmptyAfterTrim(requestParams.get(FlowableConstant.PROCESS_INSTANCE_NAME))) {
            query.processInstanceNameLike(ObjectUtils.convertToLike(requestParams.get(FlowableConstant.PROCESS_INSTANCE_NAME)));
        }
        if (CommonUtil.isNotEmptyAfterTrim(requestParams.get(FlowableConstant.PROCESS_DEFINITION_NAME))) {
            query.processDefinitionName(requestParams.get(FlowableConstant.PROCESS_DEFINITION_NAME));
        }
        if (CommonUtil.isNotEmptyAfterTrim(requestParams.get(FlowableConstant.PROCESS_DEFINITION_KEY))) {
            query.processDefinitionKey(requestParams.get(FlowableConstant.PROCESS_DEFINITION_KEY));
        }
        if (CommonUtil.isNotEmptyAfterTrim(requestParams.get(FlowableConstant.PROCESS_DEFINITION_ID))) {
            query.processDefinitionId(requestParams.get(FlowableConstant.PROCESS_DEFINITION_ID));
        }
        if (CommonUtil.isNotEmptyAfterTrim(requestParams.get(FlowableConstant.BUSINESS_KEY))) {
            query.processInstanceBusinessKey(requestParams.get(FlowableConstant.BUSINESS_KEY));
        }
        if (CommonUtil.isNotEmptyAfterTrim(requestParams.get(FlowableConstant.START_TIME))) {
            String startTimeStr = requestParams.get(FlowableConstant.START_TIME);
            Date startTime = null;
            if (!BootAppUtil.isNullOrEmpty(startTimeStr)) {
                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                ParsePosition pos = new ParsePosition(0);
                startTime = formatter.parse(startTimeStr, pos);
            }
            query.startedAfter(startTime);
        }
        if (CommonUtil.isNotEmptyAfterTrim(requestParams.get(FlowableConstant.INVOLVED_USER))) {
            query.involvedUser(requestParams.get(FlowableConstant.INVOLVED_USER));
        }
        if (CommonUtil.isNotEmptyAfterTrim(requestParams.get(FlowableConstant.FINISHED))) {
            boolean isFinished = ObjectUtils.convertToBoolean(requestParams.get(FlowableConstant.FINISHED));
            if (isFinished) {
                query.finished();
            } else {
                query.unfinished();
            }
        }
        if (CommonUtil.isNotEmptyAfterTrim(requestParams.get(FlowableConstant.SUPER_PROCESS_INSTANCE_ID))) {
            query.superProcessInstanceId(requestParams.get(FlowableConstant.SUPER_PROCESS_INSTANCE_ID));
        }
        if (CommonUtil.isNotEmptyAfterTrim(requestParams.get(FlowableConstant.EXCLUDE_SUBPROCESSES))) {
            query.excludeSubprocesses(
                    ObjectUtils.convertToBoolean(requestParams.get(FlowableConstant.EXCLUDE_SUBPROCESSES)));
        }
        if (CommonUtil.isNotEmptyAfterTrim(requestParams.get(FlowableConstant.FINISHED_AFTER))) {
            query.finishedAfter(ObjectUtils.convertToDatetime(requestParams.get(FlowableConstant.FINISHED_AFTER)));
        }
        if (CommonUtil.isNotEmptyAfterTrim(requestParams.get(FlowableConstant.FINISHED_BEFORE))) {
            query.finishedBefore(ObjectUtils.convertToDatetime(requestParams.get(FlowableConstant.FINISHED_BEFORE)));
        }
        if (CommonUtil.isNotEmptyAfterTrim(requestParams.get(FlowableConstant.STARTED_AFTER))) {
            query.startedAfter(ObjectUtils.convertToDatetime(requestParams.get(FlowableConstant.STARTED_AFTER)));
        }
        if (CommonUtil.isNotEmptyAfterTrim(requestParams.get(FlowableConstant.STARTED_BEFORE))) {
            query.startedBefore(ObjectUtils.convertToDatetime(requestParams.get(FlowableConstant.STARTED_BEFORE)));
        }
        if (CommonUtil.isNotEmptyAfterTrim(requestParams.get(FlowableConstant.STARTED_BY))) {
            query.startedBy(requestParams.get(FlowableConstant.STARTED_BY));
        }
        //TODO 牛昌注释掉下列代码: 此处代码存在问题，starteBy对应的应该是 ACT_HI_PROCINST.START_USER_ID_ 字段，但是这里获取的事开始时间因此提出疑问
//        if (CommonUtil.isNotEmptyAfterTrim(requestParams.get(FlowableConstant.START_TIME))) {
//            query.startedBy(requestParams.get(FlowableConstant.START_TIME));
//        }
        // startByMe 覆盖 startedBy
        if (CommonUtil.isNotEmptyAfterTrim(requestParams.get(FlowableConstant.START_BY_ME))) {
            boolean isStartByMe = ObjectUtils.convertToBoolean(requestParams.get(FlowableConstant.START_BY_ME));
            if (isStartByMe) {
                query.startedBy(LoginUtil.getOperatorId());
            }
        }
        if (CommonUtil.isNotEmptyAfterTrim(requestParams.get(FlowableConstant.TENANT_ID))) {
            query.processInstanceTenantIdLike(requestParams.get(FlowableConstant.TENANT_ID));
        }
        requestParams.put(Constants.ORDER_RULE, "endTime|ASC,startTime|DESC");
        FlowablePage page = pageList(requestParams, query, ProcInsListWrapper.class, allowedSortProperties,
                HistoricProcessInstanceQueryProperty.START_TIME);
        resultObj.setData(page);
        return resultObj;
    }

    @ApiOperation(value = "查询我的", notes = "[author:10027705]", httpMethod = "GET")
    @GetMapping(value = "/listMyInvolved")
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public JsonResultVo listMyInvolved(@RequestParam Map<String, String> requestParams) {
        requestParams.put(FlowableConstant.INVOLVED_USER, LoginUtil.getOperatorId());
        if (!requestParams.containsKey(START_TIME)) {
            // 如果没有传递时间，默认30天内的数据
            requestParams.put(START_TIME, DateFormatUtils.format(getDate(-30), DateUtil.DATETIME_FORMAT_DEFAULT));
        }
        return list(requestParams);
    }


    private Date getDate(int day) {
        Date date = new Date();//取时间
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date); //需要将date数据转移到Calender对象中操作
        calendar.add(calendar.DATE, day);//把日期往后增加n天.正数往后推,负数往前移动
        return calendar.getTime();   //这个时间就是日期往后推一天的结果
    }

    @ApiOperation(value = "按id查询", notes = "[author:10027705]", httpMethod = "GET")
    @GetMapping(value = "/queryById")
    public JsonResultVo queryById(@RequestParam String processInstanceId) {
        JsonResultVo resultObj = new JsonResultVo();
        permissionService.validateReadPermissionOnProcessInstance(LoginUtil.getOperatorId(), processInstanceId);
        ProcessInstance processInstance = null;
        HistoricProcessInstance historicProcessInstance = processInstanceService
                .getHistoricProcessInstanceById(processInstanceId);
        if (historicProcessInstance.getEndTime() == null) {
            processInstance = processInstanceService.getProcessInstanceById(processInstanceId);
        }
        ProcessInstanceDetailResponse pidr = responseFactory
                .createProcessInstanceDetailResponse(historicProcessInstance, processInstance);
        resultObj.setData(pidr);
        return resultObj;
    }

    @ApiOperation(value = "启动流程实例", notes = "[author:10027705]", httpMethod = "POST")
    @PostMapping(value = "/start")
    @Transactional(rollbackFor = Exception.class)
    public JsonResultVo start(@RequestBody ProcessInstanceRequest processInstanceRequest) {
        JsonResultVo resultObj = new JsonResultVo();
        if (null != processInstanceRequest.getValues()) {
            String formValue = processInstanceRequest.getValues().get(PROCESS_INSTANCE_FORM_DATA).toString();
            Object params = EscapeUnescape.unescape(formValue);
            if (null != params) {
                processInstanceRequest.getValues().put(PROCESS_INSTANCE_FORM_DATA, params);
                processInstanceRequest.getValues().put(FlowableConstant.FLOWABLE_SKIP_EXPRESSION_ENABLED, false);
                processInstanceRequest.getValues().put("skip", false);
                JSONObject value = (JSONObject) JSONObject.parse(params.toString());
                if (null != value.get("input_PKey")) {
                    processInstanceRequest.setBusinessKey(value.get("input_PKey").toString());
                }
            }
        }
        processInstanceService.start(processInstanceRequest);
        return resultObj;
    }

    @ApiOperation(value = "批量启动流程实例", notes = "[author:10027705]", httpMethod = "POST")
    @PostMapping(value = "/batchStart")
    @Transactional(rollbackFor = Exception.class)
    public JsonResultVo batchStart(@RequestBody ProcessInstanceRequest processInstanceRequest) {
        JsonResultVo resultObj = new JsonResultVo();
        String formValue = processInstanceRequest.getValues().get(PROCESS_INSTANCE_FORM_DATA).toString();
        Object params = EscapeUnescape.unescape(formValue);
        if (null != params) {
            processInstanceRequest.getValues().put(PROCESS_INSTANCE_FORM_DATA, params);
        }
        // 测试启动100个流程实例
        // 获取流程，取定义的key
        ProcessDefinition processDefinition = processDefinitionService.getProcessDefinitionById(CommonUtil.trimToEmptyStr(processInstanceRequest.getProcessDefinitionId()));
        List<String> businessKeys = new ArrayList<>();
        Integer failNum = 0;
        for (int i = 0, len = 100; i < len; i++) {
            // 随机生成businessKey
            String businessKey = processDefinition.getKey() + "_" + (i + 1) + (Math.random() * 100 * (i + 1));
            try {
                processInstanceRequest.setBusinessKey(businessKey);
                processInstanceRequest.getValues().put(FlowableConstant.FLOWABLE_SKIP_EXPRESSION_ENABLED, false);
                processInstanceRequest.getValues().put("skip", false);
                processInstanceService.start(processInstanceRequest);
                businessKeys.add(businessKey);
            } catch (Exception e) {
                failNum += 1;
                // 第" + i + "个流程启动有问题，生成的businessKey为：" + businessKey + "，报错信息为：
                String message = i18nUtil.getMessage("ERR.wf.ProcessInstanceController.processStartupError", String.valueOf(i), businessKey);
                businessKeys.add(message + e.getMessage());
            }
        }
        if (failNum != 0) {
            String message = i18nUtil.getMessage("ERR.wf.ProcessInstanceController.processStartupFail");
            resultObj.setMsg(failNum + message);
        }
        resultObj.setData(businessKeys);
        return resultObj;
    }

    @ApiOperation(value = "删除流程实例", notes = "[author:10027705]", httpMethod = "POST")
    @PostMapping(value = "/deleteInit")
    public JsonResultVo stopInit(@RequestBody ProcessInstanceRequest processInstanceRequest) {
        String message = processInstanceService.stopProcessInstance(processInstanceRequest.getProcessInstanceId());
        JsonResultVo resultObj = new JsonResultVo();
        if (CommonUtil.isNotEmptyAfterTrim(message)) {
            resultObj.setMsg(message);
        } else {
            String processStopSuccess = i18nUtil.getMessage("ERR.wf.ProcessInstanceController.processStopSuccess");
            resultObj.setMsg(processStopSuccess);
        }
        return resultObj;
    }

    @ApiOperation(value = "挂起流程实例", notes = "[author:10027705]", httpMethod = "POST")
    @PostMapping(value = "/suspend")
    public JsonResultVo suspend(@RequestBody ProcessInstanceRequest processInstanceRequest) {
        JsonResultVo resultObj = new JsonResultVo();
        processInstanceService.suspend(processInstanceRequest.getProcessInstanceId());
        return resultObj;
    }

    @ApiOperation(value = "激活流程实例", notes = "[author:10027705]", httpMethod = "POST")
    @PostMapping(value = "/activate")
    public JsonResultVo activate(@RequestBody ProcessInstanceRequest processInstanceRequest) {
        JsonResultVo resultObj = new JsonResultVo();
        processInstanceService.activate(processInstanceRequest.getProcessInstanceId());
        return resultObj;
    }

    @ApiOperation(value = "评论", notes = "[author:10027705]", httpMethod = "POST")
    @PostMapping(value = "/comments")
    public JsonResultVo comments(@RequestBody Map<String, String> requestParams) {
        JsonResultVo resultObj = new JsonResultVo();
        String processInstanceId = requestParams.get("processInstanceId");
        if (!CommonUtil.isNotEmptyAfterTrim(processInstanceId)) {
            //根据businessKey与processDefKey查processInstanceId
            resultObj = list(requestParams);
            if (null != resultObj.getData()) {
                List resultRecords = ((FlowablePage) resultObj.getData()).getRecords();
                if (!resultRecords.isEmpty()) {
                    processInstanceId = ((HistoricProcessInstanceResponse) resultRecords.get(0)).getId();
                } else {
                    processInstanceId = "";
                }
            } else {
                processInstanceId = "";
            }
        }
        if (!BootAppUtil.isNullOrEmpty(processInstanceId)) {
            //permissionService.validateReadPermissionOnProcessInstance(LoginUtil.getOperatorId(), processInstanceId);
            List<Comment> datas = taskService.getProcessInstanceComments(processInstanceId);
            List commentEntityImpls = null;
            if (datas != null && !datas.isEmpty()) {

                List<Comment> resultList = new ArrayList<>();
                if(multiFlag) {
                    for (Comment com : datas) {
                        //根据commentId查询 拓展表信息的  code 与 PROCESS_DEF_KEY
                        //QueryWrapper<ActHiCommentCodeDO> queryWrapper = new QueryWrapper<>();
                        String Vtext = processInstanceService.getButtonLangText(com.getId());
                        if (!"".equals(Vtext)) {
                            com.setType(Vtext);
                        }
                        resultList.add(com);
                    }
                }else{
                    resultList.addAll(datas);
                }


                try {
                    commentEntityImpls = resultList.stream().map(mapper -> (CommentEntityImpl) mapper).collect(Collectors.toList());
                } catch (Exception e) {
                    log.info("---error--"+"获取信息异常！", e);
                }
                Collections.reverse(commentEntityImpls);
                resultObj.setData(listWrapper(CommentListWrapper.class, commentEntityImpls));
            } else {
                resultObj.setData(new ArrayList<>());
            }
        } else {
            //说明可能是迁移过来的finish流程，根据流程定义key和业务主键key去附加comment表中查找
            List<ActHistoryCommentDO> commentDOS = processInstanceService.getHistoryComments(requestParams.get(FlowableConstant.PROCESS_DEFINITION_KEY), requestParams.get(FlowableConstant.BUSINESS_KEY));
            resultObj.setData(listWrapper(CommentListWrapper.class, commentDOS));
        }
        return resultObj;
    }

    @ApiOperation(value = "格式化数据", notes = "[author:10027705]", httpMethod = "GET")
    @GetMapping(value = "/formData")
    @SuppressWarnings("squid:S3824")
    public JsonResultVo formData(@RequestParam Map requestParams) {
        String processInstanceId = requestParams.get("processInstanceId").toString();
        String taskId = "";
        if (ObjectUtils.isNotEmpty(requestParams.get("taskId"))) {
            taskId = requestParams.get("taskId").toString();
        }
        JsonResultVo resultObj = new JsonResultVo();
        HistoricProcessInstance processInstance = permissionService
                .validateReadPermissionOnProcessInstance(LoginUtil.getOperatorId(), processInstanceId);
        Object renderedStartForm = formService.getRenderedStartForm(processInstance.getProcessDefinitionId());
        String processDefPkey = processInstance.getProcessDefinitionKey();
        String formUrl = "";
        if (renderedStartForm == null) {
            WfConf wfConf = wfConfService.getWfConfByWF(processDefPkey);
            if (wfConf != null) {
                formUrl = wfConf.getVbillRoute();
            }
        }
        Map<String, Object> variables = null;
        if (processInstance.getEndTime() == null) {
            variables = runtimeService.getVariables(processInstanceId);
        } else {
            List<HistoricVariableInstance> hisVals = historyService.createHistoricVariableInstanceQuery()
                    .processInstanceId(processInstanceId).list();
            variables = new HashMap<>(16);
            for (HistoricVariableInstance variableInstance : hisVals) {
                variables.put(variableInstance.getVariableName(), variableInstance.getValue());
            }
        }
        Map<String, Object> ret = new HashMap<>(4);
        boolean showBusinessKey = isShowBusinessKey(processInstance.getProcessDefinitionId());
        ret.put("showBusinessKey", showBusinessKey);
        ret.put(FlowableConstant.BUSINESS_KEY, processInstance.getBusinessKey());
        if (ObjectUtils.isNotEmpty(taskId)) {
            Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
            Object renderedTaskForm;
            if (null != task) {
                renderedTaskForm = formService.getRenderedTaskForm(taskId);
                if (null != renderedTaskForm) {
                    ret.put("renderedTaskForm", EscapeUnescape.escape(renderedTaskForm.toString()));
                }
            }
        }
        if (renderedStartForm != null) {
            ret.put("renderedStartForm", EscapeUnescape.escape(renderedStartForm.toString()));
            if (ObjectUtils.isNotEmpty(variables.get(PROCESS_INSTANCE_FORM_DATA))) {
                variables.put(PROCESS_INSTANCE_FORM_DATA, EscapeUnescape.escape(variables.get(PROCESS_INSTANCE_FORM_DATA).toString()));
            }
            ret.put("variables", variables);
        } else {
            ret.put("variables", variables);
            ret.put("formUrl", formUrl);
        }
        resultObj.setData(ret);
        return resultObj;
    }

    @ApiOperation(value = "删除", notes = "[author:10027705]", httpMethod = "DELETE")
    @DeleteMapping(value = "/delete")
    public Result delete(@RequestParam String processInstanceId, @RequestParam(required = false) boolean cascade,
                         @RequestParam(required = false) String deleteReason) {
        List<String> historicProcessInstanceIds = new ArrayList<>();
        historicProcessInstanceIds.add(processInstanceId);
        processInstanceService.deleteMigrateData(historicProcessInstanceIds);
        return Result.ok();
    }

    @ApiOperation(value = "根据业务码删除", notes = "[author:10027705]", httpMethod = "DELETE")
    @DeleteMapping(value = "/deleteByBusinessKey")
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public JsonResultVo deleteByBusinessKey(@RequestParam String businessKey, @RequestParam String processDefKey,
                                            @RequestParam(required = false) String deleteReason) {
        JsonResultVo<Object> jsonResultVo = new JsonResultVo<>();
        Map<String, String> searchParams = new HashMap<>();
        searchParams.put(FlowableConstant.PROCESS_DEFINITION_KEY, processDefKey);
        searchParams.put(FlowableConstant.BUSINESS_KEY, businessKey);
        JsonResultVo result = list(searchParams);
        if (null != result.getData()) {
            List resultRecords = ((FlowablePage) result.getData()).getRecords();
            if (resultRecords.isEmpty()) {
                // 工作流实例不存在
                String message = i18nUtil.getMessage("ERR.wf.ProcessInstanceController.wfinstanceNonexist");
                jsonResultVo.setMsgErr(message);
                return jsonResultVo;
            } else {
                Map map = JSONObject.parseObject(JSONObject.toJSONString(resultRecords.get(0)), Map.class);
                List<String> historicProcessInstanceIds = new ArrayList<>();
                historicProcessInstanceIds.add(map.get("id").toString());
                processInstanceService.deleteMigrateData(historicProcessInstanceIds);
            }
        }
        return jsonResultVo;
    }

    @ApiOperation(value = "批量删除", notes = "[author:10027705]", httpMethod = "POST")
    @PostMapping(value = "/deleteProcesss")
    public JsonResultVo deleteProcesss(@RequestParam List<String> businessKeys, @RequestParam String processDefKey, @RequestParam boolean allFlag) {
        List<HistoricProcessInstance> historicProcessInstances = historyService.createHistoricProcessInstanceQuery().processDefinitionKey(processDefKey).list();
        JsonResultVo<Object> jsonResultVo = new JsonResultVo<>();
        if (allFlag) {
            PageUtil pageUtil = new PageUtil<String>();
            int pageMaxNum = 999;
            int totalCount = (int) Math.ceil(historicProcessInstances.size() / (float) pageMaxNum);//需要请求次数
            try {
                for (int j = 0; j < totalCount; j++) {
                    List<String> perHistoricProcessInstanceIds = new ArrayList<>();
                    List<HistoricProcessInstance> perHistoricProcessInstances = pageUtil.getPagePageData(j + 1, totalCount, pageMaxNum, historicProcessInstances);
                    for (HistoricProcessInstance historicProcessInstance : perHistoricProcessInstances) {
                        perHistoricProcessInstanceIds.add(historicProcessInstance.getId());
                    }
                    processInstanceService.deleteMigrateData(perHistoricProcessInstanceIds);
                }
                String message = i18nUtil.getMessage("MSG.wf.common.delSuccess");
                jsonResultVo.setMsg(message);
            } catch (Exception ex) {
                String message = i18nUtil.getMessage("ERR.wf.deleteProcesss.exception", processDefKey, ex.getMessage());
                jsonResultVo.setMsgErr(message);
            }
        } else {
            List<String> historicProcessInstanceIds = new ArrayList<>();
            StringBuilder msgList = new StringBuilder();
            for (HistoricProcessInstance historicProcessInstance : historicProcessInstances) {
                if (businessKeys.contains(historicProcessInstance.getBusinessKey())) {
                    historicProcessInstanceIds.add(historicProcessInstance.getId());
                }
            }
            try {
                processInstanceService.deleteMigrateData(historicProcessInstanceIds);
                String message = i18nUtil.getMessage("MSG.wf.common.saveSuccess");
                jsonResultVo.setMsg(message);
            } catch (Exception ex) {
                jsonResultVo.setData(businessKeys);
                String message = i18nUtil.getMessage("ERR.wf.deleteProcesss.exception", processDefKey, ex.getMessage());
                msgList.append(message);
                log.info("---error--"+message);
            }
        }
        return jsonResultVo;
    }

    @ApiOperation(value = "下载附件", notes = "[author:10027705]", httpMethod = "GET")
    @GetMapping(value = "/downloadAttachment")
    public void downloadAttachment(@RequestParam String taskId, @RequestParam String commentId,
                                   @RequestParam String attachmentName, HttpServletResponse response) {
        //下载审批附件
        List<Attachment> attachments = taskService.getTaskAttachments(taskId);
        for (Attachment item : attachments) {
            if (item.getName().equals(attachmentName)) {
                InputStream resource = taskService.getAttachmentContent(item.getId());
                try {
                    response.setCharacterEncoding("UTF-8");
                    response.setContentType("application/x-download");
                    attachmentName = new String(attachmentName.getBytes(), StandardCharsets.ISO_8859_1);
                    response.setHeader("Content-Disposition", "attachment; filename=" + attachmentName);
                    OutputStream ouputStream = response.getOutputStream();
                    byte[] resourceByte = input2byte(resource);
                    ouputStream.write(resourceByte);
                    response.setHeader("Content-Length", String.valueOf(resourceByte.length));
                    ouputStream.flush();
                    ouputStream.close();
                } catch (IOException e) {
                    log.info("---error--"+"downloadAttachment:", e);
                    throw new FlowableException("Process instance image read error", e);
                }
            }
        }
    }

    private List getProcessInstance(String processDefinitionKey, String businessKey) {
        Map requestParams = new HashMap();
        requestParams.put(FlowableConstant.PROCESS_DEFINITION_KEY, processDefinitionKey);
        requestParams.put(FlowableConstant.BUSINESS_KEY, businessKey);
        JsonResultVo resultObj = list(requestParams);
        if (null != resultObj.getData()) {
            return ((FlowablePage) resultObj.getData()).getRecords();
        } else {
            return new ArrayList();
        }
    }

    @ApiOperation(value = "获取工作流实例", notes = "[author:10027705]", httpMethod = "POST")
    @PostMapping(value = "/hasProcessInstance")
    public JsonResultVo hasProcessInstance(@RequestParam String processDefinitionKey, @RequestParam String businessKey) {
        JsonResultVo resultObj = new JsonResultVo();
        List processInsList = getProcessInstance(processDefinitionKey, businessKey);
        resultObj.setData(!processInsList.isEmpty());
        return resultObj;
    }

    @ApiOperation(value = "获取流程变量", notes = "[author:10027705]", httpMethod = "POST")
    @PostMapping(value = "/getProcessInstanceVariable")
    public JsonResultVo getProcessInstanceVariable(@RequestParam String processDefinitionKey, @RequestParam String businessKey) {
        JsonResultVo resultObj = new JsonResultVo();
        Map<String, Object> processInsVariables = new HashMap<>();
        List resultRecords = getProcessInstance(processDefinitionKey, businessKey);
        try {
            if (!resultRecords.isEmpty()) {
                HistoricProcessInstanceResponse hisProInstance = (HistoricProcessInstanceResponse) resultRecords.get(0);
                ProcessInstance rpi;     //返回唯一结果集
                rpi = runtimeService    //与正在的任务相关的Service
                        .createProcessInstanceQuery()    //创建流程实例查询对象
                        .processInstanceId(hisProInstance.getId())     //查询条件 -- 流程的实例id(流程的实例id在流程启动后的整个流程中是不改变的)
                        .singleResult();
                if (rpi != null) {
                    processInsVariables = runtimeService.getVariables(hisProInstance.getId());
                } else {
                    List<HistoricVariableInstance> hisVariablelist = historyService.createHistoricVariableInstanceQuery().processInstanceId(hisProInstance.getId()).list();
                    for (HistoricVariableInstance hisVariable : hisVariablelist) {
                        processInsVariables.put(hisVariable.getVariableName(), hisVariable.getValue());
                    }
                }
            }
        } catch (Exception exception) {
            log.info("获取流程变量", exception);
        }
        resultObj.setData(processInsVariables);
        return resultObj;
    }

    @ApiOperation(value = "批量更新流程变量", notes = "[author:10027705]", httpMethod = "POST")
    @PostMapping(value = "/updateVariable")
    public JsonResultVo updateVariable(@RequestParam List<String> businessKeys, @RequestParam String processDefinitionKey, @RequestParam String varName, @RequestParam String varValue) {
        JsonResultVo jsonResultVo = new JsonResultVo();
        StringBuilder msgList = new StringBuilder();
        try {
            for (String businessKey : businessKeys) {
                try {
                    List processInsList = getProcessInstance(processDefinitionKey, businessKey);
                    if (!processInsList.isEmpty()) {
                        HistoricProcessInstanceResponse hisProInstance = (HistoricProcessInstanceResponse) processInsList.get(0);
                        runtimeService.setVariable(hisProInstance.getId(), varName, varValue);
                    }
                } catch (Exception ex) {
                    //业务主键：" + businessKey + "更新流程变量失败！
                    String message = i18nUtil.getMessage("ERR.wf.ProcessInstanceController.processChangeFail", businessKey);
                    msgList.append(message);
                }
            }
        } catch (Exception exception) {
            log.info("获取流程变量", exception);
        }
        jsonResultVo.setMsg(msgList.toString());
        return jsonResultVo;
    }

    @ApiOperation(value = "获取流程变量", notes = "[author:10027705]", httpMethod = "POST")
    @PostMapping(value = "/deleteProcessInstance")
    public JsonResultVo deleteProcessInstance(@RequestParam String processDefinitionKey, @RequestParam String businessKey) {
        JsonResultVo resultObj = new JsonResultVo();
        String msg = "";
        List processInsList = getProcessInstance(processDefinitionKey, businessKey);
        if (!processInsList.isEmpty()) {
            HistoricProcessInstanceResponse rpi = (HistoricProcessInstanceResponse) processInsList.get(0);
            try {
                processInstanceService.delete(rpi.getId(), true, "");
            } catch (Exception ex) {
                String message = i18nUtil.getMessage("ERR.wf.deleteProcessInstance.exception", businessKey, ex.getMessage());
                resultObj.setMsgErr(message);
            }
        }
        return resultObj;
    }

    @ApiOperation(value = "获取工作流实例", notes = "[author:10027705]", httpMethod = "GET")
    @GetMapping(value = "/hasProcessInstance")
    public boolean hasProcessInstance(@RequestParam Map<String, String> requestParams) {
        JsonResultVo resultObj;
        String businessKey = requestParams.get("businessKey");
        String wfCode = requestParams.get("wfCode");
        String processDefinitionKey = requestParams.get(FlowableConstant.PROCESS_DEFINITION_KEY);
        if (BootAppUtil.isNullOrEmpty(processDefinitionKey)) {
            WfConf wfConf = wfConfService.getConditionWfConf(wfCode);
            requestParams.put(FlowableConstant.PROCESS_DEFINITION_KEY, wfConf.getVmodel());
        } else {
            requestParams.put(FlowableConstant.PROCESS_DEFINITION_KEY, processDefinitionKey);
        }
        requestParams.put(FlowableConstant.BUSINESS_KEY, businessKey);
        resultObj = list(requestParams);
        if (null != resultObj.getData()) {
            List resultRecords = ((FlowablePage) resultObj.getData()).getRecords();
            return !resultRecords.isEmpty();
        }
        return false;
    }

    @ApiOperation(value = "获取流程定义所有节点按钮API", notes = "[author:10027705]", httpMethod = "POST")
    @PostMapping(value = "/getProcessButtons")
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public JsonResultVo getProcessButtons(@RequestBody Map<String, Object> requestParams) {
        JsonResultVo resultObj = new JsonResultVo();
        String processDefKey = requestParams.get(FlowableConstant.PROCESS_DEFINITION_KEY).toString();
        String transCode = requestParams.get(TRANS_CODE).toString();
        List list = processInstanceService.getProcessButtons(processDefKey, transCode);
        resultObj.setData(list);
        return resultObj;
    }

    /**
     * @description API获取流程实例当前节点按钮
     * <AUTHOR>
     * @date 2020/8/20 19:11
     */
    @ApiOperation(value = "获取流程实例当前节点按钮API", notes = "[author:10027705]", httpMethod = "POST")
    @PostMapping(value = "/getProcessTaskButtons")
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public JsonResultVo getProcessTaskButtons(@RequestBody Map<String, Object> requestParams) {
        JsonResultVo resultObj = new JsonResultVo();
        List<String> businessKeys = (List<String>) requestParams.get("businessKeys");
        String processDefKey = requestParams.get(FlowableConstant.PROCESS_DEFINITION_KEY).toString();
        String transCode = requestParams.get(TRANS_CODE).toString();
        // 构建查询参数
        Map<String, String> searchParams = new HashMap<>();
        searchParams.put(FlowableConstant.PROCESS_DEFINITION_KEY, processDefKey);
        // 判断key是否是多个
        if (!businessKeys.isEmpty()) {
            Map<String, Object> map = new HashMap<>();
            for (int i = 0, len = businessKeys.size(); i < len; i++) {
                String businessKey = businessKeys.get(i);
                searchParams.put(FlowableConstant.BUSINESS_KEY, businessKey);
                JsonResultVo result = list(searchParams);
                if (null != result.getData()) {
                    List resultRecords = ((FlowablePage) result.getData()).getRecords();
                    if (resultRecords.isEmpty()) {
                        map.put(businessKey, new ArrayList<>());
                    } else {
                        List buttons = processInstanceService.handleProcessInstanceTaskButtons(resultRecords.get(0), transCode);
                        if (!buttons.isEmpty()) {
                            map.put(businessKey, buttons);
                        }
                    }
                }
            }
            resultObj.setData(map);
        }
        return resultObj;
    }

    /**
     * @description 获取节点按钮API
     * <AUTHOR>
     * @date 2020/8/20 19:11
     */
    @ApiOperation(value = "获取节点按钮API", notes = "[author:10027705]", httpMethod = "POST")
    @PostMapping(value = "/getProcessTaskButtonsByTaskId")
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public JsonResultVo getProcessTaskButtonsByTaskId(@RequestBody Map<String, Object> requestParams) {
        JsonResultVo resultObj = new JsonResultVo();
        Map buttons = processInstanceService.getTaskButtonsByTaskId(requestParams.get("taskId").toString());
        resultObj.setData(buttons);
        return resultObj;
    }

    @ApiOperation(value = "获取流程出线", notes = "[author:10027705]", httpMethod = "POST")
    @PostMapping(value = "/getProcessTaskOutgoing")
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public JsonResultVo getProcessTaskOutgoing(@RequestBody Map<String, Object> requestParams) {
        JsonResultVo resultObj;
        String processDefKey = requestParams.get(PROCESS_DEF_KEY).toString();
        String transCode = requestParams.get(TRANS_CODE).toString();
        resultObj = processInstanceService.getProcessTaskOutgoing(processDefKey, transCode);
        return resultObj;
    }

    @ApiOperation(value = "获取流程入线", notes = "[author:10027705]", httpMethod = "POST")
    @PostMapping(value = "/getProcessTaskIncoming")
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public JsonResultVo getProcessTaskIncoming(@RequestBody Map<String, Object> requestParams) {
        JsonResultVo resultObj;
        String processDefKey = requestParams.get(PROCESS_DEF_KEY).toString();
        String transCode = requestParams.get(TRANS_CODE).toString();
        resultObj = processInstanceService.getProcessTaskIncoming(processDefKey, transCode, 0);
        return resultObj;
    }

    @ApiOperation(value = "获取流程入线", notes = "[author:10027705]", httpMethod = "POST")
    @PostMapping(value = "/getProcessTaskIncomingByAuth")
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public JsonResultVo getProcessTaskIncomingByAuth(@RequestBody Map<String, Object> requestParams) {
        JsonResultVo resultObj;
        String processDefKey = requestParams.get(PROCESS_DEF_KEY).toString();
        String transCode = requestParams.get(TRANS_CODE).toString();
        resultObj = processInstanceService.getProcessTaskIncoming(processDefKey, transCode, 1);
        return resultObj;
    }

    @ApiOperation(value = "启动流程实例", notes = "[author:10027705]", httpMethod = "POST")
    @PostMapping(value = "/startBizWorkFlow")
    public JsonResultVo startBizWorkFlow(@RequestParam String businessKey,
                                         @RequestParam String wfCode, @RequestBody Map<String, Object> params) {
        JsonResultVo resultObj = new JsonResultVo();
        String processInstanceID = "";
        try {
            params.put("skip", false);
            processInstanceID = processInstanceService.startBizWf(businessKey
                    , wfCode, params, false, LoginUtil.getOperatorId(), null);
            resultObj.setData(processInstanceID);
        } catch (Exception e) {
            String message = i18nUtil.getMessage("ERR.wf.ProcessInstanceController.processStartupFailInfo");
            log.info("---error--"+message, e);
            resultObj.setCode(500);
            String startErrMsg = i18nUtil.getMessage("ERR.wf.processInstance.startErrMsg");
            resultObj.setMsgErr(businessKey + startErrMsg + e.getMessage());
        }
        return resultObj;
    }

    @ApiOperation(value = "批量启动流程实例", notes = "[author:10027705]", httpMethod = "POST")
    @PostMapping(value = "/batchStartBizWorkFlow")
    public JsonResultVo batchStartBizWorkFlow(@RequestBody List<Map<String, Object>> requestParams) {
        JsonResultVo resultObj = new JsonResultVo();
        int maxThread = 7;
        PageUtil pageUtil = new PageUtil<Map<String, Object>>();
        try {
            int pageMaxNum = (int) Math.ceil(requestParams.size() / (float) maxThread);//需要线程数
            maxThread = Math.min(maxThread, (int) Math.ceil(requestParams.size() * 1.0 / pageMaxNum));
            List<List<Map<String, Object>>> threadList = new ArrayList<>();//线程数组，size就是线程数量
            for (int j = 0; j < maxThread; j++) {
                List<Map<String, Object>> oneThreadDealData = pageUtil.getPagePageData(j + 1, maxThread, pageMaxNum, requestParams);
                threadList.add(oneThreadDealData);
            }
            MultiThread<Map<String, Object>, String> multiThread = new MultiThread<Map<String, Object>, String>(threadList) {
                @Override
                public List<String> outExecute(int currentThread, List<Map<String, Object>> data) {
                    log.debug("当前线程号=" + currentThread);
                    return processInstanceService.startBatchBizWf(data);
                }
            };
            List<String> failBusinessResult = multiThread.getResult();
            resultObj.setData(failBusinessResult);
            if (!failBusinessResult.isEmpty()) {
                resultObj.setCode(500);
                String message = i18nUtil.getMessage("ERR.wf.ProcessInstanceController.processStartupFailInfo");
                resultObj.setMsgErr(message + StringUtils.join(failBusinessResult, ","));
            }
        } catch (Exception e) {
            resultObj.setCode(500);
            String message = i18nUtil.getMessage("ERR.wf.ProcessInstanceController.processStartupFailInfo");
            resultObj.setMsgErr(message + e.getMessage());
            String processStartupFailInfo = i18nUtil.getMessage("ERR.wf.ProcessInstanceController.processStartupFailInfo");
            log.info("---error--"+processStartupFailInfo, e);
        }
        return resultObj;
    }

    @ApiOperation(value = "启动流程实例", notes = "[author:10027705]", httpMethod = "POST")
    @PostMapping(value = "/startWorkFlow")
    public JsonResultVo startWorkFlow(@RequestParam String businessKey,
                                      @RequestParam String processDefKey, @RequestBody Map<String, Object> params) {
        JsonResultVo resultObj = new JsonResultVo();
        String processInstanceID = "";
        try {
            params.put("skip", false);
            processInstanceID = processInstanceService.startBizWf(businessKey
                    , processDefKey, params, true, LoginUtil.getOperatorId(), null);
            resultObj.setData(processInstanceID);
        } catch (Exception e) {
            log.info("---error--"+"流程启动：", e);
            resultObj.setCode(500);
            String message = i18nUtil.getMessage("ERR.wf.processInstance.startErrMsg");
            resultObj.setMsgErr(businessKey + message + e.getMessage());
        }
        return resultObj;
    }

    @ApiOperation(value = "启动流程实例", notes = "[author:10027705]", httpMethod = "POST")
    @PostMapping(value = "/startWorkFlowByOprator")
    public JsonResultVo startWorkFlowByOprator(@RequestParam String businessKey,
                                               @RequestParam String processDefKey, @RequestParam String oprator,
                                               @RequestParam String oprDateString, @RequestBody Map<String, Object> params) {
        JsonResultVo resultObj = new JsonResultVo();
        String processInstanceID = "";
        try {
            params.put("skip", false);
            Date messageTime = null;
            if (!BootAppUtil.isNullOrEmpty(oprDateString)) {
                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSSSSS");
                ParsePosition pos = new ParsePosition(0);
                messageTime = formatter.parse(oprDateString, pos);
            }
            processInstanceID = processInstanceService.startBizWf(businessKey
                    , processDefKey, params, true, oprator, messageTime);
            runtimeService.setVariable(processInstanceID, FlowableConstant.NO_EXEC_LISTENER, true);
            resultObj.setData(processInstanceID);
        } catch (Exception e) {
            log.info("---error--"+"流程启动：", e);
            resultObj.setCode(500);
            String message = i18nUtil.getMessage("ERR.wf.processInstance.startErrMsg");
            resultObj.setMsgErr(businessKey + message + e.getMessage());
        }
        return resultObj;
    }

    private byte[] input2byte(InputStream inStream)
            throws IOException {
        ByteArrayOutputStream swapStream = new ByteArrayOutputStream();
        byte[] buff = new byte[4096];
        int rc = 0;
        while ((rc = inStream.read(buff, 0, 100)) > 0) {
            swapStream.write(buff, 0, rc);
        }
        return swapStream.toByteArray();
    }
}

