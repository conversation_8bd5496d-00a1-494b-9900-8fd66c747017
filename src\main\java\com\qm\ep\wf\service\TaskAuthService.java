package com.qm.ep.wf.service;

import com.qm.ep.wf.domain.bean.TaskAuthDO;
import com.qm.ep.wf.domain.dto.ProcessCommonButtonsDTO;
import com.qm.ep.wf.domain.dto.TaskAuthDTO;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.service.IQmBaseService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-17
 */
public interface TaskAuthService extends IQmBaseService<TaskAuthDO> {

    List procDefList(List list);

    boolean judgeTaskAuthForUser(String taskName, String actionCode, String processDefKey, String buttonCode, String transCode);

    QmPage<ProcessCommonButtonsDTO> searchButtonsTwo(TaskAuthDTO tempDTO);

    boolean removeByMapForRedis(Map map);

    boolean saveOrUpdateBatchForRedis(List<TaskAuthDO> list);

    JsonResultVo<TaskAuthDO> clearAuth(List<TaskAuthDTO> tempDTOS);

    /**
     * 根据流程定义ID 清理权限缓存
     *
     * @param processDefinitionId
     * @return
     */
    JsonResultVo clearAuthByProcessDefinitionId(String processDefinitionId);

    JsonResultVo<String> selectButtonAuth(String taskName, String processDefKey, String buttonCode);

    List<TaskAuthDO> selectTaskAuthByFiveParams(String taskName, String transCode, String processDefKey, String buttonCode, String actionCode);
}
