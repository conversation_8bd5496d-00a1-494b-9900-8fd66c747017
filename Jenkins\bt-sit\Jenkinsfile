#!/usr/bin/env groovy
properties(
        [parameters(
                [gitParameter(branch: "",
                         branchFilter: "origin/(.*)",
                         defaultValue: "master",
                         description: "",
                         name: "BR<PERSON><PERSON>",
                         quickFilterEnabled: false,
                         selectedValue: "NONE",
                         sortMode: "NONE",
                         tagFilter: "*",
                         type: "PT_BRANCH")
                ]
          )
        ])

node("master") {
        withEnv(["PATH+EXTRA=/usr/sbin:/usr/bin:/sbin:/bin:/opt/apache-maven-3.5.4/bin"]) {
        stage("checkout") {
            echo "======检出代码分支名称======:" + "${params.BRANCH}"
            git branch: "${params.BRANCH}", credentialsId: 'c76172c2-95f4-4911-be62-ad176ce08a1c', url: 'http://*************/spring/common-service-wf.git'
          }
        stage("build") {
            echo "======进行代码构建======:"
            withMaven(jdk: "JDK  8u181", maven: "Maven 3.5.4") {
            sh 'mvn -U clean install -Dmaven.test.skip=true'
            }
        }
        stage("merge") {
        echo "======合并uat到prod分支======:"
        sh '''
         git push  https://zhangchun_qm:<EMAIL>/bt-ep/common-wf.git master:sit
         ''' 
        }
       
    }

}
