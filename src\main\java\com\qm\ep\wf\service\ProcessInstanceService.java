package com.qm.ep.wf.service;

import com.qm.ep.wf.domain.bean.ActHistoryCommentDO;
import com.qm.ep.wf.domain.bean.ProcessCommonButtonsDO;
import com.qm.ep.wf.domain.vo.HistoricProcessInstanceResponse;
import com.qm.ep.wf.domain.vo.ProcessInstanceRequest;
import com.qm.ep.wf.domain.vo.RoleVO;
import com.qm.tds.api.domain.JsonResultVo;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.runtime.ProcessInstance;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> @date
 */
public interface ProcessInstanceService {
    /**
     * 查询单一流程实例
     *
     * @param processInstanceId
     * @return
     */
    ProcessInstance getProcessInstanceById(String processInstanceId);

    /**
     * 查询单一历史流程实例
     *
     * @param processInstanceId
     * @return
     */
    HistoricProcessInstance getHistoricProcessInstanceById(String processInstanceId);

    /**
     * 启动流程实例
     *
     * @param processInstanceRequest
     */
    String start(ProcessInstanceRequest processInstanceRequest);

    /**
     * 删除流程实例
     *
     * @param processInstanceId
     * @param cascade
     * @param deleteReason
     */
    void delete(String processInstanceId, boolean cascade, String deleteReason);

    /**
     * 激活流程实例
     *
     * @param processInstanceId
     */
    void activate(String processInstanceId);

    /**
     * 挂起流程实例
     *
     * @param processInstanceId
     */
    void suspend(String processInstanceId);

    /**
     * 删除初始流程单据
     *
     * @param processInstanceId
     * @param cascade
     * @param deleteReason
     * @return
     */
    String deleteInit(String processInstanceId, boolean cascade, String deleteReason);

    String stopProcessInstance(String processInstanceId);

    /**
     * 业务模块启动工作流审批
     *
     * @param bizKey
     * @param wfCode
     * @param params *
     */
    String startBizWf(String bizKey, String wfCode, Map<String, Object> params, boolean isProceesDefKey, String oprator, Date oprDate);

    /**
     * 获取当前待办节点按钮
     *
     * @param o
     * @param transCode
     */
    List handleProcessInstanceTaskButtons(Object o, String transCode);

    /**
     * 根据参数查询当前待办节点按钮
     *
     * @param processInstance
     * @param transCode
     * @return Map<String, List < ProcessCommonButtonsDO>></>  key:businessKey , value: List<ProcessCommonButtonsDO>
     */
    Map<String, Collection<ProcessCommonButtonsDO>> handleProcessInstanceTaskButtons(List<HistoricProcessInstanceResponse> processInstance, String transCode);

    /**
     * @description 获取流程出线(outgoing)API
     * <AUTHOR>
     * @date 2020/8/22 10:28
     */
    JsonResultVo getProcessTaskOutgoing(String processDefKey, String transCode);

    /**
     * @description 获取流程入线(incoming)API
     * <AUTHOR>
     * @date 2020/8/22 13:38
     */
    JsonResultVo getProcessTaskIncoming(String processDefKey, String transCode, Integer auth);

    /**
     * @description 获取节点按钮API
     * <AUTHOR>
     * @date 2020/8/25 8:55
     */
    Map getTaskButtonsByTaskId(String taskId);

    /**
     * @description 获取流程定义所有节点按钮
     * <AUTHOR>
     * @date 2020/9/8 14:17
     */
    List getProcessButtons(String processDefKey, String transCode);

    /**
     * @description 根据流程定义Key与业务主键Key获取迁移的审批记录
     */
    List<ActHistoryCommentDO> getHistoryComments(String processDefKey, String businessKey);

    void deleteMigrateData(List<String> processInstanceIds);

    /**
     * 缓存所有用户角色，ttl  60s
     *
     * @return
     */
    Map<String, List<RoleVO>> cacheCurrentAllUserRole();

    /**
     * 缓存当前用户角色，ttl  10s
     */
    void cacheCurrentUserRole();

    List<String> startBatchBizWf(List<Map<String, Object>> requestParams);

    String getButtonLangText(String id);
}
