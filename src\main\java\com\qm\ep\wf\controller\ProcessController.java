package com.qm.ep.wf.controller;

import com.qm.tds.util.I18nUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.engine.*;
import org.flowable.engine.history.HistoricActivityInstance;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.runtime.Execution;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.image.ProcessDiagramGenerator;
import org.flowable.task.api.Task;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/flowable")
@Api(value = "工作流", tags = {"flowable"})
public class ProcessController {

    @Autowired
    private RuntimeService runtimeService;
    @Autowired
    private TaskService taskService;
    @Autowired
    private HistoryService historyService;
    @Autowired
    private RepositoryService repositoryService;
    @Autowired
    private ProcessEngine processEngine;
    @Autowired
    private I18nUtil i18nUtil;
    /**
     * 创建流程
     *
     * @param userId
     * @param days
     * @param reason
     * @return
     */
    @GetMapping("/add")
    @ApiOperation(value = "创建流程", notes = "[author:10027705]")
    public String addExpense(String userId, String days, String reason) {
        Map<String, Object> map = new HashMap<>();
        map.put("employee", userId);
        map.put("nrOfHolidays", days);
        map.put("description", reason);

        ProcessInstance processInstance = runtimeService.startProcessInstanceByKey("holidayRequest", map);
        String message = i18nUtil.getMessage("ERR.wf.ProcessController.submitProcessId");
        return message + processInstance.getId();
    }


    /**
     * 获取指定用户组流程任务列表
     *
     * @param group
     * @return
     */
    @GetMapping("/list")
    @ApiOperation(value = "获取指定用户组流程任务列表", notes = "[author:10027705]")
    public Object list(String group) {
        List<Task> tasks = taskService.createTaskQuery().taskCandidateGroup(group).list();
        return tasks.toString();
    }

    /**
     * 通过/拒绝任务
     *
     * @param taskId
     * @param approved 1 ：true  2：false
     * @return
     */
    @GetMapping("/apply")
    @ApiOperation(value = "通过/拒绝任务", notes = "[author:10027705]")
    public String apply(String taskId, String approved) {
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
        if (task == null) {
            String message = i18nUtil.getMessage("ERR.wf.ProcessController.processNonexist");
            return message;
        }
        Map<String, Object> variables = new HashMap<>();
        Boolean apply = approved.equals("1");
        variables.put("approved", apply);
        taskService.complete(taskId, variables);
        String message = i18nUtil.getMessage("MSG.wf.ProcessController.approvalOrNot");
        return message + approved;
    }

    /**
     * 查看历史流程记录
     *
     * @param processInstanceId
     * @return
     */
    @GetMapping("/historyList")
    @ApiOperation(value = "查看历史流程记录", notes = "[author:10027705]")
    public Object getHistoryList(String processInstanceId) {
        List<HistoricActivityInstance> historicActivityInstances;
        historicActivityInstances = historyService.createHistoricActivityInstanceQuery()
                .processInstanceId(processInstanceId).finished().orderByHistoricActivityInstanceEndTime().asc().list();
        return historicActivityInstances;
    }

    /**
     * 驳回流程实例
     *
     * @param taskId
     * @param targetTaskKey
     * @return
     */
    @GetMapping("/rollbask")
    @ApiOperation(value = "驳回流程实例", notes = "[author:10027705]")
    public String rollbaskTask(String taskId, String targetTaskKey) {
        Task currentTask = taskService.createTaskQuery().taskId(taskId).singleResult();
        if (currentTask == null) {
            String message = i18nUtil.getMessage("ERR.wf.ProcessController.nodeNonexist");
            return message;
        }
        List<String> key = new ArrayList<>();
        key.add(currentTask.getTaskDefinitionKey());

        runtimeService.createChangeActivityStateBuilder()
                .processInstanceId(currentTask.getProcessInstanceId())
                .moveActivityIdsToSingleActivityId(key, targetTaskKey)
                .changeState();
        String message = i18nUtil.getMessage("ERR.wf.ProcessController.rejectSuccess");
        return message;
    }


    /**
     * 终止流程实例
     *
     * @param processInstanceId
     */
    @ApiOperation(value = "终止流程实例", notes = "[author:10027705]")
    public String deleteProcessInstanceById(String processInstanceId) {
        // ""这个参数本来可以写删除原因
        runtimeService.deleteProcessInstance(processInstanceId, "");
        String message = i18nUtil.getMessage("ERR.wf.ProcessController.stopProcessInstanceSuccess");
        return message;
    }

    /**
     * 挂起流程实例
     *
     * @param processInstanceId 当前流程实例id
     */
    @GetMapping("/hangUp")
    @ApiOperation(value = "挂起流程实例", notes = "[author:10027705]")
    public String handUpProcessInstance(String processInstanceId) {
        runtimeService.suspendProcessInstanceById(processInstanceId);
        String message = i18nUtil.getMessage("MSG.wf.ProcessController.pendingProcessSuccess");
        return message;
    }

    /**
     * 恢复（唤醒）被挂起的流程实例
     *
     * @param processInstanceId 流程实例id
     */
    @GetMapping("/recovery")
    @ApiOperation(value = "挂起流程实例", notes = "[author:10027705]")
    public String activateProcessInstance(String processInstanceId) {
        runtimeService.activateProcessInstanceById(processInstanceId);
        String message = i18nUtil.getMessage("MSG.wf.ProcessController.recoveryProcessSuccess");
        return message;
    }

    /**
     * 判断传入流程实例在运行中是否存在
     *
     * @param processInstanceId
     * @return
     */
    @GetMapping("/isExist/running")
    @ApiOperation(value = "判断传入流程实例在运行中是否存在", notes = "[author:10027705]")
    public Boolean isExistProcIntRunning(String processInstanceId) {
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
        return processInstance != null;
    }

    /**
     * 判断流程实例在历史记录中是否存在
     *
     * @param processInstanceId
     * @return
     */
    @GetMapping("/isExist/history")
    @ApiOperation(value = "判断流程实例在历史记录中是否存在", notes = "[author:10027705]")
    public Boolean isExistProcInHistory(String processInstanceId) {
        HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
        return historicProcessInstance != null;
    }

    /**
     * 我发起的流程实例列表
     *
     * @param userId
     * @return 流程实例列表
     */
    @GetMapping("/myTasks")
    @ApiOperation(value = "我发起的流程实例列表", notes = "[author:10027705]")
    public List<HistoricProcessInstance> getMyStartProcint(String userId) {
        List<HistoricProcessInstance> list;
        list = historyService
                .createHistoricProcessInstanceQuery()
                .startedBy(userId)
                .orderByProcessInstanceStartTime()
                .asc()
                .list();
        return list;
    }

    /**
     * 查询流程图
     *
     * @param httpServletResponse
     * @param processId
     * @throws Exception
     */
    @RequestMapping(value = "/processDiagram")
    @ApiOperation(value = "查询流程图", notes = "[author:10027705]")
    public void genProcessDiagram(HttpServletResponse httpServletResponse, String processId) throws IOException {
        ProcessInstance pi = runtimeService.createProcessInstanceQuery().processInstanceId(processId).singleResult();

        //流程走完的不显示图
        if (pi == null) {
            return;
        }
        Task task = taskService.createTaskQuery().processInstanceId(pi.getId()).singleResult();
        //使用流程实例ID，查询正在执行的执行对象表，返回流程实例对象
        String instanceId = task.getProcessInstanceId();
        List<Execution> executions = runtimeService
                .createExecutionQuery()
                .processInstanceId(instanceId)
                .list();

        //得到正在执行的Activity的Id
        List<String> activityIds = new ArrayList<>();
        List<String> flows = new ArrayList<>();
        for (Execution exe : executions) {
            List<String> ids = runtimeService.getActiveActivityIds(exe.getId());
            activityIds.addAll(ids);
        }

        //获取流程图
        BpmnModel bpmnModel = repositoryService.getBpmnModel(pi.getProcessDefinitionId());
        ProcessEngineConfiguration engconf = processEngine.getProcessEngineConfiguration();
        ProcessDiagramGenerator diagramGenerator = engconf.getProcessDiagramGenerator();
        InputStream in = diagramGenerator.generateDiagram(bpmnModel, "png", activityIds, flows, engconf.getActivityFontName(),
                engconf.getLabelFontName(), engconf.getAnnotationFontName(), engconf.getClassLoader(), 1.0, true);

        OutputStream out = null;
        byte[] buf = new byte[1024];
        int legth = 0;
        try {
            out = httpServletResponse.getOutputStream();
            while ((legth = in.read(buf)) != -1) {
                out.write(buf, 0, legth);
            }
        } finally {
            if (in != null) {
                in.close();
            }
            if (out != null) {
                out.close();
            }
        }
    }
}
