package com.qm.ep.wf.remote;


import com.qm.ep.wf.domain.bean.TransCodeDO;
import com.qm.ep.wf.domain.dto.PersonnelDTO;
import com.qm.ep.wf.domain.dto.RoleDTO;
import com.qm.ep.wf.domain.dto.UserDTO;
import com.qm.ep.wf.domain.vo.RoleVO;
import com.qm.ep.wf.domain.vo.UserRoleCodeVO;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import com.qm.tds.api.mp.pagination.QmPage;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
@Data
public class SysFeignRemoteHystrix extends QmRemoteHystrix<SysFeignRemote> implements SysFeignRemote {

    @Override
    public JsonResultVo getRoleListByUserId(@RequestHeader("tenantId") String tenantId, @RequestBody UserDTO userDTO) {
        return getResult();
    }

    @Override
    public JsonResultVo getSysParamValue(@RequestHeader("tenantId") String tenantId, @RequestParam(required = false) String nCompanyId, @RequestParam(required = true) String vParamCode) {
        return getResult();
    }

    @Override
    public JsonResultVo<QmPage<TransCodeDO>> getTransCode(@RequestHeader("tenantId") String tenantId, @RequestBody List<String> codes) {
        return getResult();
    }

    @Override
    public JsonResultVo<Map<String, TransCodeDO>> selectTransAllMap(String tenantId) {
        return getResult();
    }

    @Override
    public JsonResultVo<Map<String, List<UserRoleCodeVO>>> getPersonCodeByRoleCode(String tenantId, String roleCode) {
        return getResult();
    }

    @Override
    public JsonResultVo<List<String>> selectTransCodeByName(String tenantId, String transName) {
        return getResult();
    }

    @Override
    public JsonResultVo getUserList() {
        return getResult();
    }

    @Override
    public JsonResultVo findUserById(String id) {
        return getResult();
    }

    @Override
    public JsonResultVo<String> selectUserIdByCode(String tenantId, List<String> codes) {
        return getResult();
    }

    @Override
    public JsonResultVo<List<RoleVO>> getAllRoles() {
        return getResult();
    }

    @Override
    public JsonResultVo getPersonnelInfo(String tenantId, PersonnelDTO tempDTO) {
        return getResult();
    }

    @Override
    public JsonResultVo getListByRoleId(String tenantId, RoleDTO roleDTO) {
        return getResult();
    }

    @Override
    public JsonResultVo getRoleListByTransCode(String tenantId, String transcode, String buttonCode) {
        return getResult();
    }
}
