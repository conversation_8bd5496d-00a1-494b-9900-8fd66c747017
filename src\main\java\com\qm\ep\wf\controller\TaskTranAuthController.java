package com.qm.ep.wf.controller;

import com.qm.ep.wf.common.BaseFlowableController;
import com.qm.ep.wf.common.FlowablePage;
import com.qm.ep.wf.constant.FlowableConstant;
import com.qm.ep.wf.domain.bean.TaskTranAuthDO;
import com.qm.ep.wf.domain.bean.TransCodeDO;
import com.qm.ep.wf.domain.dto.TaskTranAuthDTO;
import com.qm.ep.wf.service.TaskTranAuthService;
import com.qm.ep.wf.service.WfConfService;
import com.qm.ep.wf.util.ObjectUtils;
import com.qm.ep.wf.wapper.ProcDefListWrapper;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.util.I18nUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.flowable.common.engine.api.FlowableObjectNotFoundException;
import org.flowable.common.engine.api.query.QueryProperty;
import org.flowable.engine.repository.ProcessDefinitionQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * <p>
 * Controller
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-19
 */
@RestController
@RequestMapping("/taskTranAuth")
@Slf4j
public class TaskTranAuthController extends BaseFlowableController {
    private static final Map<String, QueryProperty> ALLOWED_SORT_PROPERTIES = new HashMap<>();
    @Autowired
    private TaskTranAuthService taskTranAuthService;
    @Autowired
    private WfConfService wfConfService;
    @Autowired
    private I18nUtil i18nUtil;

    /**
     * 使用系统默认的保存/修改 方法
     */
    @ApiOperation(value = "使用系统默认的保存/修改 方法", notes = "[author:10027705]")
    @PostMapping("/save")
    public JsonResultVo<TaskTranAuthDO> save(@RequestBody TaskTranAuthDO tempDO) {
        JsonResultVo<TaskTranAuthDO> resultObj = new JsonResultVo<>();
        boolean flag = taskTranAuthService.saveOrUpdate(tempDO);
        if (flag) {
            resultObj.setData(tempDO);
        } else {
            String message = i18nUtil.getMessage("ERR.wf.common.saveFail");
            resultObj.setMsgErr(message);
        }
        return resultObj;
    }

    /**
     * 批量的保存/修改 方法
     */
    @ApiOperation(value = "使用系统默认的保存/修改 方法", notes = "[author:10027705]")
    @PostMapping("/saveBatch")
    public JsonResultVo<TaskTranAuthDO> saveBatch(@RequestBody List<TaskTranAuthDO> list) {
        JsonResultVo<TaskTranAuthDO> resultObj = new JsonResultVo<>();
        boolean flag = taskTranAuthService.saveOrUpdateBatchAndRedis(list);
        if (flag) {
            String message = i18nUtil.getMessage("MSG.wf.common.saveSuccess");
            resultObj.setMsg(message);
        } else {
            String message = i18nUtil.getMessage("ERR.wf.common.saveFail");
            resultObj.setMsgErr(message);
        }
        return resultObj;
    }

    /**
     * 根据传入的id删除数据
     */
    @ApiOperation(value = "根据传入的id删除数据", notes = "[author:10027705]")
    @PostMapping("/deleteById")
    public JsonResultVo deleteById(@RequestBody TaskTranAuthDO tempDO) {
        JsonResultVo resultObj = new JsonResultVo();
        boolean flag = taskTranAuthService.removeById(tempDO.getId());
        if (flag) {
            log.warn("在方法taskTranAuth/deleteById processDefKey:{} ,transCode:{} 被删除", tempDO.getProcessDefKey(), tempDO.getTransCode());
            String message = i18nUtil.getMessage("MSG.wf.common.delSuccess");
            resultObj.setMsg(message);
        } else {
            String message = i18nUtil.getMessage("ERR.wf.common.delFail");
            resultObj.setMsgErr(message);
        }
        return resultObj;
    }

    /**
     * 根据传入的map删除信息
     */
    @ApiOperation(value = "根据传入的map删除信息", notes = "[author:10027705]")
    @PostMapping("/deleteByMap")
    public JsonResultVo deleteByMap(@RequestBody Map map) {
        JsonResultVo resultObj = new JsonResultVo();
        boolean flag = taskTranAuthService.removeByMapAndRedis(map);
        if (flag) {
            log.warn("在方法taskTranAuth/deleteByMap processDefKey:{} ,transCode:{} ,actionCode:{}被删除", map.get("PROCESS_DEF_KEY"), map.get("TRANS_CODE"), map.get("ACTION_CODE"));
            String message = i18nUtil.getMessage("ERR.wf.ProcessCommonButtonsController.operateSuccess");
            resultObj.setMsg(message);
        } else {
            String message = i18nUtil.getMessage("MSG.wf.ProcessCommonButtonsController.operateFail");
            resultObj.setMsgErr(message);
        }
        return resultObj;
    }

    /**
     * 根据传入的实体信息进行查询
     */
    @ApiOperation(value = "根据传入的实体信息进行查询", notes = "[author:10027705]")
    @PostMapping("/table")
    public JsonResultVo<QmPage<TransCodeDO>> table(@RequestBody TaskTranAuthDTO tempDTO) {
        return taskTranAuthService.searchTable(tempDTO);
    }

    @GetMapping(value = "/procDefList")
    @ApiOperation(value = "查询流程定义列表", notes = "[author:10027705]")
    public JsonResultVo procDefList(@RequestParam Map<String, String> requestParams) {
        JsonResultVo resultObj = new JsonResultVo();

        ProcessDefinitionQuery processDefinitionQuery = repositoryService.createProcessDefinitionQuery();
        if (ObjectUtils.isNotEmpty(requestParams.get(FlowableConstant.KEY))) {
            processDefinitionQuery
                    .processDefinitionKeyLike(ObjectUtils.convertToLike(requestParams.get(FlowableConstant.KEY)));
        }
        if (ObjectUtils.isNotEmpty(requestParams.get(FlowableConstant.NAME))) {
            processDefinitionQuery
                    .processDefinitionNameLike(ObjectUtils.convertToLike(requestParams.get(FlowableConstant.NAME)));
        }
        if (ObjectUtils.isNotEmpty(requestParams.get(FlowableConstant.TENANT_ID))) {
            processDefinitionQuery.processDefinitionTenantId(requestParams.get(FlowableConstant.TENANT_ID));
        }
        // 根据模块查询
        if (ObjectUtils.isNotEmpty(requestParams.get(FlowableConstant.MODULE))) {
            Set<String> modules = new HashSet<>();
            try {
                modules = wfConfService.getWfConfByModule(requestParams.get(FlowableConstant.MODULE), 1);
                if (modules.isEmpty()) {
                    modules.add("non");
                }
            } catch (FlowableObjectNotFoundException e) {
                log.debug(e.getMessage(), e);
                modules.add("non");
            }
            processDefinitionQuery.processDefinitionIds(modules);
        }
        processDefinitionQuery.latestVersion();
        FlowablePage page = pageList(requestParams, processDefinitionQuery, ProcDefListWrapper.class,
                ALLOWED_SORT_PROPERTIES);
        List records = taskTranAuthService.procDefList(page.getRecords());
        page.setRecords(records);
        resultObj.setData(page);
        return resultObj;
    }
}
