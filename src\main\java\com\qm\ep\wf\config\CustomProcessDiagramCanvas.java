package com.qm.ep.wf.config;

import com.qm.ep.wf.domain.vo.CustomProcessDiagramCanvasVO;
import org.flowable.image.impl.DefaultProcessDiagramCanvas;

import java.awt.*;
import java.awt.geom.RoundRectangle2D;

/**
 * <AUTHOR> @date
 */

public class CustomProcessDiagramCanvas extends DefaultProcessDiagramCanvas {

    public CustomProcessDiagramCanvas(CustomProcessDiagramCanvasVO vo) {
        super(vo.getWidth(), vo.getHeight(), vo.getMinX(), vo.getMinY(), vo.getImageType(), vo.getActivityFontName(), vo.getLabelFontName(), vo.getAnnotationFontName(),
                vo.getCustomClassLoader());
    }

    public void drawCustomHighLight(int x, int y, int width, int height, Paint paint) {
        Paint originalPaint = g.getPaint();
        Stroke originalStroke = g.getStroke();

        g.setPaint(paint);
        g.setStroke(THICK_TASK_BORDER_STROKE);
        RoundRectangle2D rect = new RoundRectangle2D.Double(x, y, width, height, 20, 20);
        g.draw(rect);

        g.setPaint(originalPaint);
        g.setStroke(originalStroke);
    }

}
