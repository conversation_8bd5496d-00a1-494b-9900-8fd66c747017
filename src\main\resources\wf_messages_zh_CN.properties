#这里填写中文翻译
MSG.wf.common.saveSuccess=保存成功！
ERR.wf.common.saveFail=保存失败！
MSG.wf.common.delSuccess=删除成功！
ERR.wf.common.delFail=删除失败！
MSG.wf.common.updateSuccess=修改成功！
ERR.wf.common.updateFail=修改失败！
ERR.wf.common.isEmpty=%s不能为空
ERR.wf.processCommonButtons.rptItemDO2=存在重复流程代码，请重新输入
MSG.wf.flow.defination=流程定义：
MSG.wf.task.name=任务：
MSG.wf.transaction.code=事务码:
MSG.wf.common.terminator=。
MSG.wf.taskAuth.checkIsLast=删除此权限后，该按钮在当前用例下不存在任何操作者授权信息，即所有有用例权限的用户均有权限！
ERR.wf.deleteProcesss.exception=流程：%s删除失败%s
ERR.wf.deleteProcessInstance.exception=业务主键%s删除失败！原因是：%s
MSG.wf.common.claimSuccess=认领成功！
MSG.wf.common.cancelClaimSuccess=取消认领成功！
ERR.wf.common.failReason=执行失败，原因是：%s
MSG.wf.common.success=成功！
ERR.wf.flowableForm.save=表单：%s已经存在！
ERR.wf.task.errorMessage=失败，原因为：无法找到待办任务！
ERR.wf.task.failMessage=失败，原因为：
ERR.wf.processInstance.startErrMsg=流程启动失败,原因
ERR.wf.processInstanceService.roleListEmpty=角色列表为空。
MSG.wf.copyButton.copySuccess=复制成功
ERR.wf.copyButton.copyFail=复制失败
ERR.wf.copyButton.nonExist=来源流程不存在按钮
ERR.wf.processCounterSignService.customeSaveOrUpdate=当前节点存在重复的会签组策略配置！
MSG.wf.common.operateSuccess=操作成功
ERR.wf.processDefinitionService.definitionExist=流程定义：%s 存在运行实例，无法删除！
ERR.wf.processInstanceService.processEndDelete=流程实例已经结束，不允许删除！
ERR.wf.processInstanceService.checkedProcessDelete=只有未审核的流程，才可以删除！
MSG.wf.common.processInstanceService.endProcessStop=流程实例已经结束，不允许终止！
ERR.wf.processInstanceService.checkedProcessStop=只有未审核的流程实例，才可以终止！
MSG.wf.processInstanceService.definitionTemplateNotFound=未找到工作流模板定义
MSG.wf.flowableTaskService.rollback=退回到%s。
MSG.wf.flowableTaskService.jobFinish=作业完成任务
MSG.wf.flowableTaskService.jobEndFlow=作业结束流程
MSG.wf.flowableTaskService.jobRollback=作业回退任务
MSG.wf.taskAuthService.cleanSuccess=权限清除成功
MSG.wf.common.tradeFail=交易执行失败
##########################################
MSG.wf.CommentTypeEnum.init=初始
MSG.wf.CommentTypeEnum.commit=提交
MSG.wf.CommentTypeEnum.recommit=重新提交
MSG.wf.CommentTypeEnum.claim=认领
MSG.wf.CommentTypeEnum.disclaim=取消认领
MSG.wf.CommentTypeEnum.approve=审批
MSG.wf.CommentTypeEnum.finish=完成
MSG.wf.CommentTypeEnum.return=退回
MSG.wf.CommentTypeEnum.withdraw=撤回
MSG.wf.CommentTypeEnum.tempStorage=暂存
MSG.wf.CommentTypeEnum.turnTo=转办
MSG.wf.CommentTypeEnum.delegate=委派
MSG.wf.CommentTypeEnum.stop=终止
MSG.wf.FlowableConstant.autoReview=自动审核
ERR.wf.FlowableConstant.busiSourceUsing=业务资源占用中，请稍后刷新重试！
ERR.wf.FlowableConstant.sourceUsing=资源占用中，请稍后刷新重试！
ERR.wf.ProcessCommonButtonsController.operateSuccess=操作成功！
MSG.wf.ProcessCommonButtonsController.operateFail=操作失败！
ERR.wf.ProcessController.submitProcessId=提交成功,流程ID为：
ERR.wf.ProcessController.processNonexist=流程不存在
MSG.wf.ProcessController.approvalOrNot=审批是否通过：
ERR.wf.ProcessController.nodeNonexist=节点不存在
ERR.wf.ProcessController.rejectSuccess=驳回成功...
ERR.wf.ProcessController.stopProcessInstanceSuccess=终止流程实例成功
MSG.wf.ProcessController.pendingProcessSuccess=挂起流程成功...
MSG.wf.ProcessController.recoveryProcessSuccess=恢复流程成功...
ERR.wf.ProcessDefinitionController.processIdNull=流程ID不能为空
ERR.wf.ProcessDefinitionController.activateSuccess=激活成功！
ERR.wf.ProcessDefinitionController.pendingSuccess=挂起成功！
ERR.wf.ProcessInstanceController.processStartupError=第%s个流程启动有问题，生成的businessKey为：%s，报错信息为：
ERR.wf.ProcessInstanceController.processStartupFail=个流程启动失败，详见失败信息！
ERR.wf.ProcessInstanceController.processStopSuccess=流程终止成功！
ERR.wf.ProcessInstanceController.wfinstanceNonexist=工作流实例不存在
ERR.wf.ProcessInstanceController.processChangeFail=业务主键：%s更新流程变量失败！
ERR.wf.ProcessInstanceController.processStartupFailInfo=流程启动失败：
ERR.wf.TaskAuthController.processDefine=流程定义：
ERR.wf.TaskAuthController.task=任务：
ERR.wf.TaskAuthController.trancCode=事务码:
ERR.wf.TaskAuthController.button=按钮：
ERR.wf.TaskAuthController.java=这些按钮第一次进行操作者授权，保存后，仅授权用户（角色）具有权限，其他用户不再具有权限！
ERR.wf.TaskController.wfcodeError=工作流代码：%s 业务主键：%s 不存在工作流实例！
ERR.wf.TaskController.failReasonIs=失败，原因是：
ERR.wf.TaskController.batchFail=批量%s失败，请选择相同状态的单据进行处理！";
ERR.wf.TaskController.buttonTaskDiff=失败，原因为：按钮与当前任务不一致，请刷新重试！
ERR.wf.TaskController.taskFailPrompt=%s个任务%s失败！
ERR.wf.TaskController.processing=处理中，请稍后再试！
ERR.wf.TaskController.rejectedBack=您已否决，任务已经退回上一节点
