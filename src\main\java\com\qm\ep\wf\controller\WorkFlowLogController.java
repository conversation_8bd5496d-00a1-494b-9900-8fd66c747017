package com.qm.ep.wf.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qm.ep.wf.domain.bean.WorkFlowLogDO;
import com.qm.ep.wf.domain.dto.WorkFlowLogDTO;
import com.qm.ep.wf.domain.vo.WorkFlowLogVO;
import com.qm.ep.wf.service.WorkFlowLogService;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import com.qm.tds.base.domain.LoginKeyDO;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.I18nUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.Calendar;

/**
 * <p>
 * Controller
 * 导出Excel日志JsonResultVo
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-25
 */
@Api(description = "导出Excel日志", tags = "导出Excel日志")
@RestController
@RequestMapping("/workFlow")
public class WorkFlowLogController extends BaseController {

    @Autowired
    private WorkFlowLogService workFlowLogService;
    @Autowired
    private I18nUtil i18nUtil;

    /**
     * 使用系统默认的保存/修改 方法
     */
    @ApiOperation(value = "使用系统默认的保存/修改 方法", notes = "[author:10027705]")
    @PostMapping("/save")
    public JsonResultVo<WorkFlowLogDO> save(@RequestBody WorkFlowLogDO tempDO) {
        LoginKeyDO loginKey = this.getUserInfo();
        //保存公司id(数据表中没有公司id请自行删除)
        if (BootAppUtil.isNullOrEmpty(tempDO.getId())) {
            tempDO.setNcompanyid(loginKey.getCompanyId());
        }
        JsonResultVo<WorkFlowLogDO> resultObj = new JsonResultVo<>();
        boolean flag = workFlowLogService.saveOrUpdate(tempDO);
        if (flag) {
            resultObj.setData(tempDO);
        } else {
            String message = i18nUtil.getMessage("ERR.wf.common.saveFail");
            resultObj.setMsgErr(message);
        }
        return resultObj;
    }

    /**
     * 根据传入的id删除数据
     */
    @ApiOperation(value = "根据传入的id删除数据", notes = "[author:10027705]")
    @PostMapping("/deleteById")
    public JsonResultVo<WorkFlowLogDO> deleteById(@RequestBody WorkFlowLogDO tempDO) {
        JsonResultVo<WorkFlowLogDO> resultObj = new JsonResultVo<>();
        boolean flag = workFlowLogService.removeById(tempDO.getId());
        if (flag) {
            String message = i18nUtil.getMessage("MSG.wf.common.delSuccess");
            resultObj.setMsg(message);
        } else {
            String message = i18nUtil.getMessage("ERR.wf.common.delFail");
            resultObj.setMsgErr(message);
        }
        return resultObj;
    }

    /**
     * 删除超过三十天的数据
     */
    @ApiOperation(value = "删除超过三十天的数据", notes = "[author:10027705]")
    @PostMapping("/del")
    public JsonResultVo<WorkFlowLogDO> del() {
        JsonResultVo<WorkFlowLogDO> resultObj = new JsonResultVo<>();
        QmQueryWrapper<WorkFlowLogDO> qmQueryWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<WorkFlowLogDO> lambdaQueryWrapper = qmQueryWrapper.lambda();
        Calendar now = Calendar.getInstance();
        now.add(Calendar.DAY_OF_MONTH, -30);
        String time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(now.getTime());
        lambdaQueryWrapper.le(WorkFlowLogDO::getDbegin, time);
        boolean flag = workFlowLogService.remove(qmQueryWrapper);
        if (flag) {
            String message = i18nUtil.getMessage("MSG.wf.common.delSuccess");
            resultObj.setMsg(message);
        }
        return resultObj;
    }

    /**
     * 根据传入的实体信息进行查询
     */
    @ApiOperation(value = "根据传入的实体信息进行查询", notes = "[author:10027705]")
    @PostMapping("/table")
    public JsonResultVo<QmPage<WorkFlowLogDO>> table(@RequestBody WorkFlowLogDTO tempDTO) {
        //定义查询构造器
        QmQueryWrapper<WorkFlowLogDO> queryWrapper = new QmQueryWrapper<>();
        //拼装实体属性查询条件
        LambdaQueryWrapper<WorkFlowLogDO> lambdaWrapper = queryWrapper.lambda();
        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getId()), WorkFlowLogDO::getId, tempDTO.getId());
        //事务码代码
        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getVtranscode()), WorkFlowLogDO::getVtranscode, tempDTO.getVtranscode());
        //用例名称、菜单名称
        lambdaWrapper.like(!BootAppUtil.isNullOrEmpty(tempDTO.getVmenuname()), WorkFlowLogDO::getVmenuname, tempDTO.getVmenuname());
        // 执行结果
        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getResult()), WorkFlowLogDO::getResult, tempDTO.getResult());
        //公司ID
        LoginKeyDO loginKey = this.getUserInfo();
        lambdaWrapper.eq(WorkFlowLogDO::getNcompanyid, loginKey.getCompanyId());
        //操作员ID
        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getNopr()), WorkFlowLogDO::getNopr, tempDTO.getNopr());
        //操作员代码
        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getVopr()), WorkFlowLogDO::getVopr, tempDTO.getVopr());
        //操作员名称
        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getVoprname()), WorkFlowLogDO::getVoprname, tempDTO.getVoprname());
        //开始时间
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getDbegin1()) && !BootAppUtil.isNullOrEmpty(tempDTO.getDbegin2())) {
            lambdaWrapper.gt(WorkFlowLogDO::getDbegin, tempDTO.getDbegin1());
            lambdaWrapper.le(WorkFlowLogDO::getDbegin, tempDTO.getDbegin2());
        }
        //心跳时间。判断后台进程是否已经中断
        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getDpulse()), WorkFlowLogDO::getDpulse, tempDTO.getDpulse());
        //进度
        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getNprocess()), WorkFlowLogDO::getNprocess, tempDTO.getNprocess());
        //流程定义Key
        lambdaWrapper.like(!BootAppUtil.isNullOrEmpty(tempDTO.getProcessdefkey()), WorkFlowLogDO::getProcessdefkey, tempDTO.getProcessdefkey());
        //业务主键Key
        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getBusinesskey()), WorkFlowLogDO::getBusinesskey, tempDTO.getBusinesskey());
        //审批按钮名称
        lambdaWrapper.like(!BootAppUtil.isNullOrEmpty(tempDTO.getButtonname()), WorkFlowLogDO::getButtonname, tempDTO.getButtonname());
        //TraceId
        lambdaWrapper.like(!BootAppUtil.isNullOrEmpty(tempDTO.getTraceid()), WorkFlowLogDO::getTraceid, tempDTO.getTraceid());
        //执行条数
        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getNbatchcount()), WorkFlowLogDO::getNbatchcount, tempDTO.getNbatchcount());
        //执行参数
        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getVpara()), WorkFlowLogDO::getVpara, tempDTO.getVpara());
        //删除标识
        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getVdelete()), WorkFlowLogDO::getVdelete, tempDTO.getVdelete());
        //查询数据，使用table函数。
        QmPage<WorkFlowLogDO> list = workFlowLogService.table(queryWrapper, tempDTO);
        JsonResultVo<QmPage<WorkFlowLogDO>> ret = new JsonResultVo<>();
        ret.setData(list);
        return ret;
    }

    @ApiOperation(value = "管理员查询", notes = "[author:10027705]")
    @PostMapping("/query")
    public JsonResultVo<QmPage<WorkFlowLogVO>> query(@RequestBody WorkFlowLogDTO workFlowLogDTO) {
        JsonResultVo<QmPage<WorkFlowLogVO>> resultVo = new JsonResultVo();
        resultVo.setData(workFlowLogService.query(workFlowLogDTO));
        return resultVo;
    }

    @ApiOperation(value = "用户查询", notes = "[author:10027705]")
    @PostMapping("/userQuery")
    public JsonResultVo<QmPage<WorkFlowLogVO>> userQuery(@RequestBody WorkFlowLogDTO workFlowLogDTO) {
        JsonResultVo<QmPage<WorkFlowLogVO>> resultVo = new JsonResultVo();
        workFlowLogDTO.setVopr(BootAppUtil.getLoginKey().getPersonCode());
        resultVo.setData(workFlowLogService.query(workFlowLogDTO));
        return resultVo;
    }

}
