package com.qm.ep.wf.controller;

import com.qm.ep.wf.common.BaseFlowableController;
import com.qm.tds.api.domain.JsonResultVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.flowable.job.api.Job;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR> @date
 */
@RestController
@RequestMapping("/flowable/processDefinitionJob")
@Api(value = "流程定义定时任务", tags = {"processDefinitionJob"})
public class ProcessDefinitionJobController extends BaseFlowableController {

    @GetMapping(value = "/list")
    @ApiOperation(value = "列表查询", notes = "[author:10027705]")
    public List<Job> list(@RequestParam String processDefinitionId) {
        return managementService.createTimerJobQuery().processDefinitionId(processDefinitionId).list();
    }

    @DeleteMapping(value = "/delete")
    @ApiOperation(value = "新增流程定义定时任务", notes = "[author:10027705]")
    @Transactional(rollbackFor = Exception.class)
    public JsonResultVo deleteJob(@RequestParam String jobId) {
        JsonResultVo resultObj = new JsonResultVo();
        managementService.deleteTimerJob(jobId);
        return resultObj;
    }
}
