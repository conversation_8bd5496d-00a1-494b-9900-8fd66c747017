<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qm.ep.wf.mapper.ProcessCounterSignMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qm.ep.wf.domain.bean.ProcessCounterSignDO">
        <id column="ID" property="id"/>
        <result column="GRP_NAME" property="grpName"/>
        <result column="TASK_KEY" property="taskKey"/>
        <result column="PROCESS_DEF_KEY" property="processDefKey"/>
        <result column="TYPE" property="type"/>
        <result column="CUSTOM_VALUE" property="customValue"/>
        <result column="RATE" property="rate"/>
        <result column="DTSTAMP" property="dtstamp"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
    ID, TASK_KEY, PROCESS_DEF_KEY, TYPE, CUSTOM_VALUE, RATE, DTSTAMP
    </sql>

    <!-- 公共查询 -->
    <sql id="QuerySQL">
        select * from (
            select
                a.GRP_NAME As grpName,
                a.TASK_KEY As taskKey,
                a.PROCESS_DEF_KEY As processDefKey,
                a.TYPE,
                a.CUSTOM_VALUE As customValue,
                a.RATE,
                a.DTSTAMP,
                a.ID
            from act_ex_countersign a
        ) innerTable
    </sql>

    <!-- 复写MP自带函数 -->
    <select id="selectByIdNew" resultType="com.qm.ep.wf.domain.bean.ProcessCounterSignDO">
        <include refid="QuerySQL"/>
        where id = #{id}
    </select>
    <select id="selectBatchIdsNew" resultType="com.qm.ep.wf.domain.bean.ProcessCounterSignDO">
        <include refid="QuerySQL"/>
        <if test="coll != null and !coll.isEmpty">
            <where>
                id in (<foreach collection="coll" item="item" separator=",">#{item}</foreach>)
            </where>
        </if>
    </select>
    <select id="selectByMapNew" resultType="com.qm.ep.wf.domain.bean.ProcessCounterSignDO">
        <include refid="QuerySQL"/>
        <if test="cm != null and !cm.isEmpty">
            <where>
                <foreach collection="cm" index="k" item="v" separator="AND">
                    <choose>
                        <when test="v == null">${k} IS NULL</when>
                        <otherwise>${k} = #{v}</otherwise>
                    </choose>
                </foreach>
            </where>
        </if>
    </select>
    <select id="selectOne" resultType="com.qm.ep.wf.domain.bean.ProcessCounterSignDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectCount" resultType="java.lang.Integer">
        select count(1) from (<include refid="QuerySQL"/>${ew.customSqlSegment} ) countTable
    </select>
    <select id="selectList" resultType="com.qm.ep.wf.domain.bean.ProcessCounterSignDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectMaps" resultType="com.qm.ep.wf.domain.bean.ProcessCounterSignDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectObjs" resultType="com.qm.ep.wf.domain.bean.ProcessCounterSignDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectPage" resultType="com.qm.ep.wf.domain.bean.ProcessCounterSignDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectMapsPage" resultType="com.qm.ep.wf.domain.bean.ProcessCounterSignDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>

    <select id="selectCountersignGroupName" resultType="com.qm.ep.wf.domain.bean.ProcessCounterSignDO"
            parameterType="com.qm.ep.wf.domain.dto.ProcessCounterSignDTO">
        SELECT
            *
        FROM
            act_ex_countersign
        WHERE
            PROCESS_DEF_KEY = #{processDefKey}
        GROUP BY
            GRP_NAME
    </select>

    <update id="updateCountersignByKey" parameterType="com.qm.ep.wf.domain.bean.ProcessCounterSignDO">
        UPDATE act_ex_countersign a
        <set>
            <if test="type != null">
                a.TYPE = #{type,jdbcType=VARCHAR},
            </if>
            <if test="customValue != null">
                a.CUSTOM_VALUE = #{customValue,jdbcType=VARCHAR},
            </if>
            <if test="rate != null">
                a.RATE = #{rate,jdbcType=VARCHAR},
            </if>
        </set>
        WHERE
        a.PROCESS_DEF_KEY = #{processDefKey,jdbcType=VARCHAR}
        AND
        a.GRP_NAME = #{grpName,jdbcType=VARCHAR}
    </update>

</mapper>
