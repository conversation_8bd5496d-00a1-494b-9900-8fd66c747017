package com.qm.ep.wf.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Sets;
import com.qm.ep.wf.common.cmd.GetProcessDefinitionInfoCmd;
import com.qm.ep.wf.constant.Constants;
import com.qm.ep.wf.constant.FlowableConstant;
import com.qm.ep.wf.domain.bean.ProcessCounterSignDO;
import com.qm.ep.wf.domain.dto.ProcessCounterSignDTO;
import com.qm.ep.wf.domain.dto.ProcessDefinitionQueryDTO;
import com.qm.ep.wf.domain.entity.FlowableForm;
import com.qm.ep.wf.domain.vo.IdentityRequest;
import com.qm.ep.wf.domain.vo.ProcessDefinitionRequest;
import com.qm.ep.wf.mapper.ProcessDefinitionMapper;
import com.qm.ep.wf.service.FlowableFormService;
import com.qm.ep.wf.service.ProcessCounterSignService;
import com.qm.ep.wf.service.ProcessDefinitionService;
import com.qm.ep.wf.util.JacksonUtil;
import com.qm.ep.wf.util.ObjectUtils;
import com.qm.tds.util.I18nUtil;
import com.qm.tds.util.RedisUtils;
import org.flowable.bpmn.converter.BpmnXMLConverter;
import org.flowable.bpmn.model.Process;
import org.flowable.bpmn.model.*;
import org.flowable.common.engine.api.FlowableException;
import org.flowable.common.engine.api.FlowableObjectNotFoundException;
import org.flowable.common.engine.impl.util.io.InputStreamSource;
import org.flowable.common.engine.impl.util.io.StreamSource;
import org.flowable.engine.ManagementService;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.impl.persistence.entity.ProcessDefinitionEntityImpl;
import org.flowable.engine.repository.DeploymentBuilder;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.job.api.Job;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayInputStream;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.zip.ZipInputStream;

/**
 * <AUTHOR> @date
 */
@Service
public class ProcessDefinitionServiceImpl implements ProcessDefinitionService {
    @Autowired
    protected RepositoryService repositoryService;
    @Autowired
    protected ManagementService managementService;
    @Autowired
    protected RuntimeService runtimeService;
    @Autowired
    private FlowableFormService flowableFormService;
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private I18nUtil i18nUtil;
    @Autowired
    private ProcessCounterSignService processCounterSignService;

    @Autowired
    private ProcessDefinitionMapper processDefinitionMapper;

    @Override
    public ProcessDefinition getProcessDefinitionById(String processDefinitionId) {
        return managementService.executeCommand(new GetProcessDefinitionInfoCmd(processDefinitionId, null, null));
    }

    @SuppressWarnings("squid:S1192")
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String processDefinitionId, Boolean cascade) {
        ProcessDefinition processDefinition = getProcessDefinitionById(processDefinitionId);
        if (processDefinition.getDeploymentId() == null) {
            throw new FlowableException("Process definition has not deployed with id " + processDefinitionId);
        }
        if (!cascade) {
            long processCount = runtimeService.createProcessInstanceQuery().processDefinitionId(processDefinitionId)
                    .count();
            if (processCount > 0) {
                String message = i18nUtil.getMessage("ERR.wf.processDefinitionService.definitionExist", processDefinitionId);
                throw new FlowableException(message);
            }
            long jobCount = managementService.createTimerJobQuery().processDefinitionId(processDefinitionId).count();
            if (jobCount > 0) {
                throw new FlowableException(
                        "There are running time jobs with process definition id " + processDefinitionId);
            }
            repositoryService.deleteDeployment(processDefinition.getDeploymentId());
        } else {
            List<Job> jobs = managementService.createTimerJobQuery().processDefinitionId(processDefinitionId).list();
            for (Job job : jobs) {
                managementService.deleteTimerJob(job.getId());
            }
            repositoryService.deleteDeployment(processDefinition.getDeploymentId(), true);
        }
        // 从redis中删除
        String key = redisUtils.keyBuilder("wf", "processDefinition", processDefinitionId);
        redisUtils.del(key);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void activate(ProcessDefinitionRequest actionRequest) {
        String processDefinitionId = actionRequest.getProcessDefinitionId();
        ProcessDefinition processDefinition = getProcessDefinitionById(processDefinitionId);
        if (!processDefinition.isSuspended()) {
            throw new FlowableException("Process definition is not suspended with id " + processDefinitionId);
        }
        repositoryService.activateProcessDefinitionById(processDefinitionId, actionRequest.isIncludeProcessInstances(),
                actionRequest.getDate());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void suspend(ProcessDefinitionRequest actionRequest) {
        String processDefinitionId = actionRequest.getProcessDefinitionId();
        ProcessDefinition processDefinition = getProcessDefinitionById(processDefinitionId);
        if (processDefinition.isSuspended()) {
            throw new FlowableException("Process definition is already suspended with id " + processDefinitionId);
        }
        repositoryService.suspendProcessDefinitionById(processDefinition.getId(),
                actionRequest.isIncludeProcessInstances(), actionRequest.getDate());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doImport(String tenantId, HttpServletRequest request) {
        String pKey = null;
        if (!(request instanceof MultipartHttpServletRequest)) {
            throw new IllegalArgumentException("request must instance of MultipartHttpServletRequest");
        }
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        if (multipartRequest.getFileMap().size() == 0) {
            throw new IllegalArgumentException("request file is empty");
        }
        MultipartFile file = multipartRequest.getFileMap().values().iterator().next();
        String fileName = file.getOriginalFilename();
        boolean isFileNameInValid = ObjectUtils.isEmpty(fileName)
                || !(fileName.endsWith(".bpmn20.xml") || fileName.endsWith(".bpmn")
                || fileName.toLowerCase().endsWith(".bar") || fileName.toLowerCase().endsWith(".zip"));


        if (isFileNameInValid && !validateBmpnFileName(fileName)) {
            throw new IllegalArgumentException("Request file must end with .bpmn20.xml,.bpmn|,.bar,.zip");
        }
        Set<String> userTaskIds = Sets.newHashSet();
        try {
            DeploymentBuilder deploymentBuilder = repositoryService.createDeployment();
            boolean isBpmnFile = validateBmpnFileName(fileName);
            if (isBpmnFile) {
                deploymentBuilder.addInputStream(fileName, file.getInputStream());
                StreamSource xmlSource = new InputStreamSource(file.getInputStream());
                BpmnModel bpmnModel = new BpmnXMLConverter().convertToBpmnModel(xmlSource, false, false, "UTF-8");

                org.flowable.bpmn.model.Process process = bpmnModel.getMainProcess();
                pKey = process.getId();
                Collection<FlowElement> flowElements = process.getFlowElements();
                Map<String, String> formKeyMap = new HashMap<>(16);
                for (FlowElement flowElement : flowElements) {
                    String formKey = null;
                    if (flowElement instanceof StartEvent) {
                        StartEvent startEvent = (StartEvent) flowElement;
                        if (startEvent.getFormKey() != null && startEvent.getFormKey().length() > 0) {
                            formKey = startEvent.getFormKey();
                        }
                    } else if (flowElement instanceof UserTask) {
                        UserTask userTask = (UserTask) flowElement;
                        userTaskIds.add(userTask.getId());
                        if (userTask.getFormKey() != null && userTask.getFormKey().length() > 0) {
                            formKey = userTask.getFormKey();
                        }
                    }
                    if (formKey != null && formKey.length() > 0 && !formKeyMap.containsKey(formKey)) {
                        String formKeyDefinition = formKey.replace(".form", "");
                        FlowableForm form = flowableFormService.getById(formKeyDefinition);
                        if (form != null && form.getFormJson() != null && form.getFormJson().length() > 0) {
                            byte[] formJson = form.getFormJson().getBytes(StandardCharsets.UTF_8);
                            ByteArrayInputStream bi = new ByteArrayInputStream(formJson);
                            deploymentBuilder.addInputStream(formKey, bi);
                            formKeyMap.put(formKey, formKey);
                        } else {
                            throw new FlowableObjectNotFoundException(
                                    "Cannot find formJson with formKey " + formKeyDefinition);
                        }
                    }
                }
            } else if (fileName.toLowerCase().endsWith(FlowableConstant.FILE_EXTENSION_BAR)
                    || fileName.toLowerCase().endsWith(FlowableConstant.FILE_EXTENSION_ZIP)) {
                deploymentBuilder.addZipInputStream(new ZipInputStream(file.getInputStream()));
            }
            deploymentBuilder.name(fileName);
            if (tenantId != null && tenantId.length() > 0) {
                deploymentBuilder.tenantId(tenantId);
            }
            deploymentBuilder.deploy();
        } catch (FlowableObjectNotFoundException e) {
            throw e;
        } catch (Exception e) {
            throw new FlowableException("Process definition import error", e);
        }
        if (pKey != null) {
            ProcessDefinition p = managementService.executeCommand(new GetProcessDefinitionInfoCmd(null, pKey, null));
            saveRedisProcessDefinitionTask(p.getId());
            //清除流程定义自动执行任务按钮信息缓存
            String defaultButtonKey = redisUtils.keyBuilder(Constants.MODULE, Constants.AUTO_TASK_BUTTON, pKey);
            redisUtils.del(defaultButtonKey);
            //清除按钮是否所属任务缓存信息
            String buttonBelongTaskKey = redisUtils.keyBuilder(Constants.MODULE, Constants.BUTTON_BELONG_TASK);
            redisUtils.del(buttonBelongTaskKey);
            // 刷新会签表中的数据
            refreshProcessCounterSign(pKey, userTaskIds);
        }
    }

    @SuppressWarnings("squid:S1192")
    @Override
    public void saveRedisProcessDefinitionTask(String processDefinitionId) {
        //先删除
        String delKey = redisUtils.keyBuilder("wf", "processDefinition", processDefinitionId.split(":")[0]);
        redisUtils.del(redisUtils.getKeys(delKey));
        //通过流程定义ID获取流程模板
        BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinitionId);
        if (bpmnModel != null) {
            List<org.flowable.bpmn.model.Process> processList = bpmnModel.getProcesses();
            for (Process process : processList) {
                List<UserTask> flowElements = process.findFlowElementsOfType(UserTask.class);
                String key = redisUtils.keyBuilder("wf", "processDefinition", processDefinitionId.split(":")[0]);
                List<UserTask> userTasks = new ArrayList<>();
                for (UserTask flowElement : flowElements) {
                    UserTask task = new UserTask();
                    task.setId(flowElement.getId());
                    task.setExtensionElements(flowElement.getExtensionElements());
                    task.setAssignee(flowElement.getAssignee());
                    task.setOwner(flowElement.getOwner());
                    task.setName(flowElement.getName());
                    userTasks.add(task);
                }
                redisUtils.set(key, JacksonUtil.objToStr(userTasks));
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveProcessDefinitionIdentityLink(IdentityRequest identityRequest) {
        ProcessDefinition processDefinition = getProcessDefinitionById(identityRequest.getProcessDefinitionId());
        validateIdentityArguments(identityRequest.getIdentityId(), identityRequest.getIdentityType());
        if (FlowableConstant.IDENTITY_GROUP.equals(identityRequest.getIdentityType())) {
            repositoryService.addCandidateStarterGroup(processDefinition.getId(), identityRequest.getIdentityId());
        } else if (FlowableConstant.IDENTITY_USER.equals(identityRequest.getIdentityType())) {
            repositoryService.addCandidateStarterUser(processDefinition.getId(), identityRequest.getIdentityId());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteProcessDefinitionIdentityLink(String processDefinitionId, String identityId, String type) {
        validateIdentityArguments(identityId, type);
        ProcessDefinition processDefinition = getProcessDefinitionById(processDefinitionId);
        if (FlowableConstant.IDENTITY_GROUP.equals(type)) {
            repositoryService.deleteCandidateStarterGroup(processDefinition.getId(), identityId);
        } else if (FlowableConstant.IDENTITY_USER.equals(type)) {
            repositoryService.deleteCandidateStarterUser(processDefinition.getId(), identityId);
        }
    }

    @Override
    public IPage<ProcessDefinitionEntityImpl> selectLatestProcessByParams(ProcessDefinitionQueryDTO dto) {
        IPage<ProcessDefinitionEntityImpl> page = new Page(dto.getCurrentPage(), dto.getPageSize());
        return processDefinitionMapper.selectLatestProcessByParams(page, dto);
    }

    private void validateIdentityArguments(String identityId, String type) {
        if (identityId == null || identityId.length() == 0) {
            throw new FlowableException("IdentityId may not be null");
        }
        if (!FlowableConstant.IDENTITY_GROUP.equals(type) && !FlowableConstant.IDENTITY_USER.equals(type)) {
            throw new FlowableException(
                    "Type must be " + FlowableConstant.IDENTITY_GROUP + " or " + FlowableConstant.IDENTITY_USER);
        }
    }

    @Override
    public ProcessDefinition getProcessDefinitionByPKey(String processDefinitionPKey) {
        return managementService.executeCommand(new GetProcessDefinitionInfoCmd(null, processDefinitionPKey, null));
    }

    /**
     * 刷新会签策略表中的信息
     */
    private void refreshProcessCounterSign(String processDefKey, Set<String> userTaskIds) {
        if (CollectionUtils.isEmpty(userTaskIds)) {
            return;
        }

        ProcessCounterSignDTO dto = new ProcessCounterSignDTO();
        dto.setProcessDefKey(processDefKey);
        List<ProcessCounterSignDO> countersignInfo = processCounterSignService.getCountersignInfo(dto);
        if (CollectionUtils.isEmpty(countersignInfo)) {
            return;
        }
        for (ProcessCounterSignDO signDO : countersignInfo) {
            if (userTaskIds.contains(signDO.getTaskKey())) {
                continue;
            }
            processCounterSignService.removeById(signDO.getId());
        }

    }


    private boolean validateBmpnFileName(String fileName) {
        String reg = "(.bpmn20.xml|.bpmn20(.*).xml|.bpmn.xml|.bpmn(.*).xml)$";

        Matcher matcher = Pattern.compile(reg).matcher(fileName);
        return matcher.find();
    }
}
