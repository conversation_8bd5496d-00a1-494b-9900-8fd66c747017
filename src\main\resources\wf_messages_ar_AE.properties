#这里填写阿语文翻译
MSG.wf.common.saveSuccess=تم الحفظ بنجاح!
ERR.wf.common.saveFail=فشل في الحفظ!
MSG.wf.common.delSuccess=تم الحذف بنجاح!
ERR.wf.common.delFail=فشل في الحذف!
MSG.wf.common.updateSuccess=تم التعديل بنجاح!
ERR.wf.common.updateFail=فشل في التعديل!
ERR.wf.common.isEmpty=لا يمكن أن يكون %s فارغًا
ERR.wf.processCommonButtons.rptItemDO2=يوجد رمز عملية مكرر، يرجى إعادة إدخاله
MSG.wf.flow.defination=تعريف العملية:
MSG.wf.task.name=المهمة:
MSG.wf.transaction.code=رمز المعاملة:
MSG.wf.common.terminator=.   
MSG.wf.taskAuth.checkIsLast=بعد حذف هذا الإذن، هذا الزر ليس له أي معلومات تفويض المشغل بموجب حالة الاستخدام الحالية، أي أن جميع المستخدمين الذين لديهم إذن حالة الاستخدام لديهم الإذن!
ERR.wf.deleteProcesss.exception=العملية: %s فشل الحذف%s
ERR.wf.deleteProcessInstance.exception=فشل في حذف المفتاح الرئيسي للعمل %s! السبب هو: %s
MSG.wf.common.claimSuccess=تمت المطالبة بنجاح!
MSG.wf.common.cancelClaimSuccess=تم إلغاء المطالبة بنجاح!
ERR.wf.common.failReason=فشل في التنفيذ، والسبب هو: %s
MSG.wf.common.success=ناجح!
ERR.wf.flowableForm.save=استمارة: %s موجود بالفعل!
ERR.wf.task.errorMessage=فشل، والسبب هو: تعذر العثور على المهمة القادمة!
ERR.wf.task.failMessage=فشل، والسبب هو:
ERR.wf.processInstance.startErrMsg=فشل تشغيل العملية، والسبب
ERR.wf.processInstanceService.roleListEmpty=قائمة الأدوار فارغة.
MSG.wf.copyButton.copySuccess=تم النسخ بنجاح
ERR.wf.copyButton.copyFail=فشل في النسخ
ERR.wf.copyButton.nonExist=لا يوجد الزر لعملية المصدر
ERR.wf.processCounterSignService.customeSaveOrUpdate=يوجد تكوين نهج مجموعة التوقيع بالمصادقة المكرر في العقدة الحالية!
MSG.wf.common.operateSuccess=نجح التشغيل
ERR.wf.processDefinitionService.definitionExist=تعريف العملية: يوجد مثيل قيد التشغيل في %s، ولا يمكن حذفه!
ERR.wf.processInstanceService.processEndDelete=انتهى مثيل العملية، لا يسمح بحذفه!
ERR.wf.processInstanceService.checkedProcessDelete=يمكن حذف فقط العمليات التي لم تتم مراجعتها!
MSG.wf.common.processInstanceService.endProcessStop=انتهى مثيل العملية، لا يسمح بإنهائه!
ERR.wf.processInstanceService.checkedProcessStop=يمكن إنهاء فقط مثيل العملية التي لم تتم مراجعته!
MSG.wf.processInstanceService.definitionTemplateNotFound=لم يتم العثور على تعريف قالب سير العمل
MSG.wf.flowableTaskService.rollback=الرجوع إلى %s.
MSG.wf.flowableTaskService.jobFinish=مهمة إنجاز الوظيفة
MSG.wf.flowableTaskService.jobEndFlow=عملية إنهاء الوظيفة
MSG.wf.flowableTaskService.jobRollback=مهمة إرجاع الوظيفة
MSG.wf.taskAuthService.cleanSuccess=تم مسح الإذن بنجاح
MSG.wf.common.tradeFail=فشل في تنفيذ الصفقة
##########################################
MSG.wf.CommentTypeEnum.init=بدء
MSG.wf.CommentTypeEnum.commit=إرسال
MSG.wf.CommentTypeEnum.recommit=إعادة الإرسال
MSG.wf.CommentTypeEnum.claim=مطالبة
MSG.wf.CommentTypeEnum.disclaim=إلغاء المطالبة
MSG.wf.CommentTypeEnum.approve=موافقة
MSG.wf.CommentTypeEnum.finish=إنهاء
ERR.wf.CommentTypeEnum.return=إرجاع
MSG.wf.CommentTypeEnum.withdraw=انسحاب
MSG.wf.CommentTypeEnum.tempStorage=تخزين مؤقت
MSG.wf.CommentTypeEnum.turnTo=التحويل للقيام به
ERR.wf.CommentTypeEnum.delegate=تفويض
MSG.wf.CommentTypeEnum.stop=إنهاء
MSG.wf.FlowableConstant.autoReview=مراجعة تلقائية
ERR.wf.FlowableConstant.busiSourceUsing=يتم شغل موارد الأعمال، يرجى التحديث والمحاولة مرة أخرى لاحقًا!
ERR.wf.FlowableConstant.sourceUsing=يتم شغل الموارد، يرجى التحديث والمحاولة مرة أخرى لاحقًا!
ERR.wf.ProcessCommonButtonsController.operateSuccess=تم التشغيل بنجاح!
MSG.wf.ProcessCommonButtonsController.operateFail=فشل في التشغيل!
ERR.wf.ProcessController.submitProcessId=نجح الإرسال، ومعرف العملية هو:
ERR.wf.ProcessController.processNonexist=العملية غير موجودة
MSG.wf.ProcessController.approvalOrNot=ما إذا تمت الموافقة عليها:
ERR.wf.ProcessController.nodeNonexist=العقدة غير موجودة
ERR.wf.ProcessController.rejectSuccess=تم الرفض بنجاح...
ERR.wf.ProcessController.stopProcessInstanceSuccess=تم إنهاء مثيل العملية بنجاح
MSG.wf.ProcessController.pendingProcessSuccess=تم تعليق العملية بنجاح...
MSG.wf.ProcessController.recoveryProcessSuccess=تم استرداد العملية بنجاح...
ERR.wf.ProcessDefinitionController.processIdNull=لا يمكن أن يكون معرف العملية فارغًا
ERR.wf.ProcessDefinitionController.activateSuccess=تم التنشيط بنجاح!
ERR.wf.ProcessDefinitionController.pendingSuccess=تم التعليق بنجاح!
ERR.wf.ProcessInstanceController.processStartupError=توجد مشكلة في بدء تشغيل العملية الـ%s، وbusinessKey الذي تم إنشاءه هو %s، ورسالة الخطأ هي:
ERR.wf.ProcessInstanceController.processStartupFail=فشل في بدء تشغيل العملية، اطلع على معلومات الفشل للتفاصيل!
ERR.wf.ProcessInstanceController.processStopSuccess=تم إنهاء العملية بنجاح!
ERR.wf.ProcessInstanceController.wfinstanceNonexist=مثيل سير العمل غير موجود
ERR.wf.ProcessInstanceController.processChangeFail=المفتاح الرئيسي للعمل: %s فشل في تحديث متغير العملية!
ERR.wf.ProcessInstanceController.processStartupFailInfo=فشل في بدء تشغيل العملية:
ERR.wf.TaskAuthController.processDefine=تعريف العملية:
ERR.wf.TaskAuthController.task=المهمة:
ERR.wf.TaskAuthController.trancCode=رمز المعاملة:
ERR.wf.TaskAuthController.button=الزر:
ERR.wf.TaskAuthController.java=تقوم هذه الأزرار بترخيص المشغل لأول مرة، وبعد حفظها، فقط المستخدمين (الأدوار) المصرح لهم لديهم الأذون، ولم يعد المستخدمين الآخرين لديهم الأذون!
ERR.wf.TaskController.wfcodeError=رمز سير العمل: %s  المفتاح الرئيسي للعمل: %s   لا يوجد مثيل سير العمل!
ERR.wf.TaskController.failReasonIs=فشل، والسبب هو:
ERR.wf.TaskController.batchFail=فشل في الدفعة من %s، يرجى تحديد المستندات بنفس الحالة لمعالجتها!"؛ ;
ERR.wf.TaskController.buttonTaskDiff=فشل، والسبب هو: الزر غير متوافق مع المهمة الحالية، يرجى التحديث والمحاولة مرة أخرى!
ERR.wf.TaskController.taskFailPrompt=فشل المهمة الـ%s %s!
ERR.wf.TaskController.processing=تجري المعالجة، يرجى المحاولة لاحقًا!
ERR.wf.TaskController.rejectedBack=لقد رفضت، وقد عادت المهمة إلى العقدة السابقة بالفعل
