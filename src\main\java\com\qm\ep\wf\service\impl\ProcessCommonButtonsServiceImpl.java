package com.qm.ep.wf.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qm.ep.wf.domain.bean.ProcessCommonButtonsDO;
import com.qm.ep.wf.domain.dto.ProcessCommonButtonsDTO;
import com.qm.ep.wf.domain.dto.TaskAuthDTO;
import com.qm.ep.wf.domain.dto.TaskTranAuthDTO;
import com.qm.ep.wf.mapper.ProcessCommonButtonsMapper;
import com.qm.ep.wf.service.ProcessCommonButtonsService;
import com.qm.ep.wf.util.CommonUtil;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.service.impl.QmBaseServiceImpl;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.I18nUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-15
 */
@Service
public class ProcessCommonButtonsServiceImpl extends QmBaseServiceImpl<ProcessCommonButtonsMapper, ProcessCommonButtonsDO> implements ProcessCommonButtonsService {
    @Autowired
    private I18nUtil i18nUtil;

    @Override
    public List<ProcessCommonButtonsDTO> getButtonsByProc(TaskTranAuthDTO taskTranAuthDTO) {
        return baseMapper.selectButtonsByProc(taskTranAuthDTO);
    }

    @Override
    public List<ProcessCommonButtonsDO> getButtons(String processDefKey, String buttonCode) {
        QueryWrapper<ProcessCommonButtonsDO> queryWrapper = new QueryWrapper<>();
        if (CommonUtil.isNotEmptyAfterTrim(processDefKey)) {
            queryWrapper.eq("processDefKey", processDefKey);
        }
        if (CommonUtil.isNotEmptyAfterTrim(buttonCode)) {
            queryWrapper.eq("code", buttonCode);
        }
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<ProcessCommonButtonsDTO> getButtonsByParams(TaskTranAuthDTO taskTranAuthDTO) {
        return baseMapper.selectButtonsByParams(taskTranAuthDTO);
    }

    @Override
    public List<ProcessCommonButtonsDTO> selectButtonsByAuth(IPage<TaskAuthDTO> page, Wrapper<TaskAuthDTO> queryWrapper) {
        return baseMapper.selectButtonsByAuth(page, queryWrapper);
    }

    @Override
    public JsonResultVo copyButton(Map<String, String> map) {
        JsonResultVo result = new JsonResultVo();
        if (!BootAppUtil.isNullOrEmpty(map.get("sourceProcess")) && !BootAppUtil.isNullOrEmpty(map.get("targetProcess"))) {
            QueryWrapper<ProcessCommonButtonsDO> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("processDefKey", map.get("sourceProcess"));
            List<ProcessCommonButtonsDO> list = baseMapper.selectList(queryWrapper);
            if (!list.isEmpty()) {
                for (ProcessCommonButtonsDO processCommonButtonsDO : list) {
                    processCommonButtonsDO.setId(null);
                    processCommonButtonsDO.setProcessDefKey(map.get("targetProcess"));
                }
                if (saveBatch(list)) {
                    String message = i18nUtil.getMessage("MSG.wf.copyButton.copySuccess");
                    result.setMsg(message);
                } else {
                    String message = i18nUtil.getMessage("ERR.wf.copyButton.copyFail");
                    result.setMsgErr(message);
                }
            } else {
                String message = i18nUtil.getMessage("ERR.wf.copyButton.nonExist");
                result.setMsgErr(message);
            }
        }
        return result;
    }

    @Override
    public String findButtonLangInfo(String buttonCode, String processDefKey) {
        ProcessCommonButtonsDO param = new ProcessCommonButtonsDO();
        param.setCode(buttonCode);
        param.setProcessDefKey(processDefKey);

        //增加语言标识----20220311--
        String LanguageCode = BootAppUtil.getLoginKey().getLanguageCode();
        if (!BootAppUtil.isNullOrEmpty(LanguageCode)) {
            param.setVlanguagecode(LanguageCode);
        }
        ProcessCommonButtonsDO  rep = baseMapper.selectButtonLang(param);
        if(BootAppUtil.isNullOrEmpty(rep)){
            //log.info("--没有在拓展表，act_hi_comment_code，查询到数据！！--");
            return "";
        }else{
            return rep.getName();
        }

    }

    @Override
    public List<ProcessCommonButtonsDO> getButtonsBy(String processDefKey, String buttonCode,Integer operationFlag) {
        QueryWrapper<ProcessCommonButtonsDO> queryWrapper = new QueryWrapper<>();
        if (CommonUtil.isNotEmptyAfterTrim(processDefKey)) {
            queryWrapper.eq("processDefKey", processDefKey);
        }
        if (CommonUtil.isNotEmptyAfterTrim(buttonCode)) {
            queryWrapper.eq("code", buttonCode);
        }

        queryWrapper.eq("operationFlag", operationFlag);

        return baseMapper.selectList(queryWrapper);
    }
}
