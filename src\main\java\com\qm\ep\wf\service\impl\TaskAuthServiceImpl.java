package com.qm.ep.wf.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.qm.common.uiep.mp.pagination.UiepPage;
import com.qm.common.uiep.table.domain.TableWhereInfo;
import com.qm.ep.wf.domain.bean.TaskAuthDO;
import com.qm.ep.wf.domain.bean.TaskTranAuthDO;
import com.qm.ep.wf.domain.bean.TransCodeDO;
import com.qm.ep.wf.domain.dto.ProcessCommonButtonsDTO;
import com.qm.ep.wf.domain.dto.TaskAuthDTO;
import com.qm.ep.wf.domain.dto.TaskTranAuthDTO;
import com.qm.ep.wf.domain.dto.UserDTO;
import com.qm.ep.wf.domain.vo.ProcessDefKeyNameVo;
import com.qm.ep.wf.domain.vo.ProcessDefinitionResponse;
import com.qm.ep.wf.domain.vo.RoleVO;
import com.qm.ep.wf.domain.vo.UserRoleCodeVO;
import com.qm.ep.wf.exception.FlowableTaskException;
import com.qm.ep.wf.mapper.TaskAuthMapper;
import com.qm.ep.wf.remote.SysFeignRemote;
import com.qm.ep.wf.service.*;
import com.qm.ep.wf.util.ObjectUtils;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.mapper.QmBaseMapper;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import com.qm.tds.api.service.impl.QmBaseServiceImpl;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.I18nUtil;
import com.qm.tds.util.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.bpmn.model.Process;
import org.flowable.bpmn.model.UserTask;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.TaskService;
import org.flowable.engine.repository.ProcessDefinition;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-17
 */
@SuppressWarnings("squid:S2326")
@Service
@Slf4j
public abstract class TaskAuthServiceImpl<M extends QmBaseMapper<T>, T> extends QmBaseServiceImpl<TaskAuthMapper, TaskAuthDO> implements TaskAuthService {
    @Autowired
    protected RepositoryService repositoryService;
    @Autowired
    private SysFeignRemote sysFeignRemote;
    @Autowired
    private TaskTranAuthService taskTranAuthService;
    @Autowired
    private ProcessCommonButtonsService processCommonButtonsService;
    @Autowired
    private TaskService taskService;
    @Autowired
    private ProcessDefinitionService processDefinitionService;
    @Autowired
    private I18nUtil i18nUtil;
    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private ProcessInstanceService processInstanceService;

    @Autowired
    private TaskAuthService taskAuthService;

    @Override
    public List procDefList(List list) {
        // 查询所有用例,MAP格式
        JsonResultVo<Map<String, TransCodeDO>> transMap = sysFeignRemote.selectTransAllMap(BootAppUtil.getLoginKey().getTenantId());
        if (transMap.getCode() == 500) {
            throw new FlowableTaskException(transMap.getMsg());
        }
        // 查询定义用例授权数据
        List<ProcessDefKeyNameVo> lists = new ArrayList<>();
        if (!list.isEmpty()) {
            for (int i = 0, len = list.size(); i < len; i++) {
                ProcessDefinitionResponse p = (ProcessDefinitionResponse) list.get(i);
                // 构建一级数据:流程定义
                ProcessDefKeyNameVo processDefKeyNameVo = new ProcessDefKeyNameVo();
                processDefKeyNameVo.setId(p.getKey());
                processDefKeyNameVo.setName(p.getName());
                processDefKeyNameVo.setVersion(p.getVersion());
                // 查询定义下的事务授权
                TaskTranAuthDTO taskTranAuthDTO = new TaskTranAuthDTO();
                taskTranAuthDTO.setProcessDefKey(p.getKey());
                // 非操作授权类
                taskTranAuthDTO.setAuthorization(0);
                JsonResultVo<QmPage<TaskTranAuthDO>> ret = taskTranAuthService.searchTransAuth(taskTranAuthDTO);
                List<TaskTranAuthDO> taskTranAuthDOS = ret.getData().getItems();
                List<Map<String, Object>> children = new ArrayList<>();
                if (!taskTranAuthDOS.isEmpty()) {
                    taskTranAuthDOS.forEach(tran -> {
                        Map<String, Object> map = new HashMap<>();
                        map.put("id", UUID.randomUUID());
                        if (transMap.getData().get(tran.getTransCode()) != null) {
                            map.put("name", transMap.getData().get(tran.getTransCode()).getVtext());
                        }
                        map.put("code", tran.getTransCode());
                        map.put("children", handleTransButtons(p.getKey(), tran.getTransCode()));
                        children.add(map);
                    });
                }
                processDefKeyNameVo.setChildren(children);
                lists.add(processDefKeyNameVo);
            }
        }
        return lists;
    }

    private List<Map<String, Object>> handleTransButtons(String key, String transCode) {
        List<Map<String, Object>> list = new ArrayList<>();
        // 查询按钮
        TaskTranAuthDTO search = new TaskTranAuthDTO();
        search.setProcessDefKey(key);
        search.setTransCode(transCode);
        search.setAuthorization(0);
        List<ProcessCommonButtonsDTO> buttons = processCommonButtonsService.getButtonsByProc(search);

        if (!buttons.isEmpty()) {
            for (ProcessCommonButtonsDTO button : buttons) {
                Map<String, Object> map = new HashMap<>();
                map.put("id", UUID.randomUUID());
                map.put("name", button.getName());
                map.put("code", button.getCode());
                map.put("remarks", button.getRemark());
                map.put("stateCode", button.getStateCode());
                map.put("taskText", getUserTask(button.getTaskName(), key));
                String userTask = getUserTask(button.getTaskName(), key);
                map.put("taskText", userTask);
                map.put("taskName", button.getTaskName());
                map.put("processDefKey", key);
                map.put("transCode", transCode);
                if (!StringUtils.isEmpty(userTask)) {
                    list.add(map);
                }

            }
        }
        return list;
    }

    /**
     * @description 获取任务节点
     * <AUTHOR>
     * @date 2020/8/18 13:18
     */
    private String getUserTask(String taskDefinitionKey, String processDefKey) {
        String name = "";
        ProcessDefinition processDefinition = processDefinitionService.getProcessDefinitionByPKey(processDefKey);
        //通过流程定义ID获取流程模板
        BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinition.getId());
        if (bpmnModel != null) {
            List<Process> processList = bpmnModel.getProcesses();
            for (Process process : processList) {
                Collection<UserTask> flowElements = process.findFlowElementsOfType(UserTask.class);
                for (UserTask userTask : flowElements) {
                    // 获取当前待办任务节点的元素
                    if (userTask.getId().equals(taskDefinitionKey)) {
                        name = userTask.getName();
                    }
                }
            }
        }
        return name;
    }

    /**
     * @description 根据任务id、人员代码、流程定义key判断是否有权限
     * <AUTHOR>
     * @date 2020/8/21 16:16
     */
    @Override
    public boolean judgeTaskAuthForUser(String taskName, String actionCode, String processDefKey, String buttonCode, String transCode) {
        boolean flag = false;
        TaskAuthDTO taskAuthDTO = new TaskAuthDTO();
        taskAuthDTO.setProcessDefKey(processDefKey);
        taskAuthDTO.setTaskName(taskName);
        if (!BootAppUtil.isNullOrEmpty(buttonCode)) {
            taskAuthDTO.setButtonCode(buttonCode);
        }
        // 查询当前任务按钮的用户授权
        List<TaskAuthDTO> list = baseMapper.judgeTaskAuthForUser(taskAuthDTO);
        // 查询当前任务按钮的角色授权
        List<TaskAuthDO> roleAuthList = selectRoleAuth(taskName, processDefKey, buttonCode, transCode);
        if (!CollectionUtils.isEmpty(list)) {
            // 判断用户权限
            for (int i = 0, len = list.size(); i < len; i++) {
                if (list.get(i).getActionCode().equals(BootAppUtil.getLoginKey().getOperatorId())) {
                    flag = true;
                    break;
                }
            }
        } else if (!CollectionUtils.isEmpty(roleAuthList)) {
            // 用户权限没过，且角色权限存在，判断角色权限
            // 查询当前登陆用户的角色
            List<String> roleIds = selectNowUserRole();
            if (!CollectionUtils.isEmpty(roleIds)) {
                for (TaskAuthDO item : roleAuthList) {
                    if (roleIds.contains(item.getActionCode())) {
                        flag = true;
                    }
                }
            }
        } else {
            // 没有用户和角色权限，直接通过
            flag = true;
        }
        return flag;
    }

    @SuppressWarnings("squid:S1192")
    @Override
    public QmPage<ProcessCommonButtonsDTO> searchButtonsTwo(TaskAuthDTO tempDTO) {
        QmPage<ProcessCommonButtonsDTO> page = new QmPage<>();
        UiepPage<TaskAuthDTO> queryPage = new UiepPage<>();
        queryPage.setSize(tempDTO.getPageSize());
        queryPage.setCurrent(tempDTO.getCurrentPage());
        //定义查询构造器
        QmQueryWrapper<TaskAuthDTO> queryWrapper = new QmQueryWrapper<>();
        //参与者代码
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getActionCode())) {
            queryWrapper.eq("ACTION_CODE", tempDTO.getActionCode());
        }
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getTwhere())) {
            // 转换对象
            List<TableWhereInfo> whereInfoList = TableWhereInfo.parser(tempDTO.getTwhere());
            if (!whereInfoList.isEmpty()) {
                for (TableWhereInfo tableWhereInfo : whereInfoList) {
                    if ("code".equals(tableWhereInfo.getFieldName())) {
                        queryWrapper.like("CODE", tableWhereInfo.getFieldValue());
                    }
                    if ("processDefKey".equals(tableWhereInfo.getFieldName())) {
                        queryWrapper.like("PROCESS_DEF_KEY", tableWhereInfo.getFieldValue());
                    }
                    if ("taskName".equals(tableWhereInfo.getFieldName())) {
                        queryWrapper.like("TASK_NAME", tableWhereInfo.getFieldValue());
                    }
                    if ("transCode".equals(tableWhereInfo.getFieldName())) {
                        queryWrapper.like("TRANS_CODE", tableWhereInfo.getFieldValue());
                    }
                    if ("transName".equals(tableWhereInfo.getFieldName())) {
                        JsonResultVo<List<String>> trans = sysFeignRemote.selectTransCodeByName(BootAppUtil.getLoginKey().getTenantId(), tableWhereInfo.getFieldValue().toString());
                        if (trans.getCode() == 500) {
                            throw new FlowableTaskException(trans.getMsg());
                        }
                        List<String> codes = new ArrayList<>();
                        if (!trans.getData().isEmpty()) {
                            codes.addAll(trans.getData());
                        } else {
                            codes.add("null");
                        }
                        queryWrapper.in("TRANS_CODE", codes);
                    }
                }
            }
        }
        sort(tempDTO, queryWrapper);
        List<ProcessCommonButtonsDTO> list = processCommonButtonsService.selectButtonsByAuth(queryPage, queryWrapper);
        if (!list.isEmpty()) {
            // 查询所有用例,MAP格式
            JsonResultVo<Map<String, TransCodeDO>> transMap = sysFeignRemote.selectTransAllMap(BootAppUtil.getLoginKey().getTenantId());
            if (transMap.getCode() == 500) {
                throw new FlowableTaskException(transMap.getMsg());
            }
            list.forEach(button -> {
                if (transMap.getData().get(button.getTransCode()) != null) {
                    String transName = transMap.getData().get(button.getTransCode()).getVtext();
                    button.setTransName(BootAppUtil.isNullOrEmpty(transName) ? "" : transName);
                }
            });
        }
        page.setItems(list);
        return page;
    }

    @SuppressWarnings("squid:S1192")
    private void sort(TaskAuthDTO tempDTO, QmQueryWrapper<TaskAuthDTO> wrapper) {
        if (StringUtils.isEmpty(tempDTO.getTsortby())) {
            return;
        }
        String[] sorts = tempDTO.getTsortby().split(",");
        for (String sort : sorts) {
            if (sort.contains("transName")) {
                if (isContainsAscend(sort)) {
                    wrapper.orderByAsc("TRANS_CODE");
                } else {
                    wrapper.orderByDesc("TRANS_CODE");
                }

            }
            if (sort.contains("stateCode")) {
                if (isContainsAscend(sort)) {
                    wrapper.orderByAsc("STATE_CODE");
                } else {
                    wrapper.orderByDesc("STATE_CODE");
                }
            }
        }

        //wrapper.orderByAsc();
    }


    private boolean isContainsAscend(String str) {
        return str.contains("ascend");
    }

    @SuppressWarnings("squid:S1192")
    @Override
    public boolean removeByMapForRedis(Map map) {
        boolean flag = removeByMap(map);
        if (flag) {
            Map<String, List<RoleVO>> roleMaps = processInstanceService.cacheCurrentAllUserRole();
            taskTranAuthService.createRedisData(map.get("PROCESS_DEF_KEY").toString(), roleMaps);
        }
        return flag;
    }

    @Override
    public boolean saveOrUpdateBatchForRedis(List<TaskAuthDO> list) {
        //这个saveOrUpdateBatch 方法会造成重复数据  暂时弃用
        //boolean flag = saveOrUpdateBatch(list);
        boolean flag = false;
        boolean flagRe = true;
        for (TaskAuthDO item : list) {
            String processDefKey = item.getProcessDefKey();
            String taskName = item.getTaskName();
            String buttonCode = item.getButtonCode();
            String transCode = item.getTransCode();
            String actionCode = item.getActionCode();
            List<TaskAuthDO> taskAuthDOS = taskAuthService.selectTaskAuthByFiveParams(taskName, transCode, processDefKey, buttonCode, actionCode);
            if (taskAuthDOS.size() == 0) {

                try {
                    flag = saveOrUpdate(item);
                } catch (Exception ex) {
                    log.warn("saveOrUpdateBatchForRedis保存数据异常！" + ex.getMessage());
                    flagRe = false;
                }
            }


        }

        // 同步REDIS数据
        // 同步REDIS数据
        if (flag && !CollectionUtils.isEmpty(list)) {
            List<String> pdf = list.stream().map(TaskAuthDO::getProcessDefKey).distinct().collect(Collectors.toList());
            Map<String, List<RoleVO>> roleMaps = processInstanceService.cacheCurrentAllUserRole();
            pdf.forEach(key -> taskTranAuthService.createRedisData(key, roleMaps));
        }
        return flagRe;
    }

    @SuppressWarnings("squid:S1192")
    @Override
    public JsonResultVo<TaskAuthDO> clearAuth(List<TaskAuthDTO> tempDTOS) {
        JsonResultVo<TaskAuthDO> ret = new JsonResultVo<>();
        if (!CollectionUtils.isEmpty(tempDTOS)) {
            Map<String, List<RoleVO>> roleMaps = processInstanceService.cacheCurrentAllUserRole();
            Set<String> processDefKeys = Sets.newHashSet();
            for (TaskAuthDTO tempDTO : tempDTOS) {
                if (!BootAppUtil.isNullOrEmpty(tempDTO.getProcessDefKey()) && !BootAppUtil.isNullOrEmpty(tempDTO.getTaskName())
                        && !BootAppUtil.isNullOrEmpty(tempDTO.getButtonCode())) {
                    Map<String, Object> columnMap = new LinkedHashMap<>();
                    columnMap.put("PROCESS_DEF_KEY", tempDTO.getProcessDefKey());
                    columnMap.put("TRANS_CODE", tempDTO.getTransCode());
                    columnMap.put("BUTTON_CODE", tempDTO.getButtonCode());
                    columnMap.put("TASK_NAME", tempDTO.getTaskName());
                    // 删除操作权限
                    removeByMap(columnMap);
                    // 删除事务权限
                    taskTranAuthService.removeByMap(columnMap);
                    log.warn("在方法TaskAuthServiceImpl中的clearAuth processDefKey:{} ,transCode:{} ,actionCode:{}被删除", columnMap.get("PROCESS_DEF_KEY"), columnMap.get("TRANS_CODE"), tempDTO.getActionCode());
                    processDefKeys.add(tempDTO.getProcessDefKey());

                }
            }
            for (String processDefKey : processDefKeys) {
                // 重新构建redis数据
                taskTranAuthService.createRedisData(processDefKey, roleMaps);
            }
        }
        String message = i18nUtil.getMessage("MSG.wf.taskAuthService.cleanSuccess");
        ret.setMsg(message);
        return ret;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public JsonResultVo clearAuthByProcessDefinitionId(String processDefinitionId) {
        ProcessDefinition processDefinition = processDefinitionService.getProcessDefinitionById(processDefinitionId);
        QmQueryWrapper<TaskTranAuthDO> queryWrapper = new QmQueryWrapper<>();
        //拼装实体属性查询条件
        LambdaQueryWrapper<TaskTranAuthDO> lambdaWrapper = queryWrapper.lambda();
        lambdaWrapper.eq(TaskTranAuthDO::getProcessDefKey, processDefinition.getKey());
        List<TaskTranAuthDO> list = taskTranAuthService.list(lambdaWrapper);

        List<TaskAuthDTO> dtos = Lists.newArrayList();
        for (TaskTranAuthDO taskAuthDO : list) {
            TaskAuthDTO dto = new TaskAuthDTO();
            dto.setTaskName(taskAuthDO.getTaskName());
            dto.setProcessDefKey(taskAuthDO.getProcessDefKey());
            dto.setButtonCode(taskAuthDO.getButtonCode());
            dto.setTransCode(taskAuthDO.getTransCode());
            dtos.add(dto);
        }
        clearAuth(dtos);

        JsonResultVo resultVo = new JsonResultVo();
        String message = i18nUtil.getMessage("MSG.wf.common.operateSuccess");
        resultVo.setMsg(message);
        return resultVo;
    }

    @Override
    public JsonResultVo<String> selectButtonAuth(String taskName, String processDefKey, String buttonCode) {
        //定义查询构造器
        QmQueryWrapper<TaskAuthDO> queryWrapper = new QmQueryWrapper<>();
        //拼装实体属性查询条件
        LambdaQueryWrapper<TaskAuthDO> lambdaWrapper = queryWrapper.lambda();
        //人工任务名称
        if (!BootAppUtil.isNullOrEmpty(taskName)) {
            lambdaWrapper.eq(TaskAuthDO::getTaskName, taskName);
        }
        //流程定义Key 对应bpmn中process的ID
        if (!BootAppUtil.isNullOrEmpty(processDefKey)) {
            lambdaWrapper.eq(TaskAuthDO::getProcessDefKey, processDefKey);
        }
        // 按钮代码
        if (!BootAppUtil.isNullOrEmpty(buttonCode)) {
            lambdaWrapper.eq(TaskAuthDO::getButtonCode, buttonCode);
        }
        //查询数据，使用table函数。
        List<TaskAuthDO> list = list(queryWrapper);
        List<String> userCodes = new ArrayList<>();
        List<String> roleCodes = new ArrayList<>();
        // 分别取出用户和角色code
        if (!CollectionUtils.isEmpty(list)) {
            userCodes = list.stream().filter(e -> "0".equals(e.getActionType())).map(TaskAuthDO::getActionCode).distinct().collect(Collectors.toList());
            roleCodes = list.stream().filter(e -> "1".equals(e.getActionType())).map(TaskAuthDO::getActionCode).distinct().collect(Collectors.toList());
        }
        // 若角色存在，取角色对应的用户code
        // 查询所有角色对应code
        if (!CollectionUtils.isEmpty(roleCodes)) {
            JsonResultVo<Map<String, List<UserRoleCodeVO>>> roleResult = sysFeignRemote.getPersonCodeByRoleCode(BootAppUtil.getLoginKey().getTenantId(), "");
            if (roleResult.getCode() == 500) {
                throw new FlowableTaskException(roleResult.getMsg());
            }
            for (String roleCode : roleCodes) {
                Map<String, List<UserRoleCodeVO>> map = roleResult.getData();
                List<UserRoleCodeVO> userRoleCodes = map.get(roleCode);
                if (!BootAppUtil.isNullOrEmpty(userRoleCodes) && !CollectionUtils.isEmpty(userRoleCodes)) {
                    userCodes.addAll(userRoleCodes.stream().map(UserRoleCodeVO::getVPERSONCODE).collect(Collectors.toList()));
                }
            }
        }
        // 如果用户存在，访问sys获取用户ID集合
        JsonResultVo<String> ret = new JsonResultVo<>();
        if (!CollectionUtils.isEmpty(userCodes)) {
            List<String> codes = userCodes.stream().distinct().collect(Collectors.toList());
            JsonResultVo<String> result = sysFeignRemote.selectUserIdByCode(BootAppUtil.getLoginKey().getTenantId(), codes);
            ret.setDataList(result.getDataList());
        }
        return ret;
    }

    protected List<TaskAuthDO> selectRoleAuth(String taskName, String processDefKey, String buttonCode, String transCode) {
        //定义查询构造器
        QmQueryWrapper<TaskAuthDO> queryWrapper = new QmQueryWrapper<>();
        //拼装实体属性查询条件
        LambdaQueryWrapper<TaskAuthDO> lambdaWrapper = queryWrapper.lambda();
        lambdaWrapper.eq(TaskAuthDO::getActionType, 1);
        lambdaWrapper.eq(TaskAuthDO::getProcessDefKey, processDefKey);
        lambdaWrapper.eq(TaskAuthDO::getTaskName, taskName);
        lambdaWrapper.eq(TaskAuthDO::getTransCode, transCode);
        if (!BootAppUtil.isNullOrEmpty(buttonCode)) {
            lambdaWrapper.eq(TaskAuthDO::getButtonCode, buttonCode);
        }
        //查询数据，使用table函数。
        QmPage<TaskAuthDO> auth = table(queryWrapper, new TaskAuthDTO());
        return auth.getItems();
    }

    protected List<String> selectNowUserRole() {
        UserDTO userDTO = new UserDTO();
        userDTO.setId(BootAppUtil.getLoginKey().getOperatorId());
        String currentUserRoleKey = redisUtils.keyBuilder("wf", "current_user", "role", BootAppUtil.getLoginKey().getOperatorId());
        Object o = redisUtils.get(currentUserRoleKey);
        if (!ObjectUtils.isEmpty(o)) {
            return (List<String>) o;
        }

        JsonResultVo roleList = sysFeignRemote.getRoleListByUserId(BootAppUtil.getLoginKey().getTenantId(), userDTO);
        if (roleList.getCode() == 500) {
            throw new FlowableTaskException(roleList.getMsg());
        }
        List<String> roleIds = new ArrayList<>();
        if (roleList.getData() != null) {
            for (LinkedHashMap roleDO : (ArrayList<LinkedHashMap>) roleList.getData()) {
                roleIds.add(roleDO.get("id").toString());
            }
        }
        return roleIds;
    }

    @Override
    public List<TaskAuthDO> selectTaskAuthByFiveParams(String taskName, String transCode, String processDefKey, String buttonCode, String actionCode) {
        TaskAuthDO taskTranAuthDTO = new TaskAuthDO();
        taskTranAuthDTO.setProcessDefKey(processDefKey);
        taskTranAuthDTO.setTransCode(transCode);
        taskTranAuthDTO.setTaskName(taskName);
        taskTranAuthDTO.setButtonCode(buttonCode);
        taskTranAuthDTO.setActionCode(actionCode);
        return baseMapper.selectTaskAuthByFiveParmas(taskTranAuthDTO);
    }
}
