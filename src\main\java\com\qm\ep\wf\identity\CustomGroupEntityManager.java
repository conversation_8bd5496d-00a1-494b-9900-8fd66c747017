package com.qm.ep.wf.identity;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.qm.ep.wf.domain.dto.UserDTO;
import com.qm.ep.wf.exception.FlowableTaskException;
import com.qm.ep.wf.remote.SysFeignRemote;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.base.domain.LoginKeyDO;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.RedisUtils;
import org.flowable.idm.api.Group;
import org.flowable.idm.engine.IdmEngineConfiguration;
import org.flowable.idm.engine.impl.GroupQueryImpl;
import org.flowable.idm.engine.impl.persistence.entity.GroupEntity;
import org.flowable.idm.engine.impl.persistence.entity.GroupEntityImpl;
import org.flowable.idm.engine.impl.persistence.entity.GroupEntityManagerImpl;
import org.flowable.idm.engine.impl.persistence.entity.data.GroupDataManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * <AUTHOR> @date
 */
public class CustomGroupEntityManager extends GroupEntityManagerImpl {

    @Autowired
    private SysFeignRemote sysFeignRemote;

    @Autowired
    private RedisUtils redisUtils;

    private static final String KEY = "wf:relaterole";
    private static final String MODULE = "common-wf";

    public CustomGroupEntityManager(IdmEngineConfiguration idmEngineConfiguration, GroupDataManager groupDataManager) {
        super(idmEngineConfiguration, groupDataManager);
    }

    @Override
    public GroupEntity findById(String entityId) {
        return new GroupEntityImpl();
    }

    @Override
    public List<Group> findGroupByQueryCriteria(GroupQueryImpl query) {
        LoginKeyDO userDO = BootAppUtil.getLoginKey();
        UserDTO userDTO = new UserDTO();
        userDTO.setId(userDO.getOperatorId());
        List<Group> groups = new ArrayList<>();
        //数据存储key
        String relateRoleKey = redisUtils.keyBuilder(MODULE, "relaterole", userDO.getOperatorId());
        //先读取缓存数据
        Object objString = redisUtils.get(relateRoleKey);
        Object obj = null;
        if (null != objString) {
            obj = JSON.parse(objString.toString());
        }
        if (null == obj || obj instanceof ArrayList && ((ArrayList) obj).isEmpty()) {
            //获取头中的租户ID
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                String tenantId = request.getHeader("tenantId");
                JsonResultVo roleList = sysFeignRemote.getRoleListByUserId(tenantId, userDTO);
                if (roleList.getCode() == 500) {
                    throw new FlowableTaskException(roleList.getMsg());
                }
                if (roleList.getData() != null) {
                    for (LinkedHashMap roleDO : (ArrayList<LinkedHashMap>) roleList.getData()) {
                        Group group = new GroupEntityImpl();
                        group.setId(roleDO.get("vrolecode").toString());
                        group.setName(roleDO.get("vrolename") != null ? roleDO.get("vrolename").toString() : roleDO.get("vrolecode").toString());
                        groups.add(group);
                    }
                }
            }
            //存入缓存
            String groupString = JSON.toJSONString(groups);
            redisUtils.set(relateRoleKey, groupString, 600);
            //存修改标记key
            redisUtils.lPush(KEY, relateRoleKey);
        } else {
            JSONArray groupsList = (JSONArray) obj;
            for (Object groupObj : groupsList) {
                Group group = new GroupEntityImpl();
                group.setId(((JSONObject) groupObj).get("id").toString());
                group.setName(((JSONObject) groupObj).get("name").toString());
                groups.add(group);
            }
        }
        return groups;
    }

    @Override
    public long findGroupCountByQueryCriteria(GroupQueryImpl query) {
        return 1;
    }
}
