package com.qm.ep.wf.util;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Enumeration;
import java.util.LinkedHashMap;
import java.util.Map;

@Slf4j
@Configuration
public class FeignBasicAuthRequestInterceptor implements RequestInterceptor {
    @Override
    public void apply(RequestTemplate requestTemplate) {
        HttpServletRequest request = getHttpServletRequest();
        if (null != request) {
            Map<String, String> headers = getHeaders(request);
            for (String headerName : headers.keySet()) {
                if (null != getHttpServletRequest())
                    requestTemplate.header(headerName, getHeaders(getHttpServletRequest()).get(headerName));
            }
        }
    }

    private HttpServletRequest getHttpServletRequest() {
        try {
            RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
            if (null != requestAttributes)
                return ((ServletRequestAttributes) requestAttributes).getRequest();
        } catch (Exception e) {
            log.info("---error--"+"getHttpServletRequest:异常", e);
        }
        return null;
    }

    private Map<String, String> getHeaders(HttpServletRequest request) {
        Map<String, String> map = new LinkedHashMap<>();
        if (null != request) {
            Enumeration<String> enumeration = request.getHeaderNames();
            while (enumeration.hasMoreElements()) {
                String key = enumeration.nextElement();
                //这里后续考虑哪些header需要传递，千万不能把所有的header传下去
                String value = request.getHeader(key);
                if (key.equals("content-length")){
                    continue;
                }
                map.put(key, value);
            }
        }
        return map;
    }
}
