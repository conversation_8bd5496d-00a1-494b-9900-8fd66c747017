package com.qm.ep.wf.domain.bean;


import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("act_ex_countersign")
@ApiModel(value = "对象ProcessCounterSignDO对象", description = "对象ProcessCounterSignDO对象")
public class ProcessCounterSignDO implements Serializable {

    @ApiModelProperty("串行版 uid")
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty(value = "会签组名称")
    @TableField("GRP_NAME")
    private String grpName;

    @ApiModelProperty(value = "人工任务名称")
    @TableField("TASK_KEY")
    private String taskKey;

    @ApiModelProperty(value = "流程定义Key 对应bpmn中process的ID")
    @TableField("PROCESS_DEF_KEY")
    private String processDefKey;

    @ApiModelProperty(value = "会签类型，（比例通过制、一票否决制、自定义）")
    @TableField("TYPE")
    private String type;

    @ApiModelProperty(value = "自定义属性")
    @TableField("CUSTOM_VALUE")
    private String customValue;

    @ApiModelProperty(value = "比例")
    @TableField("RATE")
    private String rate;

    @ApiModelProperty(value = "时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    @Version
    private Timestamp dtstamp;


}
