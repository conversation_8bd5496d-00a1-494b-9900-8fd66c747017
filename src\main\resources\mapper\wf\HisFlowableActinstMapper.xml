<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.qm.ep.wf.mapper.HisFlowableActinstMapper" >
    <delete id="deleteHisActinstsByIds" parameterType="java.util.List">
        delete from act_hi_actinst where ID_ in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <delete id="deleteHiActinst" parameterType="java.util.List">
        delete from act_hi_actinst where PROC_INST_ID_ in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <delete id="deleteHiProcInst" parameterType="java.util.List">
        delete from act_hi_procinst where PROC_INST_ID_ in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <delete id="deleteHiTaskInst" parameterType="java.util.List">
        delete from act_hi_taskinst where PROC_INST_ID_ in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <delete id="deleteHiComment" parameterType="java.util.List">
        delete from act_hi_comment where PROC_INST_ID_ in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <delete id="deleteHiCommentByIds" parameterType="java.util.List">
        delete from act_hi_comment where ID_ in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <delete id="deleteHiIdLink" parameterType="java.util.List">
        delete from ACT_HI_IDENTITYLINK where PROC_INST_ID_ in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <select id="selectHiGeByte" parameterType="java.util.List" resultType="java.util.HashMap">
        SELECT DISTINCT
        b.ID_
        FROM
        act_hi_varinst a,
        act_ge_bytearray b
        WHERE
        a.BYTEARRAY_ID_ = b.ID_
        AND a.PROC_INST_ID_ in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="hisProcInsQueryByBusinessKeys" resultType="java.util.HashMap">
        SELECT DISTINCT
        RES.*,
        DEF.KEY_ AS PROC_DEF_KEY_,
        DEF.NAME_ AS PROC_DEF_NAME_,
        DEF.VERSION_ AS PROC_DEF_VERSION_,
        DEF.DEPLOYMENT_ID_ AS DEPLOYMENT_ID_
        FROM
        ACT_HI_PROCINST RES
        LEFT OUTER JOIN ACT_RE_PROCDEF DEF ON RES.PROC_DEF_ID_ = DEF.ID_
        WHERE
        DEF.KEY_ = #{processDefKey}
        AND RES.BUSINESS_KEY_ in
        <foreach item="item" index="index" collection="businessKeys" open="(" separator="," close=")">
            #{item}
        </foreach>
        ORDER BY
        RES.ID_ ASC
    </select>

    <delete id="deleteHiVariable" parameterType="java.util.List">
        delete from act_hi_varinst where PROC_INST_ID_ in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <select id="getTaskTodoCount"  resultType="java.lang.Integer">
        select count(distinct a.id_)
        from
        (
        SELECT
        RES.*
        FROM
        ACT_RU_TASK RES
        WHERE RES.OWNER_ = #{userID}
        OR RES.ASSIGNEE_ = #{userID}
        union all
        SELECT
        RES.*
        FROM
        ACT_RU_TASK RES
        inner join ACT_RU_IDENTITYLINK LINK on LINK.TASK_ID_ = RES.ID_  and LINK.TYPE_ = 'candidate' and LINK.USER_ID_ = #{userID}
        WHERE RES.ASSIGNEE_ IS NULL
        union all
        SELECT
        RES.*
        FROM
        ACT_RU_TASK RES
        inner join ACT_RU_IDENTITYLINK LINK on LINK.TASK_ID_ = RES.ID_  and LINK.TYPE_ = 'candidate' and LINK.GROUP_ID_ IN
        <foreach item="item" index="index" collection="relateRoles" open="(" separator="," close=")">
            #{item}
        </foreach>
        WHERE RES.ASSIGNEE_ IS NULL
        ) a
    </select>

</mapper>
