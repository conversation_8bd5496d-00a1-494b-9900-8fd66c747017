package com.qm.ep.wf.controller;

import com.qm.ep.wf.common.BaseFlowableController;
import com.qm.ep.wf.domain.vo.IdentityRequest;
import com.qm.ep.wf.domain.vo.IdentityResponse;
import com.qm.ep.wf.service.FlowableTaskService;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.util.I18nUtil;
import io.swagger.annotations.ApiOperation;
import org.flowable.identitylink.api.history.HistoricIdentityLink;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR> @date
 */
@RestController
@RequestMapping("/flowable/taskIdentityLink")
public class TaskIdentityLinkController extends BaseFlowableController {
    @Autowired
    protected FlowableTaskService flowableTaskService;
    @Autowired
    private I18nUtil i18nUtil;

    @GetMapping(value = "/list")
    @ApiOperation(value = "列表查询", notes = "[author:10027705]")
    public JsonResultVo list(@RequestParam String taskId) {
        JsonResultVo resultObj = new JsonResultVo();
        HistoricTaskInstance task = flowableTaskService.getHistoricTaskInstanceNotNull(taskId);
        List<HistoricIdentityLink> historicIdentityLinks = historyService.getHistoricIdentityLinksForTask(task.getId());
        List<IdentityResponse> identityResponse = responseFactory.createTaskIdentityResponseList(historicIdentityLinks);
        resultObj.setData(identityResponse);
        return resultObj;
    }

    @PostMapping(value = "/save")
    @ApiOperation(value = "新增任务授权", notes = "[author:10027705]")
    public JsonResultVo save(@RequestBody IdentityRequest taskIdentityRequest) {
        JsonResultVo resultObj = new JsonResultVo();
        flowableTaskService.saveTaskIdentityLink(taskIdentityRequest);
        String message = i18nUtil.getMessage("MSG.wf.common.saveSuccess");
        resultObj.setMsg(message);
        return resultObj;
    }

    @DeleteMapping(value = "/delete")
    @ApiOperation(value = "删除任务授权", notes = "[author:10027705]")
    public JsonResultVo deleteIdentityLink(@RequestParam String taskId, @RequestParam String identityId,
                                           @RequestParam String identityType) {
        JsonResultVo resultObj = new JsonResultVo();
        flowableTaskService.deleteTaskIdentityLink(taskId, identityId, identityType);
        String message = i18nUtil.getMessage("MSG.wf.common.delSuccess");
        resultObj.setMsg(message);
        return resultObj;
    }
}
