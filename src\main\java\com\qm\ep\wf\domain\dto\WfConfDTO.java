package com.qm.ep.wf.domain.dto;

import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "对象WfConfDTO对象", description = "对象WfConfDTO对象")
@Data
public class WfConfDTO extends JsonParamDto {
    @ApiModelProperty("数据VWF代码")
    private String vwfCode;

    @ApiModelProperty("数据VWF 名称")
    private String vwfName;

    @ApiModelProperty("数据vmodel")
    private String vmodel;

    @ApiModelProperty("数据vbill 路由")
    private String vbillRoute;

    @ApiModelProperty("数据VSF")
    private String vsF;

    @ApiModelProperty("审核身份验证检查")
    private Integer auditAuthCheck;

    @ApiModelProperty("数据vmodule 模块")
    private String vmodule;

    @ApiModelProperty("授权")
    private Integer authorization;
}
