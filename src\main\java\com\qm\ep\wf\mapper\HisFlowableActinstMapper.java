package com.qm.ep.wf.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.qm.ep.wf.domain.entity.FlowableForm;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * @description: 运行时的节点Dao
 */
@Mapper
public interface HisFlowableActinstMapper extends BaseMapper<FlowableForm> {

    /**
     * 删除节点信息
     *
     * @param ids ids
     */
    public void deleteHisActinstsByIds(List<String> ids);

    public void deleteHiActinst(List<String> ids);

    public void deleteHiProcInst(List<String> ids);

    public void deleteHiTaskInst(List<String> ids);

    /**
     * 这里是扩展的审批明细表
     *
     * @param ids
     */
    public void deleteHiComment(List<String> ids);

    /**
     * 这个是原生的审批明细表
     *
     * @param ids
     */
    public void deleteHiCommentByIds(List<String> ids);

    public void deleteHiIdLink(List<String> ids);

    public void deleteHiGeByte(List<String> ids);

    public void deleteHiVariable(List<String> ids);

    public List<Map> selectHiGeByte(List<String> ids);

    public List<Map> hisProcInsQueryByBusinessKeys(@Param("processDefKey") String processDefKey, @Param("businessKeys") List<String> businessKeys);

    Integer getTaskTodoCount(String userID, List<String> relateRoles);
}
