package com.qm.ep.wf.common;

import com.qm.ep.wf.constant.Constants;
import com.qm.ep.wf.service.PermissionService;
import com.qm.ep.wf.util.ObjectUtils;
import com.qm.ep.wf.util.SpringContextUtils;
import com.qm.ep.wf.wapper.IListWrapper;
import org.flowable.bpmn.model.ValuedDataObject;
import org.flowable.common.engine.api.FlowableIllegalArgumentException;
import org.flowable.common.engine.api.query.Query;
import org.flowable.common.engine.api.query.QueryProperty;
import org.flowable.engine.*;
import org.springframework.beans.factory.annotation.Autowired;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> @date
 */
@SuppressWarnings({"rawtypes"})
public abstract class BaseFlowableController {
    @Autowired
    protected ResponseFactory responseFactory;
    @Autowired
    protected RepositoryService repositoryService;
    @Autowired
    protected ManagementService managementService;

    @Autowired
    protected RuntimeService runtimeService;

    @Autowired
    protected FormService formService;
    @Autowired
    protected HistoryService historyService;
    @Autowired
    protected PermissionService permissionService;
    @Autowired
    protected TaskService taskService;

    protected FlowablePage getFlowablePage(Map<String, String> requestParams) {
        int current = 1;
        if (ObjectUtils.isNotEmpty(requestParams.get(Constants.CURRENT))) {
            current = ObjectUtils.convertToInteger(requestParams.get(Constants.CURRENT), 1);
        }
        int size = 10;
        if (ObjectUtils.isNotEmpty(requestParams.containsKey(Constants.SIZE))) {
            size = ObjectUtils.convertToInteger(requestParams.get(Constants.SIZE), 10);
        }
        if (current < 0) {
            return null;
        }
        List<FlowablePage.Order> orders = null;
        if (ObjectUtils.isNotEmpty(requestParams.get(Constants.ORDER_RULE))) {
            // orderRule=column1|asc,column2|desc
            orders = new ArrayList<>();
            String orderRule = requestParams.get(Constants.ORDER_RULE);
            // 处理排序
            if (orderRule != null && orderRule.length() > 0) {
                String[] orderColumnRules = orderRule.split(",");
                for (String orderColumnRule : orderColumnRules) {
                    if (orderColumnRule.length() == 0) {
                        continue;
                    }
                    String[] rule = orderColumnRule.split("\\|");
                    String orderColumn = rule[0];
                    FlowablePage.Order orderTmp = null;
                    if (rule.length == 2 && "DESC".equalsIgnoreCase(rule[1])) {
                        orderTmp = new FlowablePage.Order(orderColumn, FlowablePage.Direction.DESC);
                    } else {
                        orderTmp = new FlowablePage.Order(orderColumn, FlowablePage.Direction.ASC);
                    }
                    orders.add(orderTmp);
                }
            }
        }
        if (orders == null) {
            return FlowablePage.of(current - 1, size);
        } else {
            return FlowablePage.of(current - 1, size, orders);
        }
    }

    protected FlowablePage pageList(Map<String, String> requestParams, Query query,
                                    Class<? extends IListWrapper> listWrapperClass, Map<String, QueryProperty> allowedSortProperties) {
        return pageList(getFlowablePage(requestParams), query, listWrapperClass, allowedSortProperties);
    }

    protected FlowablePage pageList(Map<String, String> requestParams, Query query,
                                    Class<? extends IListWrapper> listWrapperClass, Map<String, QueryProperty> allowedSortProperties,
                                    QueryProperty defaultDescSortProperty) {
        return pageList(getFlowablePage(requestParams), query, listWrapperClass, allowedSortProperties,
                defaultDescSortProperty);
    }

    protected FlowablePage pageList(FlowablePage flowablePage, Query query,
                                    Class<? extends IListWrapper> listWrapperClass, Map<String, QueryProperty> allowedSortProperties) {
        return pageList(flowablePage, query, listWrapperClass, allowedSortProperties, null);
    }

    protected FlowablePage pageList(FlowablePage flowablePage, Query query,
                                    Class<? extends IListWrapper> listWrapperClass, Map<String, QueryProperty> allowedSortProperties,
                                    QueryProperty defaultDescSortProperty) {
        List list = null;
        if (flowablePage == null) {
            list = query.list();
        } else {
            setQueryOrder(flowablePage.getOrders(), query, allowedSortProperties, defaultDescSortProperty);
            list = query.listPage((int) flowablePage.getOffset(), flowablePage.getSize());
        }
        if (listWrapperClass != null) {
            IListWrapper listWrapper = SpringContextUtils.getBean(listWrapperClass);
            list = listWrapper.execute(list);
        }
        if (null != flowablePage) {
            flowablePage.setRecords(list);
            flowablePage.setTotal(query.count());
        }
        return flowablePage;
    }

    protected List listWrapper(Class<? extends IListWrapper> listWrapperClass, List list) {
        IListWrapper listWrapper = SpringContextUtils.getBean(listWrapperClass);
        return listWrapper.execute(list);
    }

    protected void setQueryOrder(List<FlowablePage.Order> orders, Query query, Map<String, QueryProperty> properties,
                                 QueryProperty defaultDescSortProperty) {
        boolean orderByDefaultDescSortProperty = (orders == null || orders.isEmpty() || properties.isEmpty())
                && defaultDescSortProperty != null;
        if (orderByDefaultDescSortProperty) {
            query.orderBy(defaultDescSortProperty).desc();
        } else {
            if (orders != null && !orders.isEmpty()) {
                for (FlowablePage.Order order : orders) {
                    QueryProperty qp = properties.get(order.getProperty());
                    if (qp == null) {
                        throw new FlowableIllegalArgumentException("Value for param 'orders' is not valid, '"
                                + order.getProperty() + "' is not a valid property");
                    }
                    query.orderBy(qp);
                    if (order.getDirection() == FlowablePage.Direction.ASC) {
                        query.asc();
                    } else {
                        query.desc();
                    }
                }
            }
        }
    }

    /**
     * 只接收字符串
     *
     * @param message
     * @param arguments
     * @return
     */
    protected String messageFormat(String message, String... arguments) {
        return MessageFormat.format(message, (Object[]) arguments);
    }

    protected boolean isShowBusinessKey(String processDefinitionId) {
        List<ValuedDataObject> dataObjects = repositoryService.getBpmnModel(processDefinitionId).getMainProcess()
                .getDataObjects();
        if (dataObjects != null && !dataObjects.isEmpty()) {
            for (ValuedDataObject valuedDataObject : dataObjects) {
                if ("showBusinessKey".equals(valuedDataObject.getId())) {
                    if (valuedDataObject.getValue() instanceof String) {
                        return Boolean.valueOf((String) valuedDataObject.getValue());
                    } else if (valuedDataObject.getValue() instanceof Boolean) {
                        return (Boolean) valuedDataObject.getValue();
                    } else {
                        return false;
                    }
                }
            }
        }
        return false;
    }
}
