package com.qm.ep.wf.controller;

import com.qm.ep.wf.common.BaseFlowableController;
import com.qm.ep.wf.service.ProcessDefinitionService;
import com.qm.ep.wf.domain.vo.IdentityRequest;
import com.qm.tds.api.domain.JsonResultVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.identitylink.api.IdentityLink;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR> @date
 */
@RestController
@RequestMapping("/flowable/processDefinitionIdentityLink")
@Api(value = "流程定义授权", tags = {"processDefinitionIdentityLink"})
public class ProcessDefinitionIdentityLinkController extends BaseFlowableController {
    @Autowired
    private ProcessDefinitionService processDefinitionService;

    @GetMapping(value = "/list")
    @ApiOperation(value = "列表查询", notes = "[author:10027705]")
    public JsonResultVo list(@RequestParam String processDefinitionId) {
        JsonResultVo resultObj = new JsonResultVo();
        ProcessDefinition processDefinition = processDefinitionService.getProcessDefinitionById(processDefinitionId);
        List<IdentityLink> identityLinks = repositoryService
                .getIdentityLinksForProcessDefinition(processDefinition.getId());
        resultObj.setData(responseFactory.createIdentityResponseList(identityLinks));
        return resultObj;
    }

    @PostMapping(value = "/save")
    @ApiOperation(value = "新增流程定义授权", notes = "[author:10027705]")
    public JsonResultVo save(@RequestBody IdentityRequest identityRequest) {
        JsonResultVo resultObj = new JsonResultVo();
        processDefinitionService.saveProcessDefinitionIdentityLink(identityRequest);
        return resultObj;
    }

    @DeleteMapping(value = "/delete")
    @ApiOperation(value = "删除流程定义授权", notes = "[author:10027705]")
    public JsonResultVo delete(@RequestParam String processDefinitionId, @RequestParam String identityId,
                         @RequestParam String identityType) {
        JsonResultVo resultObj = new JsonResultVo();
        processDefinitionService.deleteProcessDefinitionIdentityLink(processDefinitionId, identityId, identityType);
        return resultObj;
    }
}
