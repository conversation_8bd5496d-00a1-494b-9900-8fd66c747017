package com.qm.ep.wf.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qm.ep.wf.constant.FlowableConstant;
import com.qm.ep.wf.domain.bean.WorkFlowLogDO;
import com.qm.ep.wf.domain.dto.WorkFlowLogDTO;
import com.qm.ep.wf.domain.vo.WorkFlowLogVO;
import com.qm.ep.wf.mapper.WorkFlowLogMapper;
import com.qm.ep.wf.service.WorkFlowLogService;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import com.qm.tds.api.service.impl.QmBaseServiceImpl;
import com.qm.tds.base.domain.LoginKeyDO;
import com.qm.tds.util.*;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 导出Excel日志 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-25
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class WorkFlowLogServiceImpl extends QmBaseServiceImpl<WorkFlowLogMapper, WorkFlowLogDO> implements WorkFlowLogService {
    @Autowired
    private WorkFlowLogMapper workFlowLogMapper;
    @Autowired
    private I18nUtil i18nUtil;
    @Autowired
    private RedisUtils redisUtils;

    private static final String KEY = "wf-batchComplete";

    @Override
    public QmPage<WorkFlowLogVO> query(@RequestBody WorkFlowLogDTO tempDTO) {
        //定义查询构造器
        QmQueryWrapper<WorkFlowLogDO> queryWrapper = new QmQueryWrapper<>();
        //拼装实体属性查询条件
        LambdaQueryWrapper<WorkFlowLogDO> lambdaWrapper = queryWrapper.lambda();
        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getId()), WorkFlowLogDO::getId, tempDTO.getId());
        //事务码代码
        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getVtranscode()), WorkFlowLogDO::getVtranscode, tempDTO.getVtranscode());
        //用例名称、菜单名称
        lambdaWrapper.like(!BootAppUtil.isNullOrEmpty(tempDTO.getVmenuname()), WorkFlowLogDO::getVmenuname, tempDTO.getVmenuname());
        // 执行结果
        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getResult()), WorkFlowLogDO::getResult, tempDTO.getResult());
        //公司ID
        LoginKeyDO loginKey = BootAppUtil.getLoginKey();
        lambdaWrapper.eq(WorkFlowLogDO::getNcompanyid, loginKey.getCompanyId());
        //操作员ID
        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getNopr()), WorkFlowLogDO::getNopr, tempDTO.getNopr());
        //操作员代码
        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getVopr()), WorkFlowLogDO::getVopr, tempDTO.getVopr());
        //操作员名称
        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getVoprname()), WorkFlowLogDO::getVoprname, tempDTO.getVoprname());
        //开始时间
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getDbegin1()) && !BootAppUtil.isNullOrEmpty(tempDTO.getDbegin2())) {
            lambdaWrapper.ge(WorkFlowLogDO::getDbegin, tempDTO.getDbegin1());
            lambdaWrapper.le(WorkFlowLogDO::getDbegin, tempDTO.getDbegin2());
        }
        //心跳时间。判断后台进程是否已经中断
        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getDpulse()), WorkFlowLogDO::getDpulse, tempDTO.getDpulse());
        //进度
        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getNprocess()), WorkFlowLogDO::getNprocess, tempDTO.getNprocess());
        //流程定义Key
        lambdaWrapper.like(!BootAppUtil.isNullOrEmpty(tempDTO.getProcessdefkey()), WorkFlowLogDO::getProcessdefkey, tempDTO.getProcessdefkey());
        //业务主键Key
        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getBusinesskey()), WorkFlowLogDO::getBusinesskey, tempDTO.getBusinesskey());
        //审批按钮名称
        lambdaWrapper.like(!BootAppUtil.isNullOrEmpty(tempDTO.getButtonname()), WorkFlowLogDO::getButtonname, tempDTO.getButtonname());
        //TraceId
        lambdaWrapper.like(!BootAppUtil.isNullOrEmpty(tempDTO.getTraceid()), WorkFlowLogDO::getTraceid, tempDTO.getTraceid());
        //执行条数
        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getNbatchcount()), WorkFlowLogDO::getNbatchcount, tempDTO.getNbatchcount());
        //执行参数
        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getVpara()), WorkFlowLogDO::getVpara, tempDTO.getVpara());
        //删除标识
        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getVdelete()), WorkFlowLogDO::getVdelete, tempDTO.getVdelete());
        //查询数据，使用table函数。
        lambdaWrapper.orderByDesc(WorkFlowLogDO::getDbegin);

        TableUtils.appendTableAdditional(queryWrapper, tempDTO);
        IPage<WorkFlowLogVO> queryPage = TableUtils.convertToIPage(tempDTO);
        IPage<WorkFlowLogVO> list = workFlowLogMapper.selectListWithVO(queryPage, queryWrapper, tempDTO);
        //List<WorkFlowLogVO> arry = list.getRecords();
        // List<WorkFlowLogVO> dataList = new ArrayList<>();
        /*for (int i = 0; i < arry.size(); i++) {
            WorkFlowLogVO workFlowLogVO = arry.get(i);
            long times = this.times(arry.get(i).getDbegin(), arry.get(i).getDend() == null ? DateUtils.getSysdateTime() : arry.get(i).getDend());
            workFlowLogVO.setTimes(String.valueOf(times));
            dataList.add(workFlowLogVO);
        }*/
        //list.setRecords(dataList);
        QmPage<WorkFlowLogVO> qmPage = TableUtils.convertQmPageFromMpPage(list);
        return qmPage;
    }


    public long times(Date startTime, Date endTime) {
        final Calendar cal = Calendar.getInstance();
        cal.setTime(startTime);
        final long time1 = cal.getTimeInMillis();
        cal.setTime(endTime);
        final long time2 = cal.getTimeInMillis();
        final long min = (time2 - time1) / 1000;
        return min;
    }

    @Override
    public WorkFlowLogDO queryByBatchId(String batchId) {
        QmQueryWrapper<WorkFlowLogDO> queryWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<WorkFlowLogDO> lambdaWrapper = queryWrapper.lambda();
        lambdaWrapper.eq(WorkFlowLogDO::getBatchId, batchId);
        return workFlowLogMapper.selectOne(lambdaWrapper);
    }

    private String buildLogLock(String param) {
        StringBuilder sb = new StringBuilder();
        sb.append(param);
        return sb.toString().intern();
    }

    public List<WorkFlowLogDO> queryData(String sqlfy) {
        return workFlowLogMapper.queryData(sqlfy);
    }

    /**
     * 批量日志
     *
     * @param batchCompleteInfo 审核请求参数
     * @return 保存后日志对象
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBatchCompleteInit(Map<String, Object> batchCompleteInfo) {
        String batchId = batchCompleteInfo.get("batchId").toString();
        try {
            //数据存储key
            int batchTotal = Integer.parseInt(batchCompleteInfo.get("batchTotal").toString());
            String logLock = buildLogLock(batchId);
            synchronized (logLock) {
                List<String> businessKeys = (List<String>) batchCompleteInfo.get(FlowableConstant.BUSINESS_KEYS);
                WorkFlowLogDO batchCompleteLogDO = queryByBatchId(batchId);
                if (null == batchCompleteLogDO) {
                    batchCompleteLogDO = new WorkFlowLogDO();
                    if (null != batchCompleteInfo.get("transCode"))
                        batchCompleteLogDO.setVtranscode(batchCompleteInfo.get("transCode").toString());
                    if (null != batchCompleteInfo.get("menuName"))
                        batchCompleteLogDO.setVmenuname(batchCompleteInfo.get("menuName").toString());
                    JSONObject jsonObj = new JSONObject(batchCompleteInfo);
                    batchCompleteLogDO.setVpara(jsonObj.toString());

                    LoginKeyDO loginKeyDO = BootAppUtil.getLoginKey();
                    batchCompleteLogDO.setNopr(loginKeyDO.getOperatorId());
                    batchCompleteLogDO.setVopr(loginKeyDO.getPersonCode());
                    batchCompleteLogDO.setNcompanyid(loginKeyDO.getCompanyId());
                    batchCompleteLogDO.setVoprname(loginKeyDO.getOperatorName());

                    batchCompleteLogDO.setDbegin(DateUtils.getSysdateTime());
                    batchCompleteLogDO.setDpulse(DateUtils.getSysdateTime());
                    batchCompleteLogDO.setNprocess(0);
                    batchCompleteLogDO.setBatchId(batchId);
                    batchCompleteLogDO.setTraceid(MDC.get("traceId"));
                    batchCompleteLogDO.setDtstamp(DateUtils.getSysTimestamp());
                    batchCompleteLogDO.setBusinesskey(String.join(",", businessKeys));
                    batchCompleteLogDO.setButtonname(batchCompleteInfo.get("name").toString());
                    batchCompleteLogDO.setNbatchcount(batchTotal);
                    batchCompleteLogDO.setProcessdefkey(batchCompleteInfo.get(FlowableConstant.PROCESS_DEFINITION_KEY).toString());
                    this.saveOrUpdate(batchCompleteLogDO);
                }
            }
        } catch (Exception ex) {
            log.warn("记录审核日志失败saveBatchCompleteInit！" + ex.getMessage(), ex);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBatchProcess(Map<String, Object> batchCompleteInfo, int failNum, int successNum, String msg) {
        String batchId = batchCompleteInfo.get("batchId").toString();
        int batchTotal = Integer.parseInt(batchCompleteInfo.get("batchTotal").toString());
        String logLock = buildLogLock(batchId);
        try {
            synchronized (logLock) {
                List<String> businessKeys = (List<String>) batchCompleteInfo.get(FlowableConstant.BUSINESS_KEYS);
                String sql = "select * from act_ex_batchlog a where a.VBATCHID = '" + batchId + "' for update";
                List<WorkFlowLogDO> batchList = this.queryData(sql);
                if (!batchList.isEmpty()) {
                    WorkFlowLogDO batchCompleteLogDO = batchList.get(0);
                    int nProcess = batchCompleteLogDO.getNprocess();
                    int currentTotal = batchCompleteLogDO.getCompleteNum() + businessKeys.size();
                    if (currentTotal == batchTotal) {
                        batchCompleteLogDO.setDend(DateUtils.getSysdateTime());
                        batchCompleteLogDO.setNprocess(100);
                        if (batchCompleteLogDO.getSuccessNum() + successNum == batchTotal) {
                            String message = i18nUtil.getMessage("MSG.wf.common.success");
                            batchCompleteLogDO.setResult(batchCompleteInfo.get("name") + message);
                        }
                    } else {
                        nProcess = (int) Math.min(nProcess + Math.ceil(businessKeys.size() * 100.0 / batchTotal), 95);
                        batchCompleteLogDO.setNprocess(nProcess);
                    }
                    batchCompleteLogDO.setDpulse(DateUtils.getSysdateTime());
                    batchCompleteLogDO.setFailNum(batchCompleteLogDO.getFailNum() + failNum);
                    batchCompleteLogDO.setSuccessNum(batchCompleteLogDO.getSuccessNum() + successNum);
                    batchCompleteLogDO.setCompleteNum(currentTotal);
                    batchCompleteLogDO.setBusinesskey(batchCompleteLogDO.getBusinesskey() + ";" + String.join(",", businessKeys));
                    if (failNum > 0) {
                        if (BootAppUtil.isnotNullOrEmpty(batchCompleteLogDO.getResult()))
                            batchCompleteLogDO.setResult(batchCompleteLogDO.getResult() + ";" + msg);
                        else
                            batchCompleteLogDO.setResult(msg);
                    }
                    this.saveOrUpdate(batchCompleteLogDO);
                }
            }
        } catch (Exception ex) {
            log.warn("记录批量审核日志失败saveBatchProcess！" + ex.getMessage(), ex);
        }
    }
}
