package com.qm.ep.wf.controller.v2;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.qm.ep.wf.common.FlowablePage;
import com.qm.ep.wf.constant.FlowableConstant;
import com.qm.ep.wf.controller.ProcessInstanceController;
import com.qm.ep.wf.domain.bean.ProcessCommonButtonsDO;
import com.qm.ep.wf.domain.vo.HistoricProcessInstanceResponse;
import com.qm.ep.wf.service.FlowableTaskService;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.util.BootAppUtil;
import org.flowable.task.api.Task;
import org.flowable.task.api.TaskQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/4/2
 */
@RestController
public class ProcessInstanceV2Controller extends ProcessInstanceController {

    @Autowired
    protected FlowableTaskService flowableTaskService;

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public JsonResultVo getProcessTaskButtons(@RequestBody Map<String, Object> requestParams) {
//        return super.getProcessTaskButtons(requestParams);
        System.out.println("------getProcessTaskButtons---START----");
        JsonResultVo resultObj = new JsonResultVo();
        List<String> businessKeys = (List<String>) requestParams.get("businessKeys");
        String processDefKey = requestParams.get("processDefinitionKey").toString();
        String transCode = requestParams.get("transCode").toString();
        if (businessKeys.isEmpty()) {

            return resultObj;

        }
        if (businessKeys.size() == 1) {
            Map<String, String> searchParams = new HashMap<>();
            searchParams.put(FlowableConstant.PROCESS_DEFINITION_KEY, processDefKey);
            if (CollectionUtils.isEmpty(businessKeys)) {
                return resultObj;
            }

            Map<String, Object> map = Maps.newHashMap();
            List<HistoricProcessInstanceResponse> historicProcessInstances = Lists.newArrayList();
            for (String businessKey : businessKeys) {
                searchParams.put(FlowableConstant.BUSINESS_KEY, businessKey);
                JsonResultVo result = list(searchParams);
                if (ObjectUtils.isEmpty(result)) {
                    map.put(businessKey, new ArrayList<>());
                    continue;
                }
                FlowablePage flowablePage = (FlowablePage) result.getData();
                historicProcessInstances.addAll(flowablePage.getRecords());
            }
            Map<String, Collection<ProcessCommonButtonsDO>> buttonsMaps = processInstanceService.handleProcessInstanceTaskButtons(historicProcessInstances, transCode);
            map.putAll(buttonsMaps);
            resultObj.setData(map);
            return resultObj;
        } else {
            List<Map> hisProcessInstances = new ArrayList<>();
            //业务单据历史流程实例批量查询 hyh start
            hisProcessInstances = flowableTaskService.hisProcInsQueryByBusinessKeys(processDefKey, businessKeys);
            //从list集合中，取出字段name的列表
            List<String> processInstanceIds = hisProcessInstances.stream().map(d -> d.get("PROC_INST_ID_").toString()).collect(Collectors.toList());
            TaskQuery taskQuery = taskService.createTaskQuery();
            if (BootAppUtil.isnotNullOrEmpty(processDefKey)) {
                taskQuery.processDefinitionKey(processDefKey);
            }
            if (processInstanceIds.isEmpty())
                return resultObj;
            taskQuery.processInstanceIdIn(processInstanceIds);
            List<Task> taskList;
            //批量获取待办任务
            taskList = taskQuery.list();
            //这里按照任务定义key进行分组
            Map<String, List<Task>> taskGroupMap = taskList.stream().collect(Collectors.groupingBy(Task::getTaskDefinitionKey));
            Map<String, String> uMap = new HashMap<>();
            for (Map item : hisProcessInstances) {
                String key = item.get("PROC_INST_ID_").toString();
                String value = item.get("BUSINESS_KEY_").toString();
                uMap.put(key, value);
            }
            //预先处理数据 --> {1234  [1234,2345,5676]}
            Map<String, List<String>> preHandleMap = new HashMap<>();
            for (String key : taskGroupMap.keySet()) {
                List<Task> tasks = taskGroupMap.get(key);
                for (Task task : tasks) {
                    String processInstanceId = task.getProcessInstanceId();
                    String businessKey = uMap.get(processInstanceId);

                    String keyOne = uMap.get(tasks.get(0).getProcessInstanceId());
                    if (preHandleMap.containsKey(keyOne)) {
                        List<String> list = preHandleMap.get(keyOne);
                        list.add(businessKey);
                    } else {
                        List<String> list = new ArrayList<>();
                        list.add(businessKey);
                        preHandleMap.put(businessKey, list);
                    }
                }
            }
            Set<String> businessKeyset = preHandleMap.keySet();

            Map<String, Object> map = Maps.newHashMap();
            List<HistoricProcessInstanceResponse> historicProcessInstances = Lists.newArrayList();
            for (String businessKey : businessKeyset) {
                JsonResultVo result = getListResult(businessKey, hisProcessInstances);
                if (ObjectUtils.isEmpty(result)) {
                    map.put(businessKey, new ArrayList<>());
                    continue;
                }
                //FlowablePage flowablePage = (FlowablePage) result.getData();
                List dataList = result.getDataList();
                historicProcessInstances.addAll(dataList);
            }
            Map<String, Collection<ProcessCommonButtonsDO>> buttonsMaps = processInstanceService.handleProcessInstanceTaskButtons(historicProcessInstances, transCode);
            //拿到每一个组的buttons
            for (String businessKey : preHandleMap.keySet()) {
                List<String> list = preHandleMap.get(businessKey);
                Collection<ProcessCommonButtonsDO> processCommonButtonsDOS = buttonsMaps.get(businessKey);
                if (!processCommonButtonsDOS.isEmpty()) {
                    for (String item : list) {
                        map.put(item, processCommonButtonsDOS);
                    }

                }

            }
            resultObj.setData(map);
            return resultObj;
        }

    }

    private JsonResultVo getListResult(String businessKey, List<Map> hisProcessInstances) {
        JsonResultVo resultObj = new JsonResultVo();

        List<HistoricProcessInstanceResponse> list = new ArrayList<>();
        for (Map itemMap : hisProcessInstances) {
            if (businessKey.equals(itemMap.get("BUSINESS_KEY_"))) {
                String id = itemMap.get("ID_").toString();
                String business_key = itemMap.get("BUSINESS_KEY_").toString();
                String procDefKey = itemMap.get("PROC_DEF_KEY_").toString();
                HistoricProcessInstanceResponse bean = new HistoricProcessInstanceResponse();
                bean.setId(id);
                bean.setBusinessKey(business_key);
                bean.setProcessDefinitionKey(procDefKey);
                list.add(bean);
            }


        }
        //resultObj.setData(list);
        resultObj.setDataList(list);


        return resultObj;
    }


}
