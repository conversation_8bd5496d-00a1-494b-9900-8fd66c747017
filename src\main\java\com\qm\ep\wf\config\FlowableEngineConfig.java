package com.qm.ep.wf.config;

import lombok.extern.slf4j.Slf4j;
import org.flowable.image.ProcessDiagramGenerator;
import org.flowable.spring.SpringProcessEngineConfiguration;
import org.flowable.spring.boot.EngineConfigurationConfigurer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR> @date
 */
@Configuration
@Slf4j
public class FlowableEngineConfig implements EngineConfigurationConfigurer<SpringProcessEngineConfiguration> {
    @Value("${flowableFontName}")
    private String flowableFontName;

    @Override
    public void configure(SpringProcessEngineConfiguration engineConfiguration) {
        log.info("---error--"+"流程引擎配置+FlowableEngineConfig");
        engineConfiguration.setProcessDiagramGenerator(processDiagramGenerator());
        engineConfiguration.setActivityFontName(flowableFontName);
        engineConfiguration.setLabelFontName(flowableFontName);
        engineConfiguration.setAnnotationFontName(flowableFontName);
        log.info("---error--"+"流程引擎数据库初始化------------------------------------");
    }

    @Bean
    public ProcessDiagramGenerator processDiagramGenerator() {
        return new CustomProcessDiagramGenerator();
    }
}
