package com.qm.ep.wf.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@SuppressWarnings("squid:S00116")
@ApiModel(value = "角色VO", description = "")
@Data
public class RoleVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "角色代码")
    private String vrolecode;

    @ApiModelProperty(value = "角色名称")
    private String vrolename;

    @ApiModelProperty(value = "管理员 是  否")
    private String vmanagerflag;

    @ApiModelProperty(value = "公司Id")
    private String ncompanyid;

    @ApiModelProperty(value = "停用标识")
    @TableField("VSTOP")
    private String vstop;
}
