package com.qm.ep.wf.remote;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */

@Component
@Slf4j
public class SysFeignFallbackFactory implements FallbackFactory<SysFeignRemote> {


    @Override
    public SysFeignRemote create(Throwable throwable) {
        SysFeignRemoteHystrix feignRemoteHystrix = new SysFeignRemoteHystrix();
        feignRemoteHystrix.setHystrixEx(throwable);
        return feignRemoteHystrix;
    }
}
