<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qm.ep.wf.mapper.TaskAuthMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qm.ep.wf.domain.bean.TaskAuthDO">
        <id column="ID" property="id"/>
        <result column="TASK_NAME" property="taskName"/>
        <result column="ACTION_TYPE" property="actionType"/>
        <result column="ACTION_CODE" property="actionCode"/>
        <result column="PROCESS_DEF_KEY" property="processDefKey"/>
        <result column="DTSTAMP" property="dtstamp"/>
        <result column="BUTTON_CODE" property="buttonCode"/>
        <result column="TRANS_CODE" property="transCode"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
    ID, TASK_NAME, ACTION_TYPE, ACTION_CODE, PROCESS_DEF_KEY, DTSTAMP, BUTTON_CODE, TRANS_CODE
    </sql>

    <!-- 公共查询 -->
    <sql id="QuerySQL">
        select * from (
            select
                a.TASK_NAME,
                a.ACTION_TYPE,
                a.ACTION_CODE,
                a.PROCESS_DEF_KEY,
                a.ID,
                a.DTSTAMP,
                a.BUTTON_CODE,
                a.TRANS_CODE
            from act_ex_taskactorauth a
        ) innerTable
    </sql>

    <!-- 复写MP自带函数 -->
    <select id="selectByIdNew" resultType="com.qm.ep.wf.domain.bean.TaskAuthDO">
        <include refid="QuerySQL"/>
        where id = #{id}
    </select>
    <select id="selectBatchIdsNew" resultType="com.qm.ep.wf.domain.bean.TaskAuthDO">
        <include refid="QuerySQL"/>
        <if test="coll != null and !coll.isEmpty">
            <where>
                id in (<foreach collection="coll" item="item" separator=",">#{item}</foreach>)
            </where>
        </if>
    </select>
    <select id="selectByMapNew" resultType="com.qm.ep.wf.domain.bean.TaskAuthDO">
        <include refid="QuerySQL"/>
        <if test="cm != null and !cm.isEmpty">
            <where>
                <foreach collection="cm" index="k" item="v" separator="AND">
                    <choose>
                        <when test="v == null">${k} IS NULL</when>
                        <otherwise>${k} = #{v}</otherwise>
                    </choose>
                </foreach>
            </where>
        </if>
    </select>
    <select id="selectOne" resultType="com.qm.ep.wf.domain.bean.TaskAuthDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectCount" resultType="java.lang.Integer">
        select count(1) from (<include refid="QuerySQL"/>${ew.customSqlSegment} ) countTable
    </select>
    <select id="selectList" resultType="com.qm.ep.wf.domain.bean.TaskAuthDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectMaps" resultType="com.qm.ep.wf.domain.bean.TaskAuthDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectObjs" resultType="com.qm.ep.wf.domain.bean.TaskAuthDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectPage" resultType="com.qm.ep.wf.domain.bean.TaskAuthDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectMapsPage" resultType="com.qm.ep.wf.domain.bean.TaskAuthDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>

    <!-- 根据任务id、人员code、流程定义key判断是否有权限 -->
    <select id="judgeTaskAuthForUser" parameterType="com.qm.ep.wf.domain.dto.TaskAuthDTO"
            resultType="com.qm.ep.wf.domain.dto.TaskAuthDTO">
        SELECT
        *
        FROM
        act_ex_taskactorauth ta
        WHERE
        ta.PROCESS_DEF_KEY = #{processDefKey}
        AND ta.TASK_NAME = #{taskName}
        AND ta.ACTION_TYPE = 0
        <if test="actionCode != null">
            AND ta.ACTION_CODE = #{actionCode}
        </if>
        <if test="buttonCode != null">
            AND ta.BUTTON_CODE = #{buttonCode}
        </if>
        <if test="transCode !=null">
            AND ta.TRANS_CODE =#{transCode}
        </if>
    </select>
    <select id="selectTaskAuthByFiveParmas" parameterType="com.qm.ep.wf.domain.dto.TaskAuthDTO"
            resultType="com.qm.ep.wf.domain.bean.TaskAuthDO">
        SELECT
        *
        FROM
        act_ex_taskactorauth ta
        WHERE
        ta.PROCESS_DEF_KEY = #{processDefKey}
        AND ta.TASK_NAME = #{taskName}
        <if test="actionCode != null">
            AND ta.ACTION_CODE = #{actionCode}
        </if>
        <if test="buttonCode != null">
            AND ta.BUTTON_CODE = #{buttonCode}
        </if>
        <if test="transCode !=null">
            AND ta.TRANS_CODE =#{transCode}
        </if>
    </select>
</mapper>
