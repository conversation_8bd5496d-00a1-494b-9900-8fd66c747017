package com.qm.ep.wf.controller;

import com.qm.ep.wf.domain.bean.ProcessCounterSignDO;
import com.qm.ep.wf.domain.dto.ProcessCounterSignDTO;
import com.qm.ep.wf.domain.dto.UpdateCounterSignTaskKeyDTO;
import com.qm.ep.wf.service.ProcessCounterSignService;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.I18nUtil;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Controller
 * JsonResultVo
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-18
 */
@RestController
@RequestMapping("/processCounterSign")
public class ProcessCounterSignController extends BaseController {

    @Autowired
    private ProcessCounterSignService processCounterSignService;
    @Autowired
    private I18nUtil i18nUtil;

    /**
     * 使用系统默认的保存/修改 方法
     */
    @ApiOperation(value = "使用系统默认的保存/修改 方法", notes = "[author:10027705]")
    @PostMapping("/save")
    public JsonResultVo<ProcessCounterSignDO> save(@RequestBody ProcessCounterSignDO tempDO) {
        return processCounterSignService.customeSaveOrUpdate(tempDO);
    }

    /**
     * 根据传入的id删除数据
     */
    @ApiOperation(value = "根据传入的id删除数据", notes = "[author:10027705]")
    @PostMapping("/deleteById")
    public JsonResultVo<ProcessCounterSignDO> deleteById(@RequestBody ProcessCounterSignDO tempDO) {
        JsonResultVo<ProcessCounterSignDO> resultObj = new JsonResultVo<>();
        boolean flag = processCounterSignService.removeById(tempDO.getId());
        if (flag) {
            String message = i18nUtil.getMessage("MSG.wf.common.delSuccess");
            resultObj.setMsg(message);
        } else {
            String message = i18nUtil.getMessage("ERR.wf.common.delFail");
            resultObj.setMsgErr(message);
        }
        return resultObj;
    }

    /**
     * 根据传入的map删除信息
     */
    @ApiOperation(value = "根据传入的map删除信息", notes = "[author:10027705]")
    @PostMapping("/deleteByMap")
    public JsonResultVo<ProcessCounterSignDO> deleteByMap(@RequestBody Map<String, Object> map) {
        JsonResultVo<ProcessCounterSignDO> resultObj = new JsonResultVo<>();
        boolean flag = processCounterSignService.removeByMap(map);
        if (flag) {
            String message = i18nUtil.getMessage("ERR.wf.ProcessCommonButtonsController.operateSuccess");
            resultObj.setMsg(message);
        } else {
            String message = i18nUtil.getMessage("MSG.wf.ProcessCommonButtonsController.operateFail");
            resultObj.setMsgErr(message);
        }
        return resultObj;
    }

    /**
     * 根据传入的实体信息进行查询
     */
    @ApiOperation(value = "根据传入的实体信息进行查询", notes = "[author:10027705]")
    @PostMapping("/table")
    public JsonResultVo<QmPage<ProcessCounterSignDO>> table(@RequestBody ProcessCounterSignDTO tempDTO) {
        //定义查询构造器
        QmQueryWrapper<ProcessCounterSignDO> queryWrapper = new QmQueryWrapper<>();
        //主键ID
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getId())) {
            queryWrapper.eq("id", tempDTO.getId());
        }
        //会签组名称
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getGrpName())) {
            queryWrapper.like("grpName", tempDTO.getGrpName());
        }
        //人工任务名称
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getTaskKey())) {
            queryWrapper.eq("taskKey", tempDTO.getTaskKey());
        }
        //流程定义Key 对应bpmn中process的ID
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getProcessDefKey())) {
            queryWrapper.eq("processDefKey", tempDTO.getProcessDefKey());
        }
        //会签类型，（比例通过制、一票否决制、自定义）
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getType())) {
            queryWrapper.eq("type", tempDTO.getType());
        }
        //自定义属性
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getCustomValue())) {
            queryWrapper.eq("customValue", tempDTO.getCustomValue());
        }
        //比例
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getRate())) {
            queryWrapper.eq("rate", tempDTO.getRate());
        }
        //查询数据，使用table函数。
        QmPage<ProcessCounterSignDO> list = processCounterSignService.table(queryWrapper, tempDTO);
        JsonResultVo<QmPage<ProcessCounterSignDO>> ret = new JsonResultVo<>();
        ret.setData(list);
        return ret;
    }

    @ApiOperation(value = "根据传入的流程定义key数组查询", notes = "[author:10027705]")
    @PostMapping("/tableByProcessDefKeys")
    public JsonResultVo<ProcessCounterSignDO> tableByProcessDefKeys(@RequestBody List<String> processDefKeys) {
        //定义查询构造器
        QmQueryWrapper<ProcessCounterSignDO> queryWrapper = new QmQueryWrapper<>();
        //流程定义Key 对应bpmn中process的ID
        if (!BootAppUtil.isNullOrEmpty(processDefKeys)) {
            queryWrapper.in("processDefKey", processDefKeys);
        }
        //查询数据，使用table函数。
        QmPage<ProcessCounterSignDO> list = processCounterSignService.table(queryWrapper, new ProcessCounterSignDTO());
        JsonResultVo<ProcessCounterSignDO> ret = new JsonResultVo<>();
        ret.setDataList(list.getItems());
        return ret;
    }

    @ApiOperation(value = "根据流程定义key查询分组的会签策略数据", notes = "[author:10027705]")
    @PostMapping("/selectCountersignGroupName")
    public JsonResultVo<ProcessCounterSignDO> selectCountersignGroupName(@RequestBody ProcessCounterSignDTO processCounterSignDTO) {
        JsonResultVo<ProcessCounterSignDO> ret = new JsonResultVo<>();
        ret.setDataList(processCounterSignService.selectCountersignGroupName(processCounterSignDTO));
        return ret;
    }

    @ApiOperation(value = "修改同流程、同会前策略名称的所有数据", notes = "[author:10027705]")
    @PostMapping("/updateCountersignByKey")
    public JsonResultVo<ProcessCounterSignDO> updateCountersignByKey(@RequestBody ProcessCounterSignDO tempDO) {
        return processCounterSignService.updateCountersignByKey(tempDO);
    }

    @ApiOperation(value = "修改同流程、同会前策略名称的所有数据", notes = "[author:10027705]")
    @PostMapping("/updateCountersignById")
    public JsonResultVo<String> updateTaskKeyById(@RequestBody List<UpdateCounterSignTaskKeyDTO> dtoList) {
        return processCounterSignService.updateTaskKeyById(dtoList);
    }
}
