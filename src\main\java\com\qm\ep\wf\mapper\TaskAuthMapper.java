package com.qm.ep.wf.mapper;

import com.qm.ep.wf.domain.bean.TaskAuthDO;
import com.qm.ep.wf.domain.dto.TaskAuthDTO;
import com.qm.tds.api.mp.mapper.QmBaseMapper;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-17
 */
public interface TaskAuthMapper extends QmBaseMapper<TaskAuthDO> {

    /**
     * @description 根据任务id、人员code、流程定义key判断是否有权限
     * <AUTHOR>
     * @date 2020/8/21 16:16
     */
    public List<TaskAuthDTO> judgeTaskAuthForUser(TaskAuthDTO taskAuthDTO);

    public List<TaskAuthDO> selectTaskAuthByFiveParmas(TaskAuthDO taskAuthDTO);
}
