package com.qm.ep.wf.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-18
 */
@ApiModel(value = "ProcessCounterSignVO对象", description = "")
@Data
public class ProcessCounterSignVO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private String id;

    @ApiModelProperty(value = "会签组名称")
    private String grpName;

    @ApiModelProperty(value = "人工任务名称")
    private String taskKey;

    @ApiModelProperty(value = "流程定义Key 对应bpmn中process的ID")
    private String processDefKey;

    @ApiModelProperty(value = "会签类型，（比例通过制、一票否决制、自定义）")
    private String type;

    @ApiModelProperty(value = "自定义属性")
    private String customValue;

    @ApiModelProperty(value = "比例")
    private String rate;

    @ApiModelProperty(value = "时间戳")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Timestamp dtstamp;
}
