package com.qm.ep.wf.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR> @date
 */
@ApiModel(description = "任务请求")
@Data
public class TaskRequest {
    @ApiModelProperty("任务 ID")
    private String taskId;
    @ApiModelProperty("用户 ID")
    private String userId;
    @ApiModelProperty("消息")
    private String message;
    @ApiModelProperty("活动 ID")
    private String activityId;
    @ApiModelProperty("活动名称")
    private String activityName;
    @ApiModelProperty("收件人")
    private MultipartFile[] attchemnt;
    @ApiModelProperty("值")
    private Map<String, Object> values;
    @ApiModelProperty("组 ID")
    private String groupId;
    @ApiModelProperty("消息时间")
    private Date messageTime;
    @ApiModelProperty("作器")
    private String oprtor;
    @ApiModelProperty("暂无消息")
    private boolean noMessage;
    @ApiModelProperty("数据OPR类型")
    private String oprType;
    @ApiModelProperty("有评论")
    private boolean hasComment;
    @ApiModelProperty("删除最后一条评论")
    private boolean deleteLastComment;
    @ApiModelProperty("源活动 ID")
    private String sourceActivityId;
    @ApiModelProperty("进程定义键")
    private String processDefKey;
    @ApiModelProperty("法典")
    private String code;
}
