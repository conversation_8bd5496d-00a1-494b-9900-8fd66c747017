package com.qm.ep.wf.util;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/7
 */
@EqualsAndHashCode
@Slf4j
public class SynchronizedLockObj<T> {

    private static volatile SynchronizedLockObj synchronizedLockUtil;

    private SynchronizedLockObj() {

    }


    public static SynchronizedLockObj getInstance() {
        if (synchronizedLockUtil != null) {
            return synchronizedLockUtil;
        }
        synchronized (SynchronizedLockObj.class) {
            if (synchronizedLockUtil == null) {
                synchronizedLockUtil = new SynchronizedLockObj();
            }
        }
        return synchronizedLockUtil;
    }

    @Setter
    @Getter
    private T obj;

}
