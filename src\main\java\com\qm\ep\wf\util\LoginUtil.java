package com.qm.ep.wf.util;

import com.qm.ep.wf.remote.SysFeignRemote;
import com.qm.tds.base.domain.LoginKeyDO;
import com.qm.tds.util.BootAppUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 获取登录信息工具类
 */
@Slf4j
@Component
public class LoginUtil {

    @Autowired
    private SysFeignRemote sysFeignRemote;

    /**
     * 获取当前登录用户的ID
     *
     * @return
     */
    public static String getUserId() {
        LoginKeyDO loginUser = BootAppUtil.getLoginKey();
        return loginUser.getPersonCode();
    }

    /**
     * 获取当前登录用户的账号
     *
     * @return
     */
    public static String getUsername() {
        return BootAppUtil.getLoginKey().getPersonCode();
    }

    public static String getOperatorId() {
        return BootAppUtil.getLoginKey().getOperatorId();
    }
}
