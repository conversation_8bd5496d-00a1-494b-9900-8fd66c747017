package com.qm.ep.wf.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;
import java.util.Date;

/**
 * <p>
 * 导出Excel日志
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-25
 */
@ApiModel(value = "workFlowVO对象", description = "导出Excel日志")
@Data
public class WorkFlowLogVO {

    private static final long serialVersionUID = 1L;

    private String id;

    @ApiModelProperty(value = "事务码代码")
    private String vtranscode;

    @ApiModelProperty(value = "用例名称、菜单名称")
    private String vmenuname;

    @ApiModelProperty(value = "公司ID")
    private String ncompanyid;

    @ApiModelProperty(value = "操作员ID")
    private String nopr;

    @ApiModelProperty(value = "操作员代码")
    private String vopr;

    @ApiModelProperty(value = "操作员名称")
    private String voprname;

    @ApiModelProperty(value = "开始时间")
    private Date dbegin;

    @ApiModelProperty(value = "结束时间")
    private Date dend;

    @ApiModelProperty(value = "心跳时间。判断后台进程是否已经中断")
    private Date dpulse;

    @ApiModelProperty(value = "进度")
    private Integer nprocess;

    @ApiModelProperty(value = "流程定义Key")
    private String processdefkey;

    @ApiModelProperty(value = "业务主键Key")
    private String businesskey;

    @ApiModelProperty(value = "审批按钮名称")
    private String buttonname;

    @ApiModelProperty(value = "TraceId")
    private String traceid;

    @ApiModelProperty(value = "执行条数")
    private Integer nbatchcount;

    @ApiModelProperty(value = "执行参数")
    private String vpara;

    @ApiModelProperty(value = "时间戳")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Timestamp dtstamp;

    @ApiModelProperty(value = "删除标识")
    private String vdelete;

    @ApiModelProperty(value = "执行结果")
    private String result;

    @ApiModelProperty(value = "耗时")
    private String times;

    @ApiModelProperty(value = "批量提交Id")
    private String batchId;

    @ApiModelProperty(value = "批量完成数量")
    private int completeNum;

    @ApiModelProperty(value = "失败数量")
    private int failNum;

    @ApiModelProperty(value = "成功数量")
    private int successNum;
}
