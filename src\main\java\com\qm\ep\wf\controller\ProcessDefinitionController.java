package com.qm.ep.wf.controller;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.qm.ep.wf.common.BaseFlowableController;
import com.qm.ep.wf.common.FlowablePage;
import com.qm.ep.wf.constant.FlowableConstant;
import com.qm.ep.wf.domain.vo.ProcessDefinitionRequest;
import com.qm.ep.wf.domain.vo.ProcessDefinitionResponse;
import com.qm.ep.wf.service.ProcessDefinitionService;
import com.qm.ep.wf.service.WfConfService;
import com.qm.ep.wf.util.EscapeUnescape;
import com.qm.ep.wf.util.LoginUtil;
import com.qm.ep.wf.util.ObjectUtils;
import com.qm.ep.wf.wapper.ProcDefListWrapper;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.exception.QmException;
import com.qm.tds.util.I18nUtil;
import com.qm.tds.util.RedisUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.flowable.common.engine.api.FlowableException;
import org.flowable.common.engine.api.FlowableObjectNotFoundException;
import org.flowable.common.engine.api.query.QueryProperty;
import org.flowable.engine.impl.ProcessDefinitionQueryProperty;
import org.flowable.engine.repository.Deployment;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.repository.ProcessDefinitionQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR> @date
 */
@RestController
@RequestMapping("/flowable/processDefinition")
@Slf4j
@Api(value = "流程定义", tags = {"processDefinition"})
public class ProcessDefinitionController extends BaseFlowableController {
    private static final Map<String, QueryProperty> ALLOWED_SORT_PROPERTIES = new HashMap<>();
    @Autowired
    private ProcessDefinitionService processDefinitionService;
    @Autowired
    private WfConfService wfConfService;
    @Autowired
    private I18nUtil i18nUtil;
    @Autowired
    private RedisUtils redisUtils;


    static {
        ALLOWED_SORT_PROPERTIES.put(FlowableConstant.ID, ProcessDefinitionQueryProperty.PROCESS_DEFINITION_ID);
        ALLOWED_SORT_PROPERTIES.put(FlowableConstant.KEY, ProcessDefinitionQueryProperty.PROCESS_DEFINITION_KEY);
        ALLOWED_SORT_PROPERTIES.put(FlowableConstant.CATEGORY,
                ProcessDefinitionQueryProperty.PROCESS_DEFINITION_CATEGORY);
        ALLOWED_SORT_PROPERTIES.put(FlowableConstant.NAME, ProcessDefinitionQueryProperty.PROCESS_DEFINITION_NAME);
        ALLOWED_SORT_PROPERTIES.put(FlowableConstant.VERSION,
                ProcessDefinitionQueryProperty.PROCESS_DEFINITION_VERSION);
        ALLOWED_SORT_PROPERTIES.put(FlowableConstant.TENANT_ID,
                ProcessDefinitionQueryProperty.PROCESS_DEFINITION_TENANT_ID);
    }

    @GetMapping(value = "/list")
    @ApiOperation(value = "获取流程定义列表", notes = "[author:10027705]")
    public JsonResultVo list(@RequestParam Map<String, String> requestParams) {
        JsonResultVo resultObj = new JsonResultVo();
        ProcessDefinitionQuery processDefinitionQuery = repositoryService.createProcessDefinitionQuery();
        if (ObjectUtils.isNotEmpty(requestParams.get(FlowableConstant.ID))) {
            processDefinitionQuery.processDefinitionId(requestParams.get(FlowableConstant.ID));
        }
        if (ObjectUtils.isNotEmpty(requestParams.get(FlowableConstant.CATEGORY))) {
            processDefinitionQuery.processDefinitionCategoryLike(
                    ObjectUtils.convertToLike(requestParams.get(FlowableConstant.CATEGORY)));
        }
        if (ObjectUtils.isNotEmpty(requestParams.get(FlowableConstant.KEY))) {
            processDefinitionQuery
                    .processDefinitionKeyLike(ObjectUtils.convertToLike(requestParams.get(FlowableConstant.KEY)));
        }
        if (ObjectUtils.isNotEmpty(requestParams.get(FlowableConstant.NAME))) {
            processDefinitionQuery
                    .processDefinitionNameLike(ObjectUtils.convertToLike(requestParams.get(FlowableConstant.NAME)));
        }
        if (ObjectUtils.isNotEmpty(requestParams.get(FlowableConstant.VERSION))) {
            processDefinitionQuery.processDefinitionVersion(
                    ObjectUtils.convertToInteger(requestParams.get(FlowableConstant.VERSION)));
        }
        if (ObjectUtils.isNotEmpty(requestParams.get(FlowableConstant.SUSPENDED))) {
            boolean suspended = ObjectUtils.convertToBoolean(requestParams.get(FlowableConstant.SUSPENDED));
            if (suspended) {
                processDefinitionQuery.suspended();
            } else {
                processDefinitionQuery.active();
            }
        }
        if (ObjectUtils.isNotEmpty(requestParams.get(FlowableConstant.LATEST_VERSION))) {
            boolean latest = ObjectUtils.convertToBoolean(requestParams.get(FlowableConstant.LATEST_VERSION));
            if (latest) {
                processDefinitionQuery.latestVersion();
            }
        }
        if (ObjectUtils.isNotEmpty(requestParams.get(FlowableConstant.STARTABLE_BY_USER))) {
            processDefinitionQuery.startableByUser(requestParams.get(FlowableConstant.STARTABLE_BY_USER));
        }
        if (ObjectUtils.isNotEmpty(requestParams.get(FlowableConstant.TENANT_ID))) {
            processDefinitionQuery.processDefinitionTenantId(requestParams.get(FlowableConstant.TENANT_ID));
        }
        // 根据模块查询
        if (ObjectUtils.isNotEmpty(requestParams.get(FlowableConstant.MODULE))) {
            Set<String> modules = new HashSet<>();
            try {
                modules = wfConfService.getWfConfByModule(requestParams.get(FlowableConstant.MODULE), 1);
                if (modules.isEmpty()) {
                    modules.add("non");
                }
            } catch (FlowableObjectNotFoundException e) {
                log.debug(e.getMessage(), e);
                modules.add("non");
            }
            processDefinitionQuery.processDefinitionIds(modules);
        }
        FlowablePage page = pageList(requestParams, processDefinitionQuery, ProcDefListWrapper.class,
                ALLOWED_SORT_PROPERTIES);
        resultObj.setData(page);
        return resultObj;
    }

    @GetMapping(value = "/listMyself")
    @ApiOperation(value = "获取指定用户流程定义列表", notes = "[author:10027705]")
    public JsonResultVo listMyself(@RequestParam Map<String, String> requestParams) {
        JsonResultVo resultObj = new JsonResultVo();
        ProcessDefinitionQuery processDefinitionQuery = repositoryService.createProcessDefinitionQuery();
        if (ObjectUtils.isNotEmpty(requestParams.get(FlowableConstant.NAME))) {
            processDefinitionQuery
                    .processDefinitionNameLike(ObjectUtils.convertToLike(requestParams.get(FlowableConstant.NAME)));
        }
        FlowablePage page = pageList(requestParams, processDefinitionQuery, ProcDefListWrapper.class,
                ALLOWED_SORT_PROPERTIES);
        resultObj.setData(page);
        return resultObj;
    }

    @GetMapping(value = "/queryById")
    @ApiOperation(value = "根据流程定义ID，获取流程定义信息", notes = "[author:10027705]")
    public JsonResultVo queryById(@RequestParam String processDefinitionId) {
        JsonResultVo resultObj = new JsonResultVo();
        permissionService.validateReadPermissionOnProcessDefinition(LoginUtil.getOperatorId(), processDefinitionId);
        ProcessDefinition processDefinition = processDefinitionService.getProcessDefinitionById(processDefinitionId);
        String formKey = null;
        if (processDefinition.hasStartFormKey()) {
            formKey = formService.getStartFormKey(processDefinitionId);
        }
        ProcessDefinitionResponse processDefinitionResponse = responseFactory
                .createProcessDefinitionResponse(processDefinition, formKey);
        resultObj.setData(processDefinitionResponse);
        return resultObj;
    }

    @GetMapping(value = "/renderedStartForm")
    @ApiOperation(value = "渲染表单", notes = "[author:10027705]")
    public JsonResultVo renderedStartForm(@RequestParam String processDefinitionId) {
        JsonResultVo resultObj = new JsonResultVo();
        permissionService.validateReadPermissionOnProcessDefinition(LoginUtil.getOperatorId(), processDefinitionId);
        Object renderedStartForm = formService.getRenderedStartForm(processDefinitionId);
        renderedStartForm = EscapeUnescape.escape(renderedStartForm.toString());
        boolean showBusinessKey = isShowBusinessKey(processDefinitionId);
        resultObj.setData(ImmutableMap.of("renderedStartForm", renderedStartForm, "showBusinessKey", showBusinessKey));
        return resultObj;
    }

    @GetMapping(value = "/image")
    @ApiOperation(value = "流程图", notes = "[author:10027705]")
    public ResponseEntity<byte[]> image(@RequestParam String processDefinitionId) {
        permissionService.validateReadPermissionOnProcessDefinition(LoginUtil.getOperatorId(), processDefinitionId);
        ProcessDefinition processDefinition = processDefinitionService.getProcessDefinitionById(processDefinitionId);
        InputStream imageStream = repositoryService.getProcessDiagram(processDefinition.getId());
        if (imageStream == null) {
            throw new FlowableException(
                    messageFormat("Process definition image is not found with id {0}", processDefinitionId));
        }
        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.setContentType(MediaType.IMAGE_PNG);
        try {
            return new ResponseEntity<>(IOUtils.toByteArray(imageStream), responseHeaders, HttpStatus.OK);
        } catch (Exception e) {
            throw new FlowableException(
                    messageFormat("Process definition image read error with id {0}", processDefinitionId), e);
        }
    }

    @GetMapping(value = "/xml")
    @ApiOperation(value = "获取流程定义XML信息", notes = "[author:10027705]")
    public ResponseEntity<byte[]> xml(@RequestParam String processDefinitionId) {
        permissionService.validateReadPermissionOnProcessDefinition(LoginUtil.getOperatorId(), processDefinitionId);
        ProcessDefinition processDefinition = processDefinitionService.getProcessDefinitionById(processDefinitionId);
        String deploymentId = processDefinition.getDeploymentId();
        String resourceId = processDefinition.getResourceName();
        validateDownloadIds(deploymentId, resourceId, processDefinitionId);
        Deployment deployment = repositoryService.createDeploymentQuery().deploymentId(deploymentId).singleResult();
        validateDeployment(deployment, deploymentId);


        List<String> resourceList = repositoryService.getDeploymentResourceNames(deploymentId);
        if (ObjectUtils.isEmpty(resourceList) || !resourceList.contains(resourceId)) {
            throw new FlowableException(messageFormat(
                    "Process definition resourceId {0} is not found with deploymentId {1}", resourceId, deploymentId));
        }
        InputStream resourceStream = repositoryService.getResourceAsStream(deploymentId, resourceId);
        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.setContentType(MediaType.TEXT_XML);
        try {
            return new ResponseEntity<>(IOUtils.toByteArray(resourceStream), responseHeaders, HttpStatus.OK);
        } catch (Exception e) {
            log.info("---error--"+"获取流程定义XML信息异常", e);
            throw new FlowableException(messageFormat("ProcessDefinition xml read error with id {0}", deploymentId), e);
        }
    }

    @PostMapping(value = "/downloads")
    @ApiOperation(value = "下载", notes = "[author:10027705]")
    public void downloads(HttpServletResponse response, @RequestBody String[] ids) {
        if (ids.length == 0) {
            String message = i18nUtil.getMessage("ERR.wf.ProcessDefinitionController.processIdNull");
            throw new QmException(message);
        }

        try (ZipOutputStream zipOutputStream = new ZipOutputStream(response.getOutputStream())) {

            for (String processDefinitionId : ids) {
                permissionService.validateReadPermissionOnProcessDefinition(LoginUtil.getOperatorId(), processDefinitionId);
                ProcessDefinition processDefinition = processDefinitionService.getProcessDefinitionById(processDefinitionId);
                String deploymentId = processDefinition.getDeploymentId();
                String resourceId = processDefinition.getResourceName();
                InputStream resourceStream = repositoryService.getResourceAsStream(deploymentId, resourceId);
                if (resourceStream == null) {
                    continue;
                }

                ZipEntry entry = new ZipEntry(processDefinition.getName() + "-v" + processDefinition.getVersion() + ".bpmn20.xml");
                zipOutputStream.putNextEntry(entry);
                int len;
                byte[] buffer = new byte[1024];
                while ((len = resourceStream.read(buffer)) > 0) {
                    zipOutputStream.write(buffer, 0, len);
                }
            }

        } catch (IOException e) {
            log.info("---error--"+e.getMessage(), e);
        }
    }

    /**
     * 验证部署ID 资源ID
     *
     * @param deploymentId
     * @param resourceId
     * @param processDefinitionId
     */
    private void validateDownloadIds(String deploymentId, String resourceId, String processDefinitionId) {
        if (deploymentId == null || deploymentId.length() == 0) {
            throw new FlowableException(
                    messageFormat("Process definition deployment id is not found with id {0}", processDefinitionId));
        }
        if (resourceId == null || resourceId.length() == 0) {
            throw new FlowableException(
                    messageFormat("Process definition resource id is not found with id {0}", processDefinitionId));
        }
    }

    private void validateDeployment(Deployment deployment, String deploymentId) {
        if (deployment == null) {
            throw new FlowableException(
                    messageFormat("Process definition deployment is not found with deploymentId {0}", deploymentId));
        }
    }

    @PostMapping(value = "/delete")
    @ApiOperation(value = "删除流程定义", notes = "[author:10027705]")
    public JsonResultVo delete(@RequestBody ProcessDefinitionRequest actionRequest) {
        JsonResultVo resultObj = new JsonResultVo();
        try {
            String processDefinitionId = actionRequest.getProcessDefinitionId();
            Boolean cascade = actionRequest.isIncludeProcessInstances();
            processDefinitionService.delete(processDefinitionId, cascade);
            resultObj.setMsg(i18nUtil.getMessage("MSG.wf.common.delSuccess"));//删除成功！
            resultObj.setCode(JsonResultVo.CODE_OK);
        } catch (Exception ex) {
            resultObj.setMsgErr(ex.getMessage());
        }
        return resultObj;
    }

    @PostMapping(value = "/batchDelete")
    @ApiOperation(value = "删除流程定义", notes = "[author:10027705]")
    public JsonResultVo batchDelete(@RequestBody List<ProcessDefinitionRequest> actionRequest) {
        JsonResultVo resultObj = new JsonResultVo();
        try {
            for (ProcessDefinitionRequest processDefinitionRequest : actionRequest) {
                String processDefinitionId = processDefinitionRequest.getProcessDefinitionId();
                Boolean cascade = processDefinitionRequest.isIncludeProcessInstances();
                processDefinitionService.delete(processDefinitionId, cascade);
            }
            resultObj.setCode(JsonResultVo.CODE_OK);
        } catch (Exception ex) {
            resultObj.setMsgErr(ex.getMessage());
        }
        return resultObj;
    }

    @PostMapping(value = "/activate")
    @ApiOperation(value = "激活流程定义", notes = "[author:10027705]")
    public JsonResultVo activate(@RequestBody ProcessDefinitionRequest actionRequest) {
        JsonResultVo resultObj = new JsonResultVo();
        processDefinitionService.activate(actionRequest);
        resultObj.setCode(JsonResultVo.CODE_OK);
        String message = i18nUtil.getMessage("ERR.wf.ProcessDefinitionController.activateSuccess");
        resultObj.setMsg(message);
        return resultObj;
    }

    @PostMapping(value = "/suspend")
    @ApiOperation(value = "挂起流程定义", notes = "[author:10027705]")
    public JsonResultVo suspend(@RequestBody ProcessDefinitionRequest actionRequest) {
        JsonResultVo resultObj = new JsonResultVo();
        processDefinitionService.suspend(actionRequest);
        resultObj.setCode(JsonResultVo.CODE_OK);
        String message = i18nUtil.getMessage("ERR.wf.ProcessDefinitionController.pendingSuccess");
        resultObj.setMsg(message);
        return resultObj;
    }

    /**
     * 导入流程定义
     *
     * @param request
     * @return
     */
    @PostMapping(value = "/import")
    @ApiOperation(value = "导入流程定义", notes = "[author:10027705]")
    public JsonResultVo doImport(@RequestParam(required = false) String tenantId, HttpServletRequest request) {
        JsonResultVo resultObj = new JsonResultVo();
        processDefinitionService.doImport(tenantId, request);
        resultObj.setCode(JsonResultVo.CODE_OK);
        return resultObj;
    }

    @GetMapping("/select")
    @ApiOperation(value = "查询", notes = "[author:10027705]")
    public JsonResultVo selectProcessDefinition(@RequestParam(required = false) String processDefKey) {
        ProcessDefinitionQuery processDefinitionQuery = repositoryService.createProcessDefinitionQuery();
        if (StringUtils.isNotEmpty(processDefKey)) {
            processDefinitionQuery.processDefinitionKey(processDefKey);
        }
        processDefinitionQuery.latestVersion();
        List<ProcessDefinition> list = processDefinitionQuery.list();

        Map<String, byte[]> res = Maps.newLinkedHashMap();
        for (ProcessDefinition processDefinition : list) {
            InputStream resourceAsStream = repositoryService.getResourceAsStream(processDefinition.getDeploymentId(), processDefinition.getResourceName());
            try {
                byte[] bytes = IOUtils.toByteArray(resourceAsStream);
                res.put(processDefinition.getKey(), bytes);
            } catch (IOException e) {
                log.info("---error--"+e.getMessage(), e);
            }
        }
        JsonResultVo vo = new JsonResultVo();
        vo.setData(res);
        return vo;
    }

    @GetMapping("/redis/del")
    @ApiOperation(value = "删除redis缓存", notes = "[author:10027705]")
    public JsonResultVo deleteRedisProcessDefinitionCache(@RequestParam String key) {
        String keyBuilder = redisUtils.keyBuilder("wf", key);
        Long aLong = redisUtils.delMutil(keyBuilder);
        JsonResultVo vo = new JsonResultVo();
        String message = i18nUtil.getMessage("MSG.wf.common.delSuccess");
        vo.setMsg(message);
        return vo;
    }
}
