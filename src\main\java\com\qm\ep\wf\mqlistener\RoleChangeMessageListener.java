package com.qm.ep.wf.mqlistener;

import com.alibaba.fastjson.JSON;
import com.qm.ep.wf.constant.Constants;
import com.qm.tds.mq.listener.AbstractListener;
import com.qm.tds.util.RedisUtils;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class RoleChangeMessageListener extends AbstractListener {

    @Autowired
    private RedisUtils redisUtils;

    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = Constants.RABBIT_WF_ROLECHANGE_QUEUE,
                    arguments = {@Argument(name = Constants.RABBIT_DEADLETTER_EXCHANGE,
                            value = Constants.RABBIT_WF_DEADLETTER_EXCHANGE),
                            @Argument(name = Constants.RABBIT_DEADLETTER_ROUTINGKEY,
                                    value = Constants.RABBIT_ROLE_DEADLETTER_ROUTINGKEY)}),
            exchange = @Exchange(value = Constants.RABBIT_ROLE_EXCHANGE, type = ExchangeTypes.FANOUT)))
    public void directHandlerManualAck(Message message, Channel channel) {
        log.info("广播队列，手动ACK，接收消息：{}", JSON.toJSONString(message));
        final long deliveryTag = message.getMessageProperties().getDeliveryTag();
        try {
            String roleKey = redisUtils.keyBuilder("wf", "role", "all", "v2");
            redisUtils.del(roleKey);
            //消息手动ACK
            channel.basicAck(deliveryTag, false);
        } catch (Exception e) {
            log.info("---error--"+"确认信息异常", e);
        }
    }
}
