package com.qm.ep.wf.controller;

import com.qm.ep.wf.domain.dto.WfConfDTO;
import com.qm.ep.wf.domain.entity.WfConf;
import com.qm.ep.wf.service.WfConfService;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.util.I18nUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/6/28 9:24
 */
@RestController
@RequestMapping("/flow")
@Api(value = "工作流配置", tags = {"flowConf"})
public class WfConfController extends BaseController {
    @Resource
    private WfConfService wfConfService;
    @Autowired
    private I18nUtil i18nUtil;

    @ApiOperation(value = "工作流配置列表", notes = "[author:10027705]")
    @PostMapping("/getWfConfAll")
    public JsonResultVo<List<WfConf>> getWfConfAll(@RequestBody WfConfDTO confDTO) {
        return getWfConf(confDTO);
    }

    @ApiOperation(value = "根据条件获取工作流配置列表", notes = "[author:10027705]")
    @PostMapping("/query")
    public JsonResultVo<List<WfConf>> getWfConf(@RequestBody WfConfDTO confDTO) {
        JsonResultVo reusltObj = new JsonResultVo();
        reusltObj.setData(wfConfService.getWfConf(confDTO));
        return reusltObj;
    }

    @GetMapping("vwfCode/{vwfCode}")
    @ApiOperation(value = "根据流程代码获取", notes = "[author:10027705]")
    public JsonResultVo<WfConf> getConditionWfConf(@PathVariable("vwfCode") String vwfCode) {
        JsonResultVo reusltObj = new JsonResultVo();
        reusltObj.setData(wfConfService.getConditionWfConf(vwfCode));
        return reusltObj;
    }

    @ApiOperation(value = "修改启用状态", notes = "[author:10027705]")
    @PostMapping("/saveWfConf")
    public JsonResultVo saveWfConf(@RequestBody WfConf wfConf) {
        JsonResultVo reusltObj = new JsonResultVo();

        Map<String, Object> params = new HashMap<>();
        params.put("DepositeApply", wfConf);//流程变量：审批对象
        wfConfService.saveOrUpdate(wfConf);
        String message = i18nUtil.getMessage("ERR.wf.ProcessCommonButtonsController.operateSuccess");
        reusltObj.setMsg(message);
        return reusltObj;
    }
}
