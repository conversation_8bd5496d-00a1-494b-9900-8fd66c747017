package com.qm.ep.wf.util;

import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class PageUtil<T> {

    public List<T> getPagePageData(int curPageIndex, int lastPageIndex, int pageMaxNum, List<T> migrateDetailDOs) {
        List<T> result = new ArrayList<>();
        int totalNum = migrateDetailDOs.size();
        if (curPageIndex == lastPageIndex)//假如这是最后一页
        {
            result = migrateDetailDOs.subList((curPageIndex - 1) * pageMaxNum, totalNum);
            log.info("---error--"+"---error--第" + curPageIndex + "组，范围是：" + (curPageIndex - 1) * pageMaxNum + "-" + totalNum);
        } else if (curPageIndex > 1 && curPageIndex < lastPageIndex)//假如这既不是第一页也不是最后一页
        {
            result = migrateDetailDOs.subList((curPageIndex - 1) * pageMaxNum, curPageIndex * pageMaxNum);
            log.info("---error--"+"---error--第" + curPageIndex + "组，范围是：" + (curPageIndex - 1) * pageMaxNum + "-" + curPageIndex * pageMaxNum);
        } else if (curPageIndex == 1 && totalNum > pageMaxNum)//假如这是第一页，而且总房间数大于页面最大房间数
        {
            result = migrateDetailDOs.subList(0, pageMaxNum);
            log.info("---error--"+"---error--第" + curPageIndex + "组，范围是：0-" + pageMaxNum);
        } else if (curPageIndex == 1 && totalNum < pageMaxNum)//假如这是第一页，而且总房间数小于页面最大房间数
        {
            result = migrateDetailDOs.subList(0, totalNum);
            log.info("---error--"+"---error--第" + curPageIndex + "组，范围是：0-" + totalNum);
        }
        return result;
    }

}
