package com.qm.ep.wf.service;

import com.qm.ep.wf.common.CommentTypeEnum;
import com.qm.ep.wf.domain.vo.*;
import com.qm.tds.api.domain.JsonResultVo;
import org.flowable.engine.task.Comment;
import org.flowable.task.api.Task;
import org.flowable.task.api.history.HistoricTaskInstance;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> @date
 */
public interface FlowableTaskService {
    /**
     * 查询任务详情
     *
     * @param taskId
     * @return
     */
    TaskResponse getTask(String taskId);

    /**
     * 查询子任务列表
     *
     * @param taskId
     * @return
     */
    List<TaskResponse> getSubTasks(String taskId);

    /**
     * 获取任务节点button
     *
     * @param taskId
     * @return
     */
    List getTaskButtons(String taskId, String processDefinitionKey);

    /**
     * 修改任务
     *
     * @param taskUpdateRequest
     * @return
     */
    TaskResponse updateTask(TaskUpdateRequest taskUpdateRequest);

    /**
     * 转办任务
     *
     * @param taskRequest
     */
    void assignTask(TaskRequest taskRequest);

    /**
     * 获取待办数量
     *
     * @param userID
     * @param relateRoles
     * @return
     */
    Integer getTaskTodoCount(String userID, List<String> relateRoles);

    /**
     * 新增任务参与人
     *
     * @param taskId
     * @param involveUserId
     */
    void involveUser(String taskId, String involveUserId);

    /**
     * 移除任务参与人
     *
     * @param taskId
     * @param involveUserId
     */
    void removeInvolvedUser(String taskId, String involveUserId);

    /**
     * 认领任务
     *
     * @param taskRequest
     */
    void claimTask(TaskRequest taskRequest);

    /**
     * 取消认领
     *
     * @param taskRequest
     */
    void unclaimTask(TaskRequest taskRequest);

    /**
     * 新增任务关联人
     *
     * @param task
     * @param userId
     * @param linkType
     */
    void addIdentiyLinkForUser(Task task, String userId, String linkType);

    /**
     * 委派任务
     *
     * @param taskRequest
     */
    void delegateTask(TaskRequest taskRequest);

    /**
     * 完成任务
     *
     * @param taskRequest
     */
    Map<String, Object> completeTask(TaskRequest taskRequest);

    /**
     * 删除任务
     *
     * @param taskId
     */
    void deleteTask(String taskId);

    /**
     * 终止流程
     *
     * @param taskRequest
     */
    void stopProcessInstance(TaskRequest taskRequest);

    void stopProcessInstance(String processDefId, String processInsId);

    void stopProcessInstanceByExecID(String processDefId, String executionId);

    /**
     * 查询可退回节点
     *
     * @param taskId
     * @return
     */
    List<FlowNodeResponse> getBackNodes(String taskId);

    /**
     * 退回任务
     *
     * @param taskRequest
     */
    void backTask(TaskRequest taskRequest);

    /**
     * 查询单一任务详情
     *
     * @param taskId
     * @return
     */
    Task getTaskNotNull(String taskId);

    /**
     * 查询单一历史任务详情
     *
     * @param taskId
     * @return
     */
    HistoricTaskInstance getHistoricTaskInstanceNotNull(String taskId);

    /**
     * 新增过程意见
     *
     * @param taskId
     * @param processInstanceId
     * @param userId
     * @param type
     * @param message
     */
    Comment addComment(String taskId, String processInstanceId, String userId, CommentTypeEnum type, String message);

    Comment addComment(String taskId, String processInstanceId, String userId, String type, String message);

    /**
     * 查询过程意见
     *
     * @param taskId
     * @param processInstanceId
     * @param type
     * @param userId
     * @return
     */
    List<Comment> getComments(String taskId, String processInstanceId, String type, String userId);

    /**
     * 新增任务关联信息
     *
     * @param taskIdentityRequest
     */
    void saveTaskIdentityLink(IdentityRequest taskIdentityRequest);

    /**
     * 删除任务关联信息
     *
     * @param taskId
     * @param identityId
     * @param identityType
     */
    void deleteTaskIdentityLink(String taskId, String identityId, String identityType);


    /**
     * 定时任务-完成任务
     *
     * @param map
     */
    void completeTaskByQuartz(Map<String, String> map);

    /**
     * 定时任务-终止流程
     *
     * @param map
     */
    void stopProcessInstanceByQuartz(Map<String, String> map);

    /**
     * 定时任务-退回任务
     *
     * @param map
     */
    void backTaskByQuartz(Map<String, String> map);

    /**
     * 获取指定任务的候选人
     */
    Map getTaskAssignment(String processDefKey, String taskID);

    /**
     * 获取任务按钮
     */
    List getTaskButtonsCode(String taskDefKey, String processDefID);

    /**
     * 批量获取历史流程实例
     *
     * @param processDefKey
     * @param businessKeys
     * @return
     */
    List<Map> hisProcInsQueryByBusinessKeys(String processDefKey, List<String> businessKeys);

    @SuppressWarnings("squid:S00107")
    void batchCompleteTask(List<Task> taskList, TaskRequest taskRequest, List<String> msgs, JsonResultVo resultObj,
                           Map<String, Object> requestParams, String customUniqueFlag, List<String> businessKeys,
                           List<Map> businessMsgs, List<Map> hisProcessInstances, Integer failNum);

    boolean hasListener(String processDefinitionKey, String taskDefKey, String processDefKey);


    /**
     * 获取任务按钮
     */
    List getTaskButtonsCodeBy(String taskDefKey, String processDefID);
}
