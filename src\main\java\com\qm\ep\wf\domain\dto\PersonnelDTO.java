package com.qm.ep.wf.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 人员
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-16
 */
@ApiModel(value = "PersonnelDTO对象", description = "人员")
@Data
public class PersonnelDTO extends JsonParamDto {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "主键")
    private String id;
    @ApiModelProperty(value = "人员代码:人员代码")
    private String vpersoncode;
    @ApiModelProperty(value = "本公司标识:标识人员是否属于本公司，如果属于则对应部门，如不属于则为合作伙伴")
    private String vlocalcompanyflag;
    @ApiModelProperty(value = "部门ID:人员所属部门")
    private String ndeptid;
    @ApiModelProperty(value = "登录标识:标识此人是否有登录系统权限")
    private String vlogingflag;
    @ApiModelProperty(value = "登录用户名:系统登录用户名，全局唯一")
    private String vusername;
    @ApiModelProperty(value = "登录口令:登录口令，最少6位")
    private String vpwd;
    @ApiModelProperty(value = "性别:性别 1男 2女")
    private String vsex;
    @ApiModelProperty(value = "参加工作时间:人员参加工作时间")
    private Date dstarworkdate;
    @ApiModelProperty(value = "岗位:人员所属岗位，适用于本公司人员")
    private String vstation;
    @ApiModelProperty(value = "生日:人员的生日")
    private Date dbirthday;
    @ApiModelProperty(value = "身份证号:人员的身份证号")
    private String vidcard;
    @ApiModelProperty(value = "地址:客户所在地址")
    private String vaddress;
    @ApiModelProperty(value = "联系电话:客户的联系电话，固定电话")
    private String vphone;
    @ApiModelProperty(value = "移动电话:客户联系手机号码")
    private String vmobile;
    @ApiModelProperty(value = "传真:客户的传真号码")
    private String vfaxno;
    @ApiModelProperty(value = "邮政编码:客户的邮政编码")
    private String vpostno;
    @ApiModelProperty(value = "电子邮件:客户的电子邮件地址")
    private String vemail;
    @ApiModelProperty(value = "网址:客户的网址")
    private String vurl;
    @ApiModelProperty(value = "国家和地区:国家")
    private String vcountrycode;
    @ApiModelProperty(value = "省区:省区")
    private String nprovince;
    @ApiModelProperty(value = "市县:市县")
    private String ncity;
    @ApiModelProperty(value = "备注:备注")
    private String vremark;
    @ApiModelProperty(value = "使用证书:标识此人登录系统是否使用证书，默认不使用")
    private String vusecertificate;
    @ApiModelProperty(value = "证书号:签名证书编号")
    private String vcertificateno;
    @ApiModelProperty(value = "公司ID:公司ID")
    private String ncompanyid;
    @ApiModelProperty(value = "停用标识:停止使用标识")
    private String vstop;
    @ApiModelProperty(value = "停用日期:停止使用的日期")
    private Date dstop;
    @ApiModelProperty(value = "停用人ID")
    private String nstopperson;
    @ApiModelProperty(value = "时间戳: ")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Timestamp dtstamp;
    @ApiModelProperty(value = "证书序号(管理人员自己编码)")
    private String vcertificatereq;
    @ApiModelProperty(value = "离职日期")
    private Date dleavedate;
    @ApiModelProperty(value = "民族代码")
    private String vnationality;
    @ApiModelProperty(value = "职务代码，关联数据字典DUTY")
    private String vduty;
    @ApiModelProperty(value = "职称代码")
    private String vtechpost;
    @ApiModelProperty(value = "职工编号")
    private String vworkerno;
    @ApiModelProperty(value = "加入公司日期")
    private Date dhiredate;
    @ApiModelProperty(value = "文化程度代码")
    private String vdegree;
    @ApiModelProperty(value = "职员类别代码")
    private String vempgroup;
    @ApiModelProperty(value = "县")
    private String ncountry;
    @ApiModelProperty(value = "用户选择的语言代码")
    private String vcurrentlanguagecode;
    @ApiModelProperty(value = "客户组ID")
    private String ncustgroupid;
    @ApiModelProperty(value = "dms的终端模式，数据字典TERMINAL（0：PC端；1：手机端）(接收时默认为0 'PC')")
    private String vmflag;
    @ApiModelProperty(value = "起初导入时，对应V1版本的ID")
    private String ncrmv1id;
    @ApiModelProperty(value = "APP手势密码")
    private String vgesturepwd;
    @ApiModelProperty(value = "上传头像地理位置经度")
    private String nlon;
    @ApiModelProperty(value = "上传头像地理位置纬度")
    private String nlat;
    @ApiModelProperty(value = "移动设备唯一标识")
    private String vimei;
    @ApiModelProperty(value = "手机串号绑定标识")
    private String vimeiident;
    @ApiModelProperty(value = "移动设备排除标识")
    private String vimeiexclude;
    @ApiModelProperty(value = "移动设备系统信息")
    private String vmobileinfo;
    @ApiModelProperty(value = "移动设备系统类型")
    private String vdeviceos;
    @ApiModelProperty(value = "推送ClientID")
    private String vpushclientid;
    @ApiModelProperty(value = "小能工号")
    private String vxnworkno;
    @ApiModelProperty(value = "限制登录标识")
    private String vloginlimit;
    @ApiModelProperty(value = "安全级别")
    private String vsafelevel;
    @ApiModelProperty(value = "证件类型")
    private String vdoctype;
    @ApiModelProperty(value = "微信号")
    private String vweixin;
    @ApiModelProperty(value = "钉钉号")
    private String vdingding;
    @ApiModelProperty(value = "建档日期")
    private Date dcreate;
    @ApiModelProperty(value = "建档人员")
    private String ncreater;
    @ApiModelProperty(value = "锁定日期")
    private Date dloginlimit;
    @ApiModelProperty(value = "锁定原因")
    private String vlimitreason;
    @ApiModelProperty(value = "停用原因")
    private String vstopreason;
    @ApiModelProperty(value = "组织类型")
    private String vorgtype;
    @ApiModelProperty(value = "单位")
    private String vinstcode;
    @ApiModelProperty(value = "人员名称")
    private String vrealname;
    @ApiModelProperty(value = "部门id数组")
    private List<String> nparentDeptId;
    @ApiModelProperty(value = "人员代码集合")
    private List<String> vpersoncodes;
    @ApiModelProperty(value = "单位代码")
    private String vorgcode;
    @ApiModelProperty(value = "钉钉组织代码")
    private String vddorgcode;
    @ApiModelProperty(value = "角色id")
    private String nroleid;
    @ApiModelProperty(value = "默认语言")
    private String getVcurrentlanguagecode;

    /**
     * 人员IDs
     */
    private List<String> ids;

}